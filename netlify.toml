[build]
  command = "npm run build:prod"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  SECRETS_SCAN_OMIT_PATHS = "docs/,*.md,.env.example"
  VITE_SUPABASE_URL = "https://fxqoowlruissctsgbljk.supabase.co"
  VITE_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk"
  NEXT_PUBLIC_SUPABASE_URL = "https://fxqoowlruissctsgbljk.supabase.co"
  NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk"
  VITE_CLERK_PUBLISHABLE_KEY = "pk_live_Y2xlcmsuaG91c2Vnb2luZy5jb20uYXUk"
  VITE_CLERK_DOMAIN = "clerk.housegoing.com.au"
  VITE_STRIPE_PUBLISHABLE_KEY = "pk_test_51RNPjZGGj0zoACi7E0rkbGUlMsiMPmkiODVpKJBroPsdLEzZf95WJ2rSUz9GdixosG1C9XTQquTcF1r3rZigcr3q00bteGkuZy"
  VITE_BACKEND_URL = "https://housegoing.onrender.com"

# Function settings
[functions]
  node_bundler = "esbuild"

# Handle specific static pages first
[[redirects]]
  from = "/sign-up"
  to = "/sign-up.html"
  status = 200

[[redirects]]
  from = "/sign-in"
  to = "/sign-in.html"
  status = 200

# Handle client-side routing for React app
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# NSW Party Planning Tool redirects
[[redirects]]
  from = "/nsw-curfew-zoning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning-updated"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-address-v2"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-address"
  to = "/index.html"
  status = 200
  force = true

# API redirects for Stripe functions
[[redirects]]
  from = "/api/stripe/payment-intent"
  to = "/.netlify/functions/create-payment-intent"
  status = 200

[[redirects]]
  from = "/api/stripe/create-customer"
  to = "/.netlify/functions/create-customer"
  status = 200

[[redirects]]
  from = "/api/stripe/webhook"
  to = "/.netlify/functions/stripe-webhook"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Headers for API endpoints
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type, Authorization, Stripe-Signature"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
