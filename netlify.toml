[build]
  command = "npm run build:prod"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  SECRETS_SCAN_OMIT_PATHS = "docs/,*.md,.env.example"

# Function settings
[functions]
  node_bundler = "esbuild"

# Handle specific static pages first
[[redirects]]
  from = "/sign-up"
  to = "/sign-up.html"
  status = 200

[[redirects]]
  from = "/sign-in"
  to = "/sign-in.html"
  status = 200

# Handle client-side routing for React app
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# NSW Party Planning Tool redirects
[[redirects]]
  from = "/nsw-curfew-zoning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning-updated"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-address-v2"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-address"
  to = "/index.html"
  status = 200
  force = true

# API redirects for Stripe functions
[[redirects]]
  from = "/api/stripe/payment-intent"
  to = "/.netlify/functions/create-payment-intent"
  status = 200

[[redirects]]
  from = "/api/stripe/create-customer"
  to = "/.netlify/functions/create-customer"
  status = 200

[[redirects]]
  from = "/api/stripe/webhook"
  to = "/.netlify/functions/stripe-webhook"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Headers for API endpoints
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type, Authorization, Stripe-Signature"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
