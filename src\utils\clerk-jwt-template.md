# Clerk JWT Template for Supa<PERSON>

To properly configure <PERSON> to work with Supa<PERSON>, you need to set up a JWT template in the Clerk Dashboard.

## Steps to Configure the JWT Template

1. Go to the [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Select your application
3. Navigate to "JWT Templates" in the sidebar
4. Click on the "supabase" template (or create a new one named "supabase" if it doesn't exist)
5. Replace the template with the following JSON:

```json
{
  "aud": "authenticated",
  "role": "authenticated",
  "email": "{{user.primary_email_address}}",
  "app_metadata": {
    "provider": "clerk"
  },
  "user_metadata": {
    "id": "{{user.id}}",
    "first_name": "{{user.first_name}}",
    "last_name": "{{user.last_name}}",
    "email": "{{user.primary_email_address}}",
    "image_url": "{{user.image_url}}",
    "clerk_id": "{{user.id}}",
    "created_at": "{{user.created_at}}",
    "updated_at": "{{user.updated_at}}"
  },
  "exp": "{{jwt.exp}}"
}
```

6. Click "Save" to update the template

## Why This Template Works

This template includes:

- `aud`: Set to "authenticated" which is what Supabase expects
- `role`: Set to "authenticated" to give the user the authenticated role in Supabase
- `email`: The user's primary email address
- `app_metadata`: Additional metadata about the authentication
- `user_metadata`: User profile information that will be available in Supabase
- `exp`: The expiration time for the JWT

## Testing the Integration

After setting up the template, you can test the integration by:

1. Signing in with Clerk
2. Using the browser console to check if the token is being properly retrieved
3. Verifying that Supabase operations work correctly after authentication

## Troubleshooting

If you're still having issues:

1. Make sure the JWT template is saved correctly in Clerk
2. Check that you're using the correct template name when requesting the token (`supabase`)
3. Verify that the token is being properly set on the Supabase client
4. Look for any errors in the browser console related to JWT validation
