import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { SessionProvider } from 'next-auth/react';
import { AuthProvider } from './providers/AuthProvider';
import './styles/fonts.css';
// Removed ClerkProvider import
import Header from './components/Header';
import Footer from './components/layout/Footer';
import ChatWidget from './components/chat/ChatWidget';
// DevAuthPanel removed for production
import ErrorBoundary from './components/ErrorBoundary';
import HomePage from './pages/HomePage';
import FindVenues from './pages/FindVenues';
import ListSpace from './pages/ListSpace';
import HowItWorks from './pages/HowItWorks';
import VenueDetail from './pages/VenueDetail';
import MyAccount from './pages/MyAccount';
import UserProfile from './pages/UserProfile';
import UserSettings from './pages/UserSettings';
import MessagesPage from './pages/Messages';
import SalesAssistant from './pages/SalesAssistant';
import BecomeHost from './pages/BecomeHost';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import ContactPage from './pages/ContactPage';
import HelpPage from './pages/HelpPage';
import SafetyPage from './pages/SafetyPage';
import BlogPage from './pages/blog/BlogPage';
import LocationPage from './pages/locations/LocationPage';
import FAQPage from './pages/FAQPage';
import TermsAndConditions from './pages/TermsAndConditions';
import ProtectedRoute from './routes/ProtectedRoute';
import HostProtectedRoute from './routes/HostProtectedRoute';
import AdminProtectedRoute from './routes/AdminProtectedRoute';
import SellerPortal from './pages/host/SellerPortal';
import OwnerPortal from './pages/host/OwnerPortal';
import OwnerLanding from './pages/host/OwnerLanding';
import HostDashboard from './pages/host/Dashboard';
import Properties from './pages/host/Properties';
import PropertyForm from './pages/host/PropertyForm';

import Bookings from './pages/host/Bookings';
import Messages from './pages/host/Messages';
import Earnings from './pages/host/Earnings';
import Settings from './pages/host/Settings';
import Help from './pages/host/Help';
import Unauthorized from './pages/Unauthorized';
import HostUnauthorized from './pages/host/Unauthorized';
import AdminDashboard from './pages/admin/Dashboard'; // Updated with AI chat management
import AnalyticsDashboard from './pages/admin/Analytics';
import AdminProperties from './pages/admin/Properties'; // Admin Properties page for approvals
import AdminPropertyDetail from './pages/admin/AdminPropertyDetail'; // Admin Property Detail page
import NotFound from './pages/NotFound';
import OwnerPortalLayout from './layouts/OwnerPortalLayout';
import AITraining from './pages/admin/AITraining';
import SiteSettings from './pages/admin/SiteSettings';
import UserManagement from './pages/admin/UserManagement';
import ErrorLogs from './pages/admin/ErrorLogs';
import BookingConfirmation from './pages/BookingConfirmation';
import BookingReview from './pages/BookingReview';
import MyBookings from './pages/MyBookings';
import NSWPartyPlanningToolPage from './pages/NSWPartyPlanningToolPage';
import NSWCurfewZoningPage from './pages/NSWCurfewZoningPage';


import NSWNoiseGuide from './pages/NSWNoiseGuide';
import PartyPlanningGuide from './pages/PartyPlanningGuide';
import VenueGuideHub from './pages/VenueGuideHub';
import SitemapPage from './pages/SitemapPage';
import TestAuth from './pages/test-auth';
import SupabaseTest from './pages/supabase-test';
import TestNextAuth from './pages/test-nextauth';
// Test imports removed for production
import { AdminRoute } from './hooks/useAdminAuth.tsx';
import AdminApprovalDashboard from './pages/admin/AdminApprovalDashboard';
// Admin test imports removed for production
import AdminSubmissionDetail from './pages/admin/AdminSubmissionDetail';
// AdminReminderTest removed for production
import OwnerDashboard from './pages/owner/OwnerDashboard';
import EditProperty from './pages/owner/EditProperty';
import ManageProperty from './pages/owner/ManageProperty';

import { initializeApp } from './services/init';

// Auth pages
import ResetPassword from './pages/auth/ResetPassword';
import VerifyEmail from './pages/auth/VerifyEmail';

// Component to conditionally render footer
function ConditionalFooter() {
  const location = useLocation();

  // Don't show footer on admin, host portal, or owner portal pages
  const hideFooterPaths = ['/admin', '/host', '/owner'];
  const shouldHideFooter = hideFooterPaths.some(path => location.pathname.startsWith(path));

  return shouldHideFooter ? null : <Footer />;
}

function App() {
  // Initialize the application when it starts
  useEffect(() => {
    initializeApp();

    // NextAuth.js handles authentication automatically
    console.log("✅ NextAuth.js authentication initialized");
  }, []);

  return (
    <ErrorBoundary>
      <SessionProvider>
        <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
          <AuthProvider>
          <div className="min-h-screen bg-gray-50">
            <Header />
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/find-venues" element={<FindVenues />} />
              <Route path="/venue-search-results" element={<Navigate to="/find-venues" replace />} />
              <Route path="/venues/:id" element={<VenueDetail />} />
              <Route path="/venue/:id" element={<VenueDetail />} />
              <Route path="/enhanced-booking/:id" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/EnhancedBooking')))}
              </React.Suspense>} />
              <Route path="/list-space" element={<ListSpace />} />
              <Route path="/how-it-works" element={<HowItWorks />} />
              <Route path="/sales-assistant" element={<SalesAssistant />} />
              <Route path="/venue-assistant" element={
                <React.Suspense fallback={<div className="flex items-center justify-center min-h-screen">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                </div>}>
                  {React.createElement(React.lazy(() => import('./pages/VenueAssistant')))}
                </React.Suspense>
              } />
              <Route path="/party-planning-guide" element={<PartyPlanningGuide />} />
              <Route path="/venue-guide" element={<VenueGuideHub />} />
              <Route path="/nsw-party-planning" element={<NSWPartyPlanningToolPage />} />
              <Route path="/nsw-curfew-zoning" element={<Navigate to="/nsw-party-planning" replace />} />

              <Route path="/nsw-noise-guide" element={<NSWNoiseGuide />} />
              {/* Test routes removed for production */}
              <Route path="/nsw-party-planning-updated" element={
                <React.Suspense fallback={<div className="flex items-center justify-center min-h-screen">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                </div>}>
                  {React.createElement(React.lazy(() => import('./pages/NSWPartyPlanningUpdated')))}
                </React.Suspense>
              } />
              <Route path="/nsw-address-v2" element={<NSWPartyPlanningToolPage />} />
              <Route path="/precise-party-planning" element={<NSWPartyPlanningToolPage />} />
              <Route path="/precise-address" element={<NSWPartyPlanningToolPage />} />
              {/* Test routes removed for production */}
              <Route path="/login" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/auth/Login')))}
              </React.Suspense>} />
              <Route path="/signup" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/auth/Signup')))}
              </React.Suspense>} />
              {/* Redirect common URL variations */}
              <Route path="/sign-in" element={<Navigate to="/login" replace />} />
              <Route path="/sign-up" element={<Navigate to="/signup" replace />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/verify-email" element={<VerifyEmail />} />
              <Route path="/host/login" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/auth/Login')))}
              </React.Suspense>} />
              <Route path="/host/signup" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/auth/HostSignup')))}
              </React.Suspense>} />
              <Route path="/auth/callback" element={<React.Suspense fallback={<div className="flex items-center justify-center min-h-screen">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                </div>}>
                {React.createElement(React.lazy(() => import('./pages/auth/AuthCallback')))}
              </React.Suspense>} />
              <Route
                path="/my-account"
                element={
                  <ProtectedRoute>
                    <MyAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/account"
                element={
                  <ProtectedRoute>
                    <UserProfile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <UserSettings />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/messages"
                element={
                  <ProtectedRoute>
                    <MessagesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/my-bookings"
                element={
                  <ProtectedRoute>
                    <MyBookings />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/booking-confirmation/:id"
                element={
                  <ProtectedRoute>
                    <BookingConfirmation />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/booking-review"
                element={
                  <ProtectedRoute>
                    <BookingReview />
                  </ProtectedRoute>
                }
              />
              <Route path="/host/portal" element={<OwnerLanding />} />
              <Route path="/host/dashboard-portal" element={<ProtectedRoute redirectTo="/host/login">
                <OwnerPortal />
              </ProtectedRoute>} />
              <Route
                path="/host/onboarding"
                element={
                  <HostProtectedRoute>
                    <SellerPortal />
                  </HostProtectedRoute>
                }
              />

              {/* Owner Portal Routes */}
              <Route
                path="/host"
                element={
                  <HostProtectedRoute>
                    <OwnerPortalLayout />
                  </HostProtectedRoute>
                }
              >
                <Route path="dashboard" element={<HostDashboard />} />
                <Route path="properties" element={<Properties />} />
                <Route path="properties/new" element={
                  <React.Suspense fallback={<div className="p-8 text-center">Loading form...</div>}>
                    {React.createElement(React.lazy(() => import('./pages/host/EnhancedPropertyForm')))}
                  </React.Suspense>
                } />
                <Route path="properties/new-basic" element={<PropertyForm />} />
                <Route path="properties/:id/edit" element={<PropertyForm />} />
                <Route path="availability" element={
                  <React.Suspense fallback={<div className="p-8 text-center">Loading availability management...</div>}>
                    {React.createElement(React.lazy(() => import('./pages/host/AvailabilityManagement')))}
                  </React.Suspense>
                } />
                <Route path="bookings" element={<Bookings />} />
                <Route path="messages" element={
                  <React.Suspense fallback={<div className="p-8 text-center">Loading messages...</div>}>
                    {React.createElement(React.lazy(() => import('./pages/host/MessagesSimple')))}
                  </React.Suspense>
                } />
                <Route path="ai-assistant" element={
                  <React.Suspense fallback={<div className="p-8 text-center">Loading AI Assistant...</div>}>
                    {React.createElement(React.lazy(() => import('./pages/host/ai-assistant')))}
                  </React.Suspense>
                } />
                <Route path="earnings" element={<Earnings />} />
                <Route path="settings" element={<Settings />} />
                <Route path="help" element={<Help />} />
              </Route>
              <Route path="/host/unauthorized" element={<HostUnauthorized />} />
              <Route path="/unauthorized" element={<Unauthorized />} />
              <Route path="/blog" element={<BlogPage />} />
              <Route path="/faq" element={<FAQPage />} />
              <Route path="/locations/:location" element={<LocationPage />} />
              <Route path="/locations/:location/:sublocation" element={<LocationPage />} />

              {/* Category, Event Type, and Venue Type Pages */}
              <Route path="/categories/:category" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/categories/CategoryPage')))}
              </React.Suspense>} />
              <Route path="/event-types/:eventType" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/event-types/EventTypePage')))}
              </React.Suspense>} />
              <Route path="/venue-types/:venueType" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/venue-types/VenueTypePage')))}
              </React.Suspense>} />

              {/* Gallery Pages */}
              <Route path="/gallery" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/gallery/GalleryPage')))}
              </React.Suspense>} />
              <Route path="/gallery/:galleryType" element={<React.Suspense fallback={<div>Loading...</div>}>
                {React.createElement(React.lazy(() => import('./pages/gallery/GalleryPage')))}
              </React.Suspense>} />
              <Route path="/terms" element={<TermsAndConditions />} />
              <Route path="/terms-old" element={<Terms />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/help" element={<HelpPage />} />
              <Route path="/safety" element={<SafetyPage />} />
              <Route path="/sitemap" element={<SitemapPage />} />
              <Route path="/test-auth" element={<TestAuth />} />
              <Route path="/supabase-test" element={<SupabaseTest />} />
              <Route path="/test-nextauth" element={<TestNextAuth />} />
              <Route
                path="/admin"
                element={
                  <AdminProtectedRoute>
                    <AdminDashboard />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/dashboard"
                element={
                  <AdminProtectedRoute>
                    <AdminDashboard />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/analytics"
                element={
                  <AdminProtectedRoute>
                    <AnalyticsDashboard />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/ai-training"
                element={
                  <AdminProtectedRoute>
                    <AITraining />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/settings"
                element={
                  <AdminProtectedRoute>
                    <SiteSettings />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/users"
                element={
                  <AdminProtectedRoute>
                    <UserManagement />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/approval"
                element={
                  <AdminRoute>
                    <AdminApprovalDashboard />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/properties"
                element={
                  <AdminProtectedRoute>
                    <AdminProperties />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/properties/:id"
                element={
                  <AdminProtectedRoute>
                    <AdminPropertyDetail />
                  </AdminProtectedRoute>
                }
              />
              <Route
                path="/admin/submissions/:id"
                element={
                  <AdminRoute>
                    <AdminSubmissionDetail />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/logs"
                element={
                  <AdminProtectedRoute>
                    <ErrorLogs />
                  </AdminProtectedRoute>
                }
              />
              {/* Admin test routes removed for production */}
              {/* Admin test routes removed for production */}

              {/* Owner Portal Routes */}
              <Route path="/owner/dashboard" element={<OwnerDashboard />} />
              <Route path="/owner/property/:propertyId/edit" element={<EditProperty />} />
              <Route path="/owner/property/:propertyId/manage" element={<ManageProperty />} />
              {/* Add fallback routes for authentication redirects */}
              <Route path="/auth/redirect" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/undefined" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/null" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/sso-callback" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/callback" element={<Navigate to="/auth/callback" replace />} />

              {/* Add comprehensive routes for Google OAuth */}
              <Route path="/google-oauth" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/google-callback" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/oauth/google/callback" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/auth/google/callback" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/api/auth/callback/google" element={<Navigate to="/auth/callback" replace />} />
              <Route path="/callback/google" element={<Navigate to="/auth/callback" replace />} />

              {/* Removed /become-host route in favor of direct signup through /host/signup */}
              {/* Catch-all route for 404 pages */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            <ConditionalFooter />
            <ChatWidget />
            {/* DevAuthPanel removed for production */}
          </div>
        </AuthProvider>
      </Router>
    </SessionProvider>
    </ErrorBoundary>
  );
}

export default App;