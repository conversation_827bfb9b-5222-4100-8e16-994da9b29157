import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';

// Mock data for analytics
const mockData = {
  signUps: [
    { date: '2023-06-01', count: 12 },
    { date: '2023-06-02', count: 8 },
    { date: '2023-06-03', count: 15 },
    { date: '2023-06-04', count: 10 },
    { date: '2023-06-05', count: 20 },
    { date: '2023-06-06', count: 18 },
    { date: '2023-06-07', count: 25 },
  ],
  signIns: [
    { date: '2023-06-01', count: 45 },
    { date: '2023-06-02', count: 38 },
    { date: '2023-06-03', count: 52 },
    { date: '2023-06-04', count: 41 },
    { date: '2023-06-05', count: 60 },
    { date: '2023-06-06', count: 55 },
    { date: '2023-06-07', count: 68 },
  ],
  authMethods: [
    { method: 'Email/Password', count: 120 },
    { method: 'Google', count: 85 },
    { method: 'Facebook', count: 45 },
    { method: 'Apple', count: 30 },
  ],
  userRetention: {
    day1: 95,
    day7: 80,
    day30: 65,
    day90: 45,
  },
};

export default function AnalyticsDashboard() {
  const { user, isLoaded } = useUser();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('7d');

  if (!isLoaded) {
    return <div className="pt-32 flex justify-center">Loading...</div>;
  }

  if (!user) {
    return <div className="pt-32 flex justify-center">Please sign in to access analytics</div>;
  }

  // DIRECT OVERRIDE: List of admin emails
  const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

  // Get user email
  const userEmail = user.primaryEmailAddress?.emailAddress || '';

  // Simple check: user is admin if email is in the list or we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAdmin = isDevelopment || ADMIN_EMAILS.includes(userEmail.toLowerCase());

  // Log for debugging
  console.log('Analytics admin check:', { email: userEmail, isDevelopment, isAdmin });

  if (!isAdmin) {
    return (
      <div className="pt-32 px-4 flex flex-col items-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-8">
          You don't have permission to access the analytics dashboard.
          Your email: <span className="font-mono">{userEmail}</span>
        </p>
        <a href="/" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
          Return to Home
        </a>
      </div>
    );
  }

  // Calculate total sign-ups and sign-ins
  const totalSignUps = mockData.signUps.reduce((sum, day) => sum + day.count, 0);
  const totalSignIns = mockData.signIns.reduce((sum, day) => sum + day.count, 0);
  const totalUsers = totalSignUps;
  const activeUsers = Math.round(totalUsers * 0.75); // Mock data: 75% of users are active

  return (
    <div className="pt-32 px-4 max-w-7xl mx-auto">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            className={`px-3 py-1 rounded-md ${
              timeRange === '7d'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setTimeRange('7d')}
          >
            Last 7 days
          </button>
          <button
            className={`px-3 py-1 rounded-md ${
              timeRange === '30d'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setTimeRange('30d')}
          >
            Last 30 days
          </button>
          <button
            className={`px-3 py-1 rounded-md ${
              timeRange === '90d'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setTimeRange('90d')}
          >
            Last 90 days
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Users</h3>
          <p className="text-3xl font-bold">{totalUsers}</p>
          <p className="text-sm text-green-600 mt-2">+{mockData.signUps[6].count} today</p>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Active Users</h3>
          <p className="text-3xl font-bold">{activeUsers}</p>
          <p className="text-sm text-green-600 mt-2">+{mockData.signIns[6].count} today</p>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Sign-ups (Last 7 days)</h3>
          <p className="text-3xl font-bold">{totalSignUps}</p>
          <p className="text-sm text-green-600 mt-2">+15% vs previous period</p>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Sign-ins (Last 7 days)</h3>
          <p className="text-3xl font-bold">{totalSignIns}</p>
          <p className="text-sm text-green-600 mt-2">+8% vs previous period</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Sign-ups Over Time</h2>
          <div className="h-64 flex items-end space-x-2">
            {mockData.signUps.map((day, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-purple-500 rounded-t"
                  style={{ height: `${(day.count / 25) * 100}%` }}
                ></div>
                <p className="text-xs text-gray-500 mt-2">{day.date.split('-')[2]}</p>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Sign-ins Over Time</h2>
          <div className="h-64 flex items-end space-x-2">
            {mockData.signIns.map((day, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-blue-500 rounded-t"
                  style={{ height: `${(day.count / 70) * 100}%` }}
                ></div>
                <p className="text-xs text-gray-500 mt-2">{day.date.split('-')[2]}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Authentication Methods */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Authentication Methods</h2>
          <div className="space-y-4">
            {mockData.authMethods.map((method, index) => (
              <div key={index}>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">{method.method}</span>
                  <span className="text-sm text-gray-500">
                    {Math.round((method.count / 280) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-purple-600 h-2.5 rounded-full"
                    style={{ width: `${(method.count / 280) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">User Retention</h2>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">Day 1</span>
                <span className="text-sm text-gray-500">{mockData.userRetention.day1}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-green-500 h-2.5 rounded-full"
                  style={{ width: `${mockData.userRetention.day1}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">Day 7</span>
                <span className="text-sm text-gray-500">{mockData.userRetention.day7}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-green-500 h-2.5 rounded-full"
                  style={{ width: `${mockData.userRetention.day7}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">Day 30</span>
                <span className="text-sm text-gray-500">{mockData.userRetention.day30}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-green-500 h-2.5 rounded-full"
                  style={{ width: `${mockData.userRetention.day30}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">Day 90</span>
                <span className="text-sm text-gray-500">{mockData.userRetention.day90}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-green-500 h-2.5 rounded-full"
                  style={{ width: `${mockData.userRetention.day90}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Export Section */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Export Data</h2>
        <p className="text-gray-600 mb-4">
          Download analytics data for further analysis or reporting.
        </p>
        <div className="flex flex-wrap gap-4">
          <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
            Export as CSV
          </button>
          <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
            Export as JSON
          </button>
          <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md">
            Schedule Reports
          </button>
        </div>
      </div>
    </div>
  );
}
