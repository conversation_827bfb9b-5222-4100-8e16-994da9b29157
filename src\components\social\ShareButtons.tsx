import React, { useState } from 'react';
import { 
  Facebook, 
  Twitter, 
  Linkedin, 
  Mail, 
  Link as LinkIcon, 
  Check,
  X
} from 'lucide-react';

interface ShareButtonsProps {
  url: string;
  title: string;
  description?: string;
  image?: string;
  hashtags?: string[];
  className?: string;
  showLabel?: boolean;
}

export const ShareButtons: React.FC<ShareButtonsProps> = ({
  url,
  title,
  description = '',
  image = '',
  hashtags = [],
  className = '',
  showLabel = false
}) => {
  const [copied, setCopied] = useState(false);
  const [showToast, setShowToast] = useState(false);
  
  // Ensure URL is absolute
  const absoluteUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;
  
  // Prepare social media share URLs
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(absoluteUrl)}`;
  
  const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(absoluteUrl)}&text=${encodeURIComponent(title)}${hashtags.length > 0 ? `&hashtags=${hashtags.join(',')}` : ''}`;
  
  const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(absoluteUrl)}`;
  
  const mailtoUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(`Check out this venue: ${absoluteUrl}`)}`;
  
  // Handle copy to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(absoluteUrl).then(() => {
      setCopied(true);
      setShowToast(true);
      
      setTimeout(() => {
        setCopied(false);
      }, 2000);
      
      setTimeout(() => {
        setShowToast(false);
      }, 3000);
    });
  };
  
  // Handle social media share
  const handleShare = (e: React.MouseEvent<HTMLAnchorElement>, url: string) => {
    e.preventDefault();
    window.open(url, '_blank', 'width=600,height=400');
  };
  
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Facebook */}
      <a
        href={facebookUrl}
        onClick={(e) => handleShare(e, facebookUrl)}
        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors"
        aria-label="Share on Facebook"
        title="Share on Facebook"
      >
        <Facebook size={16} />
      </a>
      
      {/* Twitter */}
      <a
        href={twitterUrl}
        onClick={(e) => handleShare(e, twitterUrl)}
        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-sky-500 text-white hover:bg-sky-600 transition-colors"
        aria-label="Share on Twitter"
        title="Share on Twitter"
      >
        <Twitter size={16} />
      </a>
      
      {/* LinkedIn */}
      <a
        href={linkedinUrl}
        onClick={(e) => handleShare(e, linkedinUrl)}
        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-700 text-white hover:bg-blue-800 transition-colors"
        aria-label="Share on LinkedIn"
        title="Share on LinkedIn"
      >
        <Linkedin size={16} />
      </a>
      
      {/* Email */}
      <a
        href={mailtoUrl}
        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-600 text-white hover:bg-gray-700 transition-colors"
        aria-label="Share via Email"
        title="Share via Email"
      >
        <Mail size={16} />
      </a>
      
      {/* Copy Link */}
      <button
        onClick={copyToClipboard}
        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-600 text-white hover:bg-purple-700 transition-colors"
        aria-label="Copy Link"
        title="Copy Link"
      >
        {copied ? <Check size={16} /> : <LinkIcon size={16} />}
      </button>
      
      {/* Label */}
      {showLabel && (
        <span className="text-sm text-gray-600">Share</span>
      )}
      
      {/* Toast notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg flex items-center z-50">
          <Check size={16} className="mr-2 text-green-400" />
          <span>Link copied to clipboard!</span>
          <button 
            onClick={() => setShowToast(false)}
            className="ml-2 text-gray-400 hover:text-white"
          >
            <X size={16} />
          </button>
        </div>
      )}
    </div>
  );
};
