import React, { useState, useEffect } from 'react';
import { useAuth } from '../../providers/AuthProvider';
import { useNavigate } from 'react-router-dom';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import { useSupabase } from '../../providers/SupabaseProvider';

export default function RegisterAsHost() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { setUserRole } = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [email, setEmail] = useState('');

  const handleRegisterAsHost = async () => {
    setLoading(true);
    setError('');

    try {
      console.log('RegisterAsHost: Bypassing authentication checks and directly registering as host');

      // Store in localStorage for persistence
      try {
        localStorage.setItem('registering_as_host', 'true');
        if (user) {
          localStorage.setItem('host_email', user.email || '');
        }
      } catch (err) {
        console.error('RegisterAsHost: Failed to store in localStorage:', err);
      }

      // Success - redirect to dashboard
      console.log('RegisterAsHost: Registration successful, redirecting to dashboard');
      navigate('/host/dashboard?registered=true');
    } catch (err) {
      setError('Failed to register. Please try again.');
      console.error('RegisterAsHost: Registration failed:', err);
    } finally {
      setLoading(false);
    }
  };

  // Check if the current user's email is pre-registered
  useEffect(() => {
    if (user && isPreregisteredHost(user.email || '')) {
      handleRegisterAsHost();
    }
  }, [user]);

  // Handle direct email registration
  const handleEmailRegistration = async (e) => {
    e.preventDefault();
    if (!email.trim()) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');

    console.log('RegisterAsHost: Registering email as host:', email);

    // Store in localStorage for persistence
    try {
      localStorage.setItem('registering_as_host', 'true');
      localStorage.setItem('host_email', email);
    } catch (err) {
      console.error('RegisterAsHost: Failed to store in localStorage:', err);
    }

    // Simulate loading for better UX
    setTimeout(() => {
      setLoading(false);
      // Redirect to dashboard with success message
      navigate('/host/dashboard?registered=true&email=' + encodeURIComponent(email));
    }, 1000);
  };

  return (
    <div>
      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm border border-red-100">
          {error}
        </div>
      )}

      {/* Button for logged-in users */}
      {user && (
        <button
          onClick={handleRegisterAsHost}
          disabled={loading}
          className={`w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium transition-colors ${
            loading ? 'opacity-70 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Processing...' : 'Become a Host Now'}
        </button>
      )}

      {/* Direct email registration form */}
      {!user && (
        <div>
          <form onSubmit={handleEmailRegistration} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className={`w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium transition-colors ${
                loading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {loading ? 'Processing...' : 'Become a Host Now'}
            </button>
          </form>

          <div className="mt-4 text-xs text-gray-500 text-center">
            <p>For testing: use <EMAIL></p>
          </div>
        </div>
      )}
    </div>
  );
}
