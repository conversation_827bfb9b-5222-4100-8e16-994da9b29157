import React, { useState, useEffect } from 'react';
import SearchBar from '../SearchBar';
import HomeSmartSearch from './HomeSmartSearch';
import { Shield, Star, Clock, ArrowRight } from 'lucide-react';

function HeroSectionComponent() {
  // Emotionally-engaging subheadline options for A/B testing
  const subheadlineOptions = [
    // Option A: Exclusivity + Social Proof
    "Unlock exclusive, verified spaces made for unforgettable celebrations with your crew.",

    // Option B: Bragging Rights + Memory Making
    "Book one-of-a-kind party venues your friends will talk about for years.",

    // Option C: Main Event + Standout Factor
    "Discover hand-picked venues where your celebration becomes the main event—not just another night out.",

    // Option D: VIP Access + Unforgettable
    "Access private, verified party spaces—so your event stands out and your guests never forget it.",

    // Option E: Legendary + Iconic Vibes
    "Make your next gathering legendary—book verified venues with iconic vibes and private access.",

    // Option F: Hidden Gems + Insider Access
    "Find hidden gem venues that turn your party into the story everyone's still talking about.",

    // Option G: VIP Treatment + Exclusive Access
    "Get VIP access to exclusive party venues that make every celebration feel like the event of the year."
  ];

  // Simple A/B testing - rotate based on day of month for consistent user experience
  // To test a specific variant, replace the useEffect logic with: setCurrentSubheadline(subheadlineOptions[X])
  const [currentSubheadline, setCurrentSubheadline] = useState('');

  useEffect(() => {
    const today = new Date().getDate();
    const optionIndex = today % subheadlineOptions.length;
    const selectedOption = subheadlineOptions[optionIndex];
    setCurrentSubheadline(selectedOption);

    // Log A/B test variant for analytics (optional)
    console.log(`🎯 Hero subheadline A/B test - Variant ${String.fromCharCode(65 + optionIndex)}: "${selectedOption}"`);
  }, []);

  const trustFactors = [
    {
      icon: Shield,
      text: 'Verified Listings',
      description: 'Venue listings verified for accuracy and completeness'
    },
    {
      icon: Star,
      text: 'Trusted Reviews',
      description: 'Real reviews from verified guests'
    },
    {
      icon: Clock,
      text: 'Quick Response Time',
      description: 'Most hosts respond within 2 hours'
    }
  ];

  return (
    <section className="pt-12 md:pt-20 pb-6 md:pb-8 px-4 sm:px-6 relative" style={{ overflow: 'visible' }}>
      {/* Enhanced background with more visual appeal */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-white to-purple-50" />
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239333ea' fill-opacity='0.6'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container-width relative w-full">
        {/* Mobile-first design: Search comes first on mobile */}
        <div className="block md:hidden mb-6">
          {/* Mobile header - compact */}
          <div className="text-center mb-4">
            <h1 className="text-2xl font-bold mb-2">
              Find Your Perfect
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-purple-800">
                Party Venue
              </span>
            </h1>
            <p className="text-base text-gray-600 mb-3">
              Book amazing venues for your next party
            </p>
            <div className="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1.5 rounded-full text-sm font-medium">
              <span className="w-1.5 h-1.5 bg-purple-600 rounded-full mr-2 animate-pulse"></span>
              Start searching below
            </div>
          </div>

          {/* Mobile Search - Priority placement */}
          <div className="relative z-50 mb-4" style={{ overflow: 'visible' }}>
            <SearchBar />
          </div>

          {/* Mobile Smart Search */}
          <div className="relative z-40" style={{ overflow: 'visible' }}>
            <HomeSmartSearch />
          </div>
        </div>

        {/* Desktop design: Traditional layout */}
        <div className="hidden md:block">
          <div className="text-center mb-4">
            <h1 className="mb-3">
              Find Your Perfect
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-purple-800">
                Party Venue
              </span>
            </h1>

            <p className="text-large max-w-3xl mx-auto mb-4 text-gray-600">
              {currentSubheadline || "Book one-of-a-kind party venues your friends will talk about for years."}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              {trustFactors.map(({ icon: Icon, text, description }) => (
                <div
                  key={text}
                  className="card flex items-center gap-3 px-3 py-3 group"
                >
                  <div className="p-1.5 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                    <Icon className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-gray-900 text-sm">{text}</div>
                    <div className="text-xs text-gray-600">{description}</div>
                  </div>
                </div>
              ))}
            </div>

            <div className="inline-flex items-center bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-small font-medium mb-2">
              <span className="w-2 h-2 bg-purple-600 rounded-full mr-2 animate-pulse"></span>
              Start your search below - any field works!
            </div>
          </div>

          {/* Desktop Search Section */}
          <div className="relative z-50 mt-3" style={{ overflow: 'visible' }}>
            <SearchBar />
          </div>

          {/* Desktop Smart Search Section */}
          <div className="relative z-40 mt-3" style={{ overflow: 'visible' }}>
            <HomeSmartSearch />
          </div>
        </div>
      </div>
    </section>
  );
}

// Export both as default and named export
export const HeroSection = HeroSectionComponent;
export default HeroSectionComponent;