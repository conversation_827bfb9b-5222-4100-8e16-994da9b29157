import React, { useState, useEffect } from 'react';
import SearchBar from '../SearchBar';
import HomeSmartSearch from './HomeSmartSearch';
import { Shield, Star, Clock, ArrowRight } from 'lucide-react';

function HeroSectionComponent() {
  // Emotionally-engaging subheadline options for A/B testing
  const subheadlineOptions = [
    // Option A: Exclusivity + Social Proof
    "Unlock exclusive, verified spaces made for unforgettable celebrations with your crew.",

    // Option B: Bragging Rights + Memory Making
    "Book one-of-a-kind party venues your friends will talk about for years.",

    // Option C: Main Event + Standout Factor
    "Discover hand-picked venues where your celebration becomes the main event—not just another night out.",

    // Option D: VIP Access + Unforgettable
    "Access private, verified party spaces—so your event stands out and your guests never forget it.",

    // Option E: Legendary + Iconic Vibes
    "Make your next gathering legendary—book verified venues with iconic vibes and private access.",

    // Option F: Hidden Gems + Insider Access
    "Find hidden gem venues that turn your party into the story everyone's still talking about.",

    // Option G: VIP Treatment + Exclusive Access
    "Get VIP access to exclusive party venues that make every celebration feel like the event of the year."
  ];

  // Simple A/B testing - rotate based on day of month for consistent user experience
  // To test a specific variant, replace the useEffect logic with: setCurrentSubheadline(subheadlineOptions[X])
  const [currentSubheadline, setCurrentSubheadline] = useState('');

  useEffect(() => {
    const today = new Date().getDate();
    const optionIndex = today % subheadlineOptions.length;
    const selectedOption = subheadlineOptions[optionIndex];
    setCurrentSubheadline(selectedOption);

    // Log A/B test variant for analytics (optional)
    console.log(`🎯 Hero subheadline A/B test - Variant ${String.fromCharCode(65 + optionIndex)}: "${selectedOption}"`);
  }, []);

  const trustFactors = [
    {
      icon: Shield,
      text: 'Verified Listings',
      description: 'Venue listings verified for accuracy and completeness'
    },
    {
      icon: Star,
      text: 'Trusted Reviews',
      description: 'Real reviews from verified guests'
    },
    {
      icon: Clock,
      text: 'Quick Response Time',
      description: 'Most hosts respond within 2 hours'
    }
  ];

  return (
    <section className="pt-20 pb-12 px-4 sm:px-6 relative min-h-[85vh] flex items-center" style={{ overflow: 'visible' }}>
      {/* Enhanced background with more visual appeal */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-white to-purple-50" />
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239333ea' fill-opacity='0.6'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto max-w-6xl relative w-full">
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            Find Your Perfect
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-purple-800">
              Party Venue
            </span>
          </h1>

          <p className="text-lg md:text-xl max-w-2xl mx-auto mb-8 text-gray-600 leading-relaxed">
            {currentSubheadline || "Book one-of-a-kind party venues your friends will talk about for years."}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-4xl mx-auto">
            {trustFactors.map(({ icon: Icon, text, description }) => (
              <div
                key={text}
                className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl flex items-center gap-3 px-4 py-4 group hover:shadow-md transition-all duration-200"
              >
                <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                  <Icon className="h-5 w-5 text-purple-600" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900 text-sm">{text}</div>
                  <div className="text-xs text-gray-600">{description}</div>
                </div>
              </div>
            ))}
          </div>

          <div className="inline-flex items-center bg-purple-100 text-purple-800 px-6 py-3 rounded-full text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-purple-600 rounded-full mr-2 animate-pulse"></span>
            Start your search below - any field works!
          </div>
        </div>

        {/* Enhanced Search Section with higher z-index */}
        <div className="relative z-50 max-w-4xl mx-auto" style={{ overflow: 'visible' }}>
          <SearchBar />
        </div>

        {/* Smart Search Section */}
        <div className="relative z-40 mt-6 max-w-4xl mx-auto" style={{ overflow: 'visible' }}>
          <HomeSmartSearch />
        </div>
      </div>
    </section>
  );
}

// Export both as default and named export
export const HeroSection = HeroSectionComponent;
export default HeroSectionComponent;