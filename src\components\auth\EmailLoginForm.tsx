import React, { useState } from 'react';
import { useAuth } from '../../providers/AuthProvider';

interface EmailLoginFormProps {
  className?: string;
  onSuccess?: () => void;
}

export default function EmailLoginForm({ 
  className = '',
  onSuccess 
}: EmailLoginFormProps) {
  const { signInWithEmail, isLoading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim() || isSubmitting || isLoading) return;

    setIsSubmitting(true);
    setSuccessMessage('');

    try {
      await signInWithEmail(email.trim());
      setSuccessMessage('Magic link sent! Check your email to sign in.');
      setEmail('');
      onSuccess?.();
    } catch (err) {
      console.error('Email sign-in error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  return (
    <div className={`w-full ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email address
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
            disabled={isSubmitting || isLoading}
            className="
              w-full px-4 py-3 border border-gray-300 rounded-lg
              focus:ring-2 focus:ring-purple-500 focus:border-transparent
              disabled:opacity-50 disabled:cursor-not-allowed
              placeholder-gray-400 text-gray-900
            "
          />
        </div>

        <button
          type="submit"
          disabled={!email.trim() || !isValidEmail(email) || isSubmitting || isLoading}
          className="
            w-full flex items-center justify-center px-4 py-3
            bg-purple-600 text-white font-medium rounded-lg
            hover:bg-purple-700 focus:outline-none focus:ring-2 
            focus:ring-offset-2 focus:ring-purple-500
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
          "
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"></div>
              Sending magic link...
            </div>
          ) : (
            'Send magic link'
          )}
        </button>
      </form>

      {successMessage && (
        <div className="mt-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {successMessage}
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}
    </div>
  );
}
