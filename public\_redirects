# SEO-friendly redirects for HouseGoing

# Redirect old URLs to new structure
/become-host /host/signup 301
/list-venue /host/signup 301
/venues/* /find-venues 301

# API routes (keep as 200 for SPA functionality)
/api/* /api/:splat 200

# Admin routes (keep as 200 for SPA functionality)
/admin/* /index.html 200

# Host portal routes (keep as 200 for SPA functionality)
/host/* /index.html 200

# Auth routes (keep as 200 for SPA functionality)
/auth/* /index.html 200

# Owner portal routes (keep as 200 for SPA functionality)
/owner/* /index.html 200

# Main SPA fallback - this should be last
/* /index.html 200
