/**
 * Clerk-<PERSON>pabase Synchronization Service
 *
 * This service handles the synchronization of user data between Clerk and Supa<PERSON>.
 * It ensures that when a user authenticates with <PERSON>, their data is properly
 * synchronized with Supa<PERSON> for backend operations.
 */

import { User as ClerkUser } from '@clerk/clerk-react';
import { clerkSupabase } from '../../lib/clerk-supabase';
import { UserRole } from './index';

/**
 * Synchronize a Clerk user with Supabase
 * This should be called after authentication or when user data changes
 */
export async function syncClerkUserWithSupabase(
  clerkUser: ClerkUser,
  role?: UserRole
): Promise<any> {
  try {
    if (!clerkUser) {
      console.error('Cannot sync null user with Supabase');
      return null;
    }

    const clerkId = clerkUser.id;
    const email = clerkUser.primaryEmailAddress?.emailAddress || '';

    if (!email) {
      console.error('User has no email address, cannot sync with Supabase');
      return null;
    }

    console.log(`Syncing Clerk user ${email} (${clerkId}) with <PERSON>pa<PERSON>...`);

    // Check for user type in localStorage
    const registrationTypeFromStorage = localStorage.getItem('registering_as_host');
    const userTypeFromStorage = localStorage.getItem('auth_user_type') as UserRole;

    // Check if the user_profiles table exists
    let existingProfile = null;
    try {
      // First check if the table exists
      const { error: tableCheckError } = await clerkSupabase
        .from('user_profiles')
        .select('count(*)', { count: 'exact', head: true });

      // If table doesn't exist, log it but continue
      if (tableCheckError && tableCheckError.code === '42P01') {
        console.warn('user_profiles table does not exist, will attempt to create it during sync');
      } else {
        // Table exists, check for user profile
        const { data: profile, error: fetchError } = await clerkSupabase
          .from('user_profiles')
          .select('*')
          .eq('clerk_id', clerkId)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error fetching user profile:', fetchError);
        } else if (profile) {
          existingProfile = profile;
        }
      }
    } catch (error) {
      console.error('Exception checking for user profile:', error);
      // Continue with sync even if profile check fails
    }

    // Determine role with priority:
    // 1. Explicitly provided role parameter
    // 2. Role from auth_user_type in localStorage
    // 3. Role based on registering_as_host in localStorage
    // 4. Existing profile role
    // 5. Role from Clerk metadata
    // 6. Default to 'guest'
    let userRole: UserRole;

    if (role) {
      userRole = role;
      console.log(`Using explicitly provided role: ${userRole}`);
    } else if (userTypeFromStorage) {
      userRole = userTypeFromStorage;
      console.log(`Using role from auth_user_type in localStorage: ${userRole}`);
    } else if (registrationTypeFromStorage === 'true') {
      userRole = 'host';
      console.log(`Using role from registering_as_host in localStorage: ${userRole}`);
    } else if (existingProfile?.role) {
      userRole = existingProfile.role as UserRole;
      console.log(`Using existing profile role: ${userRole}`);
    } else if (clerkUser.publicMetadata?.role) {
      userRole = clerkUser.publicMetadata.role as UserRole;
      console.log(`Using role from Clerk metadata: ${userRole}`);
    } else {
      userRole = 'guest';
      console.log(`Using default role: ${userRole}`);
    }

    // Prepare user data for upsert
    const userData = {
      clerk_id: clerkId,
      email: email,
      role: userRole,
      first_name: clerkUser.firstName || '',
      last_name: clerkUser.lastName || '',
      is_host: userRole === 'host',
      updated_at: new Date().toISOString(),
      ...(existingProfile ? {} : { created_at: new Date().toISOString() })
    };

    console.log('Upserting user data in Supabase:', userData);

    // Try to upsert user in Supabase
    try {
      const { data, error } = await clerkSupabase
        .from('user_profiles')
        .upsert(userData, {
          onConflict: 'clerk_id',
          returning: 'representation'
        })
        .select()
        .single();

      if (error) {
        console.error('Error upserting user in Supabase:', error);

        // If the error is related to the table not existing, try to create it
        if (error.code === '42P01' || (error.message && error.message.includes('relation "user_profiles" does not exist'))) {
          console.log('Table does not exist, attempting to create it...');

          try {
            // Import the database initializer
            const { initializeDatabase } = await import('../../utils/database-initializer');

            // Initialize the database
            const dbInitResult = await initializeDatabase();

            if (dbInitResult) {
              console.log('Successfully initialized database, retrying upsert...');

              // Retry the upsert
              const { data: retryData, error: retryError } = await clerkSupabase
                .from('user_profiles')
                .upsert(userData, {
                  onConflict: 'clerk_id',
                  returning: 'representation'
                })
                .select()
                .single();

              if (retryError) {
                console.error('Error upserting user after table creation:', retryError);
                // Continue with authentication even if upsert fails
                return {
                  id: null,
                  clerk_id: clerkId,
                  email: email,
                  role: userRole
                };
              }

              return retryData;
            } else {
              console.error('Failed to initialize database');
              // Continue with authentication even if database initialization fails
              return {
                id: null,
                clerk_id: clerkId,
                email: email,
                role: userRole
              };
            }
          } catch (createTableError) {
            console.error('Exception creating user_profiles table:', createTableError);
            // Continue with authentication even if table creation fails
            return {
              id: null,
              clerk_id: clerkId,
              email: email,
              role: userRole
            };
          }
        }

        // Continue with authentication even if upsert fails
        return {
          id: null,
          clerk_id: clerkId,
          email: email,
          role: userRole
        };
      }

      console.log('Successfully upserted user in Supabase:', data);
      return data;
    } catch (error) {
      console.error('Exception upserting user in Supabase:', error);
      // Continue with authentication even if upsert fails
      return {
        id: null,
        clerk_id: clerkId,
        email: email,
        role: userRole
      };
    }

    // If the role in Clerk doesn't match the role in Supabase, update Clerk
    if (clerkUser.publicMetadata?.role !== userRole) {
      try {
        await clerkUser.update({
          publicMetadata: {
            ...clerkUser.publicMetadata,
            role: userRole
          }
        });
        console.log(`Updated Clerk user role to ${userRole}`);
      } catch (updateError) {
        console.error('Error updating Clerk user metadata:', updateError);
      }
    }

    // Set a flag in localStorage to indicate successful authentication
    localStorage.setItem('auth_success', 'true');
    localStorage.setItem('auth_success_time', new Date().toISOString());

    return data;
  } catch (error) {
    console.error('Error in syncClerkUserWithSupabase:', error);
    return null;
  }
}

/**
 * Create a Supabase session from a Clerk user
 * This is a simplified version that doesn't try to create Supabase auth sessions
 * Instead, it just creates a user profile in the database
 */
export async function createSupabaseSessionFromClerk(clerkUser: ClerkUser): Promise<boolean> {
  try {
    if (!clerkUser) {
      console.error('Cannot create Supabase session for null user');
      return false;
    }

    const email = clerkUser.primaryEmailAddress?.emailAddress;
    if (!email) {
      console.error('Cannot create Supabase session for user without email');
      return false;
    }

    console.log('Syncing Clerk user with Supabase database:', email);

    // Just sync the user profile to the database
    const result = await syncClerkUserWithSupabase(clerkUser);

    if (result) {
      console.log('Successfully synced user profile to Supabase');

      // Set auth success flags
      localStorage.setItem('auth_success', 'true');
      localStorage.setItem('auth_success_time', new Date().toISOString());

      // Dispatch auth complete event
      window.dispatchEvent(new CustomEvent('auth_complete', {
        detail: { success: true, provider: 'clerk-supabase' }
      }));

      return true;
    } else {
      console.error('Failed to sync user profile to Supabase');
      return false;
    }
  } catch (error) {
    console.error('Error in createSupabaseSessionFromClerk:', error);
    return false;
  }
}

/**
 * Initialize the Clerk-Supabase synchronization
 * This should be called when the app starts
 */
export function initClerkSupabaseSync(): void {
  // Add event listeners for Clerk authentication events
  document.addEventListener('ClerkLoaded', () => {
    console.log('Clerk loaded, initializing Clerk-Supabase sync');
  });

  // Listen for auth_success events from localStorage
  const checkAuthSuccess = () => {
    const authSuccess = localStorage.getItem('auth_success');
    if (authSuccess === 'true') {
      console.log('Auth success flag found in localStorage');

      // Get user info from localStorage
      const email = localStorage.getItem('clerk_user_email');
      const firstName = localStorage.getItem('first_name');
      const lastName = localStorage.getItem('last_name');
      const clerkId = localStorage.getItem('clerk_user_id');
      const role = localStorage.getItem('auth_user_type') || 'guest';

      // Remove the flag to prevent repeated checks
      localStorage.removeItem('auth_success');

      // Dispatch a custom event to notify the app that authentication is complete
      window.dispatchEvent(new CustomEvent('auth_complete', {
        detail: {
          success: true,
          provider: 'clerk-supabase',
          user: {
            id: clerkId || `temp-${Date.now()}`,
            email: email || '',
            first_name: firstName || '',
            last_name: lastName || '',
            name: `${firstName || ''} ${lastName || ''}`.trim() || (email ? email.split('@')[0] : ''),
            role: role
          }
        }
      }));
    }
  };

  // Check for auth success flag every second for 15 seconds
  const authSuccessInterval = setInterval(checkAuthSuccess, 1000);
  setTimeout(() => clearInterval(authSuccessInterval), 15000);

  // Initial check
  checkAuthSuccess();
}
