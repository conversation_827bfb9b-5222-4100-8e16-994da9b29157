import React from 'react';
import { useAuth } from '../providers/AuthProvider';
import { useLocation } from 'react-router-dom';
import EnhancedMessaging from '../components/messaging/EnhancedMessaging';
import SEO from '../components/seo/SEO';

export default function Messages() {
  const { user } = useAuth();
  const location = useLocation();

  // Get any pre-selected conversation from navigation state
  const selectedConversation = location.state?.selectedConversation;

  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p>Please sign in to view your messages</p>
      </div>
    );
  }

  return (
    <>
      <SEO
        title="Messages | HouseGoing"
        description="Communicate with hosts and guests on HouseGoing. Send messages, coordinate bookings, and get answers to your questions."
        url="https://housegoing.com.au/messages"
      />

      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
            <p className="mt-1 text-gray-600">Communicate with hosts and guests</p>
          </div>

          <EnhancedMessaging
            className="h-[70vh]"
            otherUserId={selectedConversation?.otherUser?.id}
            bookingId={selectedConversation?.booking?.id}
          />
        </div>
      </div>
    </>
  );
}
