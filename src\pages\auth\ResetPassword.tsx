import { SignIn } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';

export default function ResetPassword() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Reset your password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-xl sm:rounded-xl sm:px-10 border border-gray-100">
          <SignIn
            appearance={clerkAppearance}
            routing="path"
            path="/reset-password"
            redirectUrl="/login"
            initialStep="forgot_password"
          />
        </div>
      </div>
    </div>
  );
}
