import React, { useState, useRef, useEffect } from 'react';
import { Send, X, MessageSquare, Loader, RefreshCw, Maximize2, Minimize2, ThumbsUp, ThumbsDown, History } from 'lucide-react';
import { useAuth } from '@clerk/clerk-react';
import { createConversation, addMessage, getUserConversations, getConversationMessages } from '../../lib/supabase/chat-history';

const DEFAULT_MESSAGES = [
  { role: 'assistant', content: "Hi there! I'm Homie from HouseGoing. How can I help you find the perfect venue for your event today?" }
];

export default function LangChainChat({ agentType = 'sales', context = 'general' }) {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [message, setMessage] = useState('');
  const [sessionId, setSessionId] = useState(null);
  const [conversationId, setConversationId] = useState(null);
  const [messages, setMessages] = useState(DEFAULT_MESSAGES);
  const [conversations, setConversations] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [feedback, setFeedback] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus();
    }
  }, [isOpen]);

  // Toggle chat open/closed
  const toggleChat = () => {
    setIsOpen(prev => !prev);
  };

  // Toggle maximize/minimize
  const toggleMaximize = () => {
    setIsMaximized(prev => !prev);
  };

  // Load user's conversation history
  const loadConversationHistory = async () => {
    if (!user) return;

    try {
      setIsLoadingHistory(true);
      const userId = user.id;
      const userConversations = await getUserConversations(userId, agentType);
      setConversations(userConversations);
    } catch (error) {
      console.error('Error loading conversation history:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Load a specific conversation
  const loadConversation = async (id) => {
    try {
      setIsLoading(true);
      const conversationMessages = await getConversationMessages(id);

      if (conversationMessages.length > 0) {
        setMessages(conversationMessages.map(msg => ({
          role: msg.role,
          content: msg.content
        })));
        setConversationId(id);
        setShowHistory(false);
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new conversation
  const createNewConversation = async () => {
    if (!user) return null;

    try {
      const userId = user.id;
      const title = `Conversation ${new Date().toLocaleString()}`;
      const conversation = await createConversation(userId, agentType, title);
      return conversation.id;
    } catch (error) {
      console.error('Error creating conversation:', error);
      return null;
    }
  };

  // Save a message to the current conversation
  const saveMessage = async (role, content) => {
    if (!conversationId) {
      // Create a new conversation if one doesn't exist
      const newConversationId = await createNewConversation();
      if (newConversationId) {
        setConversationId(newConversationId);
        // Save the initial assistant message
        if (messages.length > 0 && messages[0].role === 'assistant') {
          await addMessage(newConversationId, 'assistant', messages[0].content);
        }
      } else {
        return; // Failed to create conversation
      }
    }

    try {
      await addMessage(conversationId, role, content);
    } catch (error) {
      console.error('Error saving message:', error);
    }
  };

  // Reset conversation
  const handleReset = () => {
    setMessages(DEFAULT_MESSAGES);
    setSessionId(null);
    setConversationId(null);
  };

  // Toggle conversation history
  const toggleHistory = () => {
    if (!showHistory) {
      loadConversationHistory();
    }
    setShowHistory(prev => !prev);
  };

  // Send message to API
  const sendMessage = async () => {
    if (!message.trim()) return;

    // Add user message to chat
    const userMessage = { role: 'user', content: message };
    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsLoading(true);

    try {
      // Save user message to database
      if (user) {
        await saveMessage('user', userMessage.content);
      }

      // Send message to API
      const response = await fetch('/api/langchain-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message.trim(),
          sessionId,
          agentType,
          context,
          userId: user?.id
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();

      // Update session ID
      if (data.sessionId) {
        setSessionId(data.sessionId);
      }

      // Add assistant response to chat
      const assistantMessage = {
        id: `msg_${Date.now()}`,
        role: 'assistant',
        content: data.response,
        userMessage: message.trim()
      };
      setMessages(prev => [...prev, assistantMessage]);

      // Save assistant message to database
      if (user) {
        await saveMessage('assistant', assistantMessage.content);
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message to chat
      const errorMessage = {
        role: 'assistant',
        content: "I'm sorry, I encountered an error. Please try again in a moment."
      };
      setMessages(prev => [...prev, errorMessage]);

      // Save error message to database
      if (user) {
        await saveMessage('assistant', errorMessage.content);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    sendMessage();
  };

  // Handle input change
  const handleInputChange = (e) => {
    setMessage(e.target.value);
  };

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Handle feedback submission
  const handleFeedback = async (messageId, rating) => {
    // Update local state
    setFeedback(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        rating,
        messageId
      }
    }));

    // Find the message
    const message = messages.find(msg => msg.id === messageId);
    if (!message) return;

    try {
      // Submit feedback to API
      const response = await fetch('/api/ai-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'submit-feedback',
          data: {
            messageId,
            conversationId: sessionId || `conv_${Date.now()}`,
            userMessage: message.userMessage || '',
            assistantResponse: message.content,
            rating,
            notes: feedback[messageId]?.notes || '',
            agentType
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      console.log('Feedback submitted successfully');
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  // Handle feedback notes
  const handleFeedbackNotes = async (messageId, notes) => {
    // Update local state
    setFeedback(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        notes
      }
    }));

    // If we already have a rating, submit the updated feedback
    if (feedback[messageId]?.rating) {
      try {
        // Find the message
        const message = messages.find(msg => msg.id === messageId);
        if (!message) return;

        // Submit feedback to API
        const response = await fetch('/api/ai-feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'submit-feedback',
            data: {
              messageId,
              conversationId: sessionId || `conv_${Date.now()}`,
              userMessage: message.userMessage || '',
              assistantResponse: message.content,
              rating: feedback[messageId].rating,
              notes,
              agentType
            }
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to submit feedback notes');
        }

        console.log('Feedback notes submitted successfully');
      } catch (error) {
        console.error('Error submitting feedback notes:', error);
      }
    }
  };

  return (
    <div className={`fixed z-50 ${isMaximized ? 'inset-0' : 'bottom-4 right-4'}`}>
      {/* Chat button - hidden when maximized */}
      {(!isOpen || !isMaximized) && (
        <button
          onClick={toggleChat}
          className="bg-purple-600 text-white p-4 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
          aria-label={isOpen ? "Close chat" : "Open chat"}
        >
          {isOpen ? <X size={24} /> : <MessageSquare size={24} />}
        </button>
      )}

      {/* Chat window */}
      {isOpen && (
        <div className={`fixed z-50 bg-white rounded-lg shadow-xl flex flex-col overflow-hidden border border-gray-200 transition-all duration-300 ${isMaximized
          ? 'inset-4 md:inset-10'
          : 'bottom-16 right-0 w-80 sm:w-96 h-[32rem]'}`}>
          {/* Header */}
          <div className="bg-purple-600 text-white p-3 flex justify-between items-center">
            <div className="flex items-center">
              <MessageSquare size={20} className="mr-2" />
              <h3 className="font-medium">Chat with Homie (LangChain)</h3>
            </div>
            <div className="flex space-x-1">
              <button
                onClick={toggleHistory}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Chat history"
                disabled={!user}
              >
                <History size={16} />
              </button>
              <button
                onClick={handleReset}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Reset conversation"
              >
                <RefreshCw size={16} />
              </button>
              <button
                onClick={toggleMaximize}
                className="text-white hover:text-purple-200 p-1 rounded"
                title={isMaximized ? "Minimize" : "Maximize"}
                aria-label={isMaximized ? "Minimize chat window" : "Maximize chat window"}
                aria-expanded={isMaximized}
              >
                {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
              </button>
              <button
                onClick={toggleChat}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Close chat"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Conversation History */}
          {showHistory && (
            <div className="flex-1 p-3 overflow-y-auto bg-gray-50">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Conversation History</h4>
              {isLoadingHistory ? (
                <div className="flex justify-center items-center h-32">
                  <RefreshCw className="animate-spin h-5 w-5 text-purple-600" />
                </div>
              ) : conversations.length > 0 ? (
                <ul className="space-y-2">
                  {conversations.map((conversation) => (
                    <li key={conversation.id}>
                      <button
                        onClick={() => loadConversation(conversation.id)}
                        className="w-full text-left p-2 rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-sm text-gray-800">
                            {conversation.title || `Conversation ${new Date(conversation.created_at).toLocaleString()}`}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(conversation.updated_at).toLocaleString()}
                          </span>
                        </div>
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No conversation history found.
                </p>
              )}
              <div className="mt-3 flex justify-center">
                <button
                  onClick={handleReset}
                  className="px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  Start New Conversation
                </button>
              </div>
            </div>
          )}

          {/* Messages */}
          {!showHistory && (
            <div className="flex-1 p-3 overflow-y-auto bg-gray-50">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`mb-3 ${
                  msg.role === 'user' ? 'text-right' : 'text-left'
                }`}
              >
                <div
                  className={`inline-block p-3 rounded-lg ${
                    msg.role === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-200 text-gray-800'
                  }`}
                >
                  {msg.content}
                </div>
                {msg.role === 'assistant' && msg.id && context === 'training' && (
                  <div className="flex items-center mt-1 space-x-2">
                    <button
                      onClick={() => handleFeedback(msg.id, 'good')}
                      className={`p-1 rounded ${feedback[msg.id]?.rating === 'good' ? 'bg-green-100 text-green-600' : 'text-gray-400 hover:text-green-600'}`}
                      title="Good response"
                    >
                      <ThumbsUp size={14} />
                    </button>
                    <button
                      onClick={() => handleFeedback(msg.id, 'bad')}
                      className={`p-1 rounded ${feedback[msg.id]?.rating === 'bad' ? 'bg-red-100 text-red-600' : 'text-gray-400 hover:text-red-600'}`}
                      title="Bad response"
                    >
                      <ThumbsDown size={14} />
                    </button>
                    {feedback[msg.id] && (
                      <input
                        type="text"
                        placeholder="Add feedback notes..."
                        value={feedback[msg.id].notes || ''}
                        onChange={(e) => handleFeedbackNotes(msg.id, e.target.value)}
                        className="ml-2 text-xs p-1 border border-gray-300 rounded w-full"
                      />
                    )}
                  </div>
                )}
              </div>
            ))}
            {isLoading && (
              <div className="text-left mb-3">
                <div className="inline-block p-3 rounded-lg bg-gray-200 text-gray-800">
                  <div className="flex items-center">
                    <Loader size={16} className="animate-spin mr-2" />
                    <span>Thinking...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
            </div>
          )}

          {/* Input */}
          <form onSubmit={handleSubmit} className="p-3 border-t border-gray-200">
            <div className="flex">
              <input
                type="text"
                value={message}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 p-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-purple-600"
                ref={inputRef}
                disabled={isLoading}
              />
              <button
                type="submit"
                className="bg-purple-600 text-white p-2 rounded-r-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-600"
                disabled={isLoading || !message.trim()}
              >
                <Send size={20} />
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
