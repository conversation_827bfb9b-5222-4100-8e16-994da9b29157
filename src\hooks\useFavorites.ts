import { useState, useEffect } from 'react';
import { useAuth } from '../providers/AuthProvider';

export function useFavorites() {
  const [favorites, setFavorites] = useState<string[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      // Load favorites from localStorage for now
      // Replace with API call later
      const storedFavorites = localStorage.getItem(`favorites_${user.id}`);
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    }
  }, [user]);

  const toggleFavorite = (venueId: string) => {
    if (!user) return;

    const newFavorites = favorites.includes(venueId)
      ? favorites.filter(id => id !== venueId)
      : [...favorites, venueId];

    setFavorites(newFavorites);
    localStorage.setItem(`favorites_${user.id}`, JSON.stringify(newFavorites));
  };

  return { favorites, toggleFavorite };
}