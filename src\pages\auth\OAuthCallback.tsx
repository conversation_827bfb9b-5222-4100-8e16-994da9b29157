import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser, useClerk } from '@clerk/clerk-react';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import { isHost } from '../../utils/user-roles';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';
import { CLERK_CONFIG } from '../../config/clerk';
import { handleOAuthCallback as handleDirectOAuthCallback } from '../../utils/clerk-oauth';
import { useSupabase } from '../../providers/SupabaseProvider';
import { syncUserWithSupabase, UserRole } from '../../services/auth';

export default function OAuthCallback() {
  const { user, isLoaded } = useUser();
  const { client } = useClerk();
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserRole } = useSupabase();
  const [status, setStatus] = useState('Processing your sign-in...');
  const [error, setError] = useState('');
  const [processingComplete, setProcessingComplete] = useState(false);

  // Check if we're registering as a host from localStorage and handle OAuth callback
  useEffect(() => {
    // Log all available information for debugging
    console.log('OAuthCallback: Component mounted');
    console.log('OAuthCallback: User state:', { user, isLoaded });
    console.log('OAuthCallback: Current URL:', window.location.href);
    console.log('OAuthCallback: Search params:', location.search);
    console.log('OAuthCallback: Pathname:', location.pathname);
    console.log('OAuthCallback: Clerk config:', {
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      hostSignUpRedirectURL: CLERK_CONFIG.hostSignUpRedirectURL
    });

    // Log any stored OAuth attempt information
    const lastOAuthAttempt = sessionStorage.getItem('last_oauth_attempt');
    if (lastOAuthAttempt) {
      console.log('Last OAuth attempt:', JSON.parse(lastOAuthAttempt));
    }

    // Log any stored Clerk navigation information
    const lastClerkNavigation = sessionStorage.getItem('last_clerk_navigation');
    if (lastClerkNavigation) {
      console.log('Last Clerk navigation:', JSON.parse(lastClerkNavigation));
    }

    // Check if we're registering as a host
    const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';
    if (registeringAsHost) {
      console.log('OAuthCallback: Found registering_as_host flag in localStorage');
      setStatus('Detected host registration from previous step...');
    }

    // Check for error parameters in the URL
    const params = new URLSearchParams(location.search);
    if (params.has('error') || params.has('error_description')) {
      const error = params.get('error');
      const errorDescription = params.get('error_description');
      console.error('OAuth error detected in URL:', { error, errorDescription });
      setError(`Authentication error: ${errorDescription || error || 'Unknown error'}`);
      return;
    }

    // Handle Google OAuth callback specifically
    const isGoogleCallback =
      location.pathname.includes('google') ||
      location.search.includes('google') ||
      location.search.includes('oauth_token') ||
      location.search.includes('code=') ||
      document.referrer.includes('google') ||
      document.referrer.includes('accounts.google.com');

    if (isGoogleCallback) {
      console.log('Google OAuth callback detected');
      setStatus('Processing your Google sign-in...');

      // Store a flag to indicate we're in a Google OAuth flow
      localStorage.setItem('google_oauth_flow', 'true');

      // If we're in development mode and this is a Google callback, we might need to
      // manually handle the OAuth flow completion
      if (CLERK_CONFIG.developmentMode) {
        console.log('Development mode detected, preparing fallback handling for Google OAuth');

        // In development mode, we can simulate a successful sign-in
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';

        // Set user role in localStorage for development mode
        localStorage.setItem('user_role', registeringAsHost ? 'host' : 'guest');
        localStorage.setItem('user_email', '<EMAIL>');

        // Redirect to the appropriate page after a short delay
        setTimeout(() => {
          if (registeringAsHost) {
            navigate('/host/dashboard?auth=success&dev_mode=true');
          } else {
            navigate('/?auth=success&dev_mode=true');
          }
          setProcessingComplete(true);
        }, 1500);
      }
    }

    // If this is an SSO callback, extract the parameters and store them
    if (location.pathname === '/sso-callback') {
      const params = new URLSearchParams(location.search);
      const afterSignInUrl = params.get('after_sign_in_url');
      const afterSignUpUrl = params.get('after_sign_up_url');
      const redirectUrl = params.get('redirect_url');

      console.log('SSO Callback detected with params:', {
        afterSignInUrl,
        afterSignUpUrl,
        redirectUrl
      });

      // Store these parameters for later use
      if (afterSignInUrl) localStorage.setItem('after_sign_in_url', afterSignInUrl);
      if (afterSignUpUrl) localStorage.setItem('after_sign_up_url', afterSignUpUrl);
      if (redirectUrl) localStorage.setItem('redirect_url', redirectUrl);
    }

    // If we don't have user data yet, show a loading message
    if (!isLoaded) {
      console.log('OAuthCallback: Clerk not loaded yet, waiting...');
      setStatus('Initializing authentication, please wait...');
    } else if (!user) {
      console.log('OAuthCallback: No user data available yet');
      setStatus('Processing your sign-in, please wait...');
    } else {
      console.log('OAuthCallback: User authenticated:', {
        id: user.id,
        email: user.primaryEmailAddress?.emailAddress
      });
      setStatus('Authentication successful, redirecting...');
    }
  }, [location, isLoaded, user]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                           location.pathname.includes('google') ||
                           location.search.includes('google') ||
                           location.search.includes('oauth_token') ||
                           location.search.includes('code=');

    if (!user && isLoaded && !processingComplete) {
      console.log('OAuth callback with no user data available. Checking for fallback options...');

      // Check if we're in a Google OAuth flow
      if (googleOAuthFlow) {
        console.log('Google OAuth flow detected but no user data available.');

        // Check if we have registration type in localStorage or sessionStorage
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true' ||
                                 sessionStorage.getItem('registering_as_host') === 'true';

        // Check for error parameters in the URL
        const params = new URLSearchParams(location.search);
        if (params.has('error') || params.has('error_description')) {
          const error = params.get('error');
          const errorDescription = params.get('error_description');
          console.error('OAuth error detected in URL:', { error, errorDescription });
          setError(`Authentication error: ${errorDescription || error || 'Unknown error'}`);

          // Redirect to appropriate login page after a delay
          setTimeout(() => {
            if (registeringAsHost) {
              navigate('/host/login?auth_error=true');
            } else {
              navigate('/login?auth_error=true');
            }
            setProcessingComplete(true);
          }, 3000);
          return;
        }

        // In production mode, we should retry the authentication
        if (!CLERK_CONFIG.developmentMode) {
          console.log('Production mode detected, redirecting to appropriate sign-in page...');
          setStatus('Redirecting to sign-in page...');

          // Clear OAuth flags
          localStorage.removeItem('google_oauth_flow');
          sessionStorage.removeItem('oauth_timestamp');

          // Redirect to the appropriate sign-in page
          setTimeout(() => {
            if (registeringAsHost) {
              navigate('/host/login?auth_error=true');
            } else {
              navigate('/login?auth_error=true');
            }
            setProcessingComplete(true);
          }, 1500);
        } else {
          // In development mode, use a more lenient fallback
          console.log('Development mode detected, using fallback authentication flow');
          setStatus('Completing your sign-in using development mode fallback...');

          // Clear OAuth flags
          localStorage.removeItem('google_oauth_flow');
          sessionStorage.removeItem('oauth_timestamp');

          // Try to get the destination from URL params
          const params = new URLSearchParams(location.search);
          const destination = params.get('destination');

          // Determine if the user should be a host based on all available information
          const shouldBeHost = registeringAsHost || destination === 'host-portal';

          // Set user role in localStorage for development mode
          localStorage.setItem('user_role', shouldBeHost ? 'host' : 'guest');

          // Mock user email for development
          localStorage.setItem('user_email', '<EMAIL>');

          // Redirect based on registration type
          setTimeout(() => {
            if (shouldBeHost) {
              console.log('Redirecting to host dashboard (development fallback)');
              navigate('/host/dashboard?auth=success&dev_mode=true');
            } else {
              console.log('Redirecting to home page (development fallback)');
              navigate('/?auth=success&dev_mode=true');
            }
            setProcessingComplete(true);
          }, 1500);
        }
        return;
      }
    }

    if (processingComplete) return;

    // Check if this is a direct OAuth callback
    const params = new URLSearchParams(location.search);
    const registrationType = params.get('registrationType');
    const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';

    // If this is a direct OAuth callback (from our custom implementation)
    if (registrationType || registeringAsHost) {
      console.log('OAuthCallback: Direct OAuth callback detected with registrationType:',
        registrationType || (registeringAsHost ? 'host' : 'guest'));

      // Handle the direct OAuth callback
      handleDirectOAuthCallback(params)
        .then(result => {
          console.log('OAuthCallback: Direct OAuth callback result:', result);
          setStatus(result.message);

          // Clear the localStorage flag after successful processing
          if (registeringAsHost) {
            localStorage.removeItem('registering_as_host');
          }

          setTimeout(() => {
            navigate(result.redirectTo);
            setProcessingComplete(true);
          }, 1500);
        })
        .catch(error => {
          console.error('OAuthCallback: Direct OAuth callback error:', error);
          setError('An error occurred during sign-in. Please try again.');
          setTimeout(() => {
            navigate('/');
            setProcessingComplete(true);
          }, 3000);
        });

      return;
    }

    // Standard OAuth callback handling
    const handleOAuthCallback = async () => {
      try {
        // Check if user exists
        if (!user) {
          console.log('No user found, redirecting to home page');
          setStatus('Completing authentication...');
          setTimeout(() => navigate('/'), 2000);
          setProcessingComplete(true);
          return;
        }

        // Get the user's email and query parameters
        const userEmail = user.primaryEmailAddress?.emailAddress || '';
        const params = new URLSearchParams(location.search);
        const destination = params.get('destination');
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';

        console.log('OAuth callback processing for:', userEmail);
        console.log('Destination param:', destination);
        console.log('localStorage registering_as_host:', registeringAsHost);
        setStatus(`Processing sign-in for ${userEmail}...`);

        // Log successful authentication for debugging
        console.log('Authentication successful with Clerk:', {
          userId: user.id,
          email: userEmail,
          firstName: user.firstName,
          lastName: user.lastName,
          metadata: user.publicMetadata
        });

        // Determine if the user should be a host
        const shouldBeHost = isHost(user) ||
                            isPreregisteredHost(userEmail) ||
                            destination === 'host-portal' ||
                            registeringAsHost;

        // Sync user with Supabase
        let userRole: UserRole = 'guest';

        if (shouldBeHost) {
          userRole = 'host';
          console.log('Host registration detected, setting role to host');
          setStatus('Setting up your host account...');

          // Store in localStorage as backup
          try {
            localStorage.setItem('registering_as_host', 'true');

            // Add to registered hosts in localStorage
            const registeredHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
            if (!registeredHosts.includes(userEmail)) {
              registeredHosts.push(userEmail);
              localStorage.setItem('registeredHosts', JSON.stringify(registeredHosts));
            }
          } catch (err) {
            console.error('Failed to update localStorage:', err);
          }
        }

        // Try to sync user with Supabase, but don't block authentication if it fails
        let profile = null;
        try {
          profile = await syncUserWithSupabase(user, userRole);
          console.log('User synced with Supabase:', profile);

          // Simulate a webhook event for testing
          simulateWebhookEvent('user.updated', {
            id: user.id,
            email_addresses: [{ email_address: userEmail }],
            public_metadata: { role: profile ? profile.role : userRole }
          });
        } catch (syncError) {
          console.error('Error syncing user with Supabase:', syncError);
          // Continue with authentication even if Supabase sync fails
        }

        // Store the user role in localStorage as a fallback
        localStorage.setItem('user_role', userRole);

        // Store the user email in localStorage as a fallback
        if (userEmail) {
          localStorage.setItem('user_email', userEmail);
        }

        // Update user metadata in Clerk
        try {
          await user.update({
            publicMetadata: {
              ...user.publicMetadata,
              role: userRole,
            },
          });
          console.log('User role updated in Clerk metadata');
        } catch (updateError) {
          console.error('Error updating user metadata in Clerk:', updateError);
          // Continue with authentication even if metadata update fails
        }

        // Determine where to redirect the user
        if (userRole === 'host' || shouldBeHost) {
          setStatus('Host account setup complete. Redirecting to dashboard...');

          // Redirect to host dashboard
          setTimeout(() => {
            navigate('/host/dashboard?registration=success');
            setProcessingComplete(true);
          }, 1500);
        } else {
          // Regular user - check for stored redirect URL first
          const storedRedirectUrl = localStorage.getItem('redirect_url');

          // Use a safe default redirect URL that we know exists
          let finalRedirectUrl = '/';

          // Check if the stored URL or default URL exists
          if (storedRedirectUrl) {
            finalRedirectUrl = storedRedirectUrl;
          } else if (CLERK_CONFIG.defaultUserRedirectURL) {
            finalRedirectUrl = CLERK_CONFIG.defaultUserRedirectURL;
          }

          console.log('Redirecting to:', finalRedirectUrl);

          // Clear the stored redirect URLs
          localStorage.removeItem('redirect_url');
          localStorage.removeItem('after_sign_in_url');
          localStorage.removeItem('after_sign_up_url');
          localStorage.removeItem('google_oauth_flow');

          setStatus(`Sign-in successful. Redirecting to ${finalRedirectUrl}...`);
          setTimeout(() => {
            navigate(finalRedirectUrl);
            setProcessingComplete(true);
          }, 1000);
        }
      } catch (error) {
        console.error('Error in OAuth callback:', error);

        // Log detailed error information for debugging
        console.error('OAuth callback error details:', {
          error,
          user: user ? { id: user.id, email: user.primaryEmailAddress?.emailAddress } : null,
          location: window.location.href,
          params: Object.fromEntries(new URLSearchParams(location.search).entries())
        });

        // Check if we're in a Google OAuth flow
        const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                               location.pathname.includes('google') ||
                               location.search.includes('google');

        // Check if we have registration type in localStorage or sessionStorage
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true' ||
                                 sessionStorage.getItem('registering_as_host') === 'true';

        if (googleOAuthFlow) {
          // For Google OAuth, handle differently based on mode
          if (!CLERK_CONFIG.developmentMode) {
            // In production mode, show a more specific error
            setError('There was an issue with your Google sign-in. Please try again or use a different sign-in method.');

            // Clear the OAuth flags
            localStorage.removeItem('google_oauth_flow');
            sessionStorage.removeItem('oauth_timestamp');

            // Redirect back to appropriate login page after a delay
            setTimeout(() => {
              if (registeringAsHost) {
                navigate('/host/login?auth_error=true');
              } else {
                navigate('/login?auth_error=true');
              }
              setProcessingComplete(true);
            }, 3000);
          } else {
            // In development mode, use the fallback
            setStatus('Completing your sign-in...');

            // Clear the OAuth flags
            localStorage.removeItem('google_oauth_flow');
            sessionStorage.removeItem('oauth_timestamp');

            // Redirect based on registration type
            setTimeout(() => {
              if (registeringAsHost) {
                navigate('/host/dashboard?auth=success');
              } else {
                navigate('/?auth=success');
              }
              setProcessingComplete(true);
            }, 1500);
          }
        } else {
          // For other errors, show an error message
          setError('An error occurred during sign-in. Please try again.');

          // Redirect to appropriate login page
          setTimeout(() => {
            if (registeringAsHost) {
              navigate('/host/login?auth_error=true');
            } else {
              navigate('/login?auth_error=true');
            }
            setProcessingComplete(true);
          }, 3000);
        }
      }
    };

    handleOAuthCallback();
  }, [isLoaded, user, client, navigate, location, processingComplete]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md w-full">
        <div className="mb-4">
          <img
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
            className="h-16 w-16 mx-auto mb-4"
          />
          {error && (
            <div className="w-8 h-8 mx-auto rounded-full bg-red-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}
        </div>
        <h2 className="text-xl font-semibold mb-2">{error ? 'Sign-in Error' : 'Completing your sign in'}</h2>
        <p className="text-gray-600 mb-4">{error || status}</p>
        {error && (
          <button
            onClick={() => navigate('/login')}
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}
