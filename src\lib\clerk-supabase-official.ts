/**
 * Official Clerk-Supabase Native Integration
 * Based on the official Clerk documentation (native integration, not JWT template)
 *
 * This is the NEW recommended way to integrate <PERSON> with Supabase as of April 2025.
 * The old JWT template method is deprecated.
 */

import { createClient } from '@supabase/supabase-js';

// Environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_URL : '') ||
  'https://fxqoowlruissctsgbljk.supabase.co';

const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY : '') ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk';

/**
 * Create a Supabase client using the official native integration pattern
 * This uses the accessToken callback for automatic token injection
 *
 * @param session - Clerk session object from useSession() hook
 * @returns Supabase client with Clerk authentication
 */
export function createClerkSupabaseClient(session: any) {
  console.log('🔗 Creating Clerk-Supabase client with native integration');

  return createClient(supabaseUrl, supabaseKey, {
    async accessToken() {
      try {
        // Use the native integration - no template needed, just getToken()
        const token = await session?.getToken();
        if (token) {
          console.log('✅ Clerk token obtained for Supabase request');
        } else {
          console.log('❌ No Clerk token available');
        }
        return token ?? null;
      } catch (error) {
        console.error('❌ Error getting Clerk token:', error);
        return null;
      }
    },
  });
}

/**
 * Server-side Supabase client (for API routes and server components)
 * Uses the official native integration pattern
 */
export function createServerSupabaseClient(getToken: () => Promise<string | null>) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_KEY!,
    {
      async accessToken() {
        return await getToken();
      },
    }
  );
}

/**
 * React hook to get an authenticated Supabase client
 * Use this in React components with useSession()
 */
export function useClerkSupabase(session: any) {
  return createClerkSupabaseClient(session);
}

/**
 * Simple client-side helper that matches the official docs pattern
 */
export function createOfficialClerkSupabaseClient(session: any) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_KEY!,
    {
      async accessToken() {
        return session?.getToken() ?? null;
      },
    }
  );
}
