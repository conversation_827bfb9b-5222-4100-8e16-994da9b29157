CREATE TABLE lga_rules (
  id SERIAL PRIMARY KEY,
  lga_name VARCHAR(100) NOT NULL UNIQUE,
  standard_residential_start TIME,
  standard_residential_end TIME,
  commercial_start TIME,
  commercial_end TIME,
  special_provisions TEXT,
  entertainment_precincts JSONB,
  late_night_trading_rules JSONB
);

-- Insert LGA-specific rules
INSERT INTO lga_rules (lga_name, standard_residential_start, standard_residential_end, commercial_start, commercial_end, special_provisions)
VALUES
  ('City of Sydney', '22:00:00', '07:00:00', '01:00:00', '07:00:00', 'Category A Areas: 24-hour trading permitted for approved venues'),
  ('North Sydney Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Stricter noise controls for harbourside properties (9:00 PM outdoor cutoff)'),
  ('Northern Beaches Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Manly Entertainment Precinct: Venues with extended trading permits until 2:00 AM'),
  ('Inner West Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Enmore Road Special Entertainment Precinct: Extended hours with sound attenuation requirements'),
  ('Waverley Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Bondi Beach Area: Stricter enforcement during summer months'),
  ('Parramatta Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Parramatta CBD: Extended hours for approved venues until 1:00 AM'),
  ('Newcastle City Council', '22:00:00', '07:00:00', '23:00:00', '07:00:00', 'Newcastle CBD: Extended hours until 3:00 AM for approved venues');
