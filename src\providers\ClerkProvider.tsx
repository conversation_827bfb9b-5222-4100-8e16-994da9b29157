import { <PERSON><PERSON><PERSON><PERSON> as Clerk<PERSON><PERSON>iderBase, useUser } from '@clerk/clerk-react';
import { ReactNode, useEffect, useState } from 'react';
import { clerkAppearance } from '../utils/clerk-theme';
import { CLERK_CONFIG } from '../config/clerk';
import { syncClerkUserWithSupabase, createSupabaseSessionFromClerk, initClerkSupabaseSync } from '../services/auth/clerk-supabase-sync';
import { clerkSupabase } from '../lib/clerk-supabase';

interface ClerkProviderProps {
  children: ReactNode;
}

export function ClerkProvider({ children }: ClerkProviderProps) {
  const [error, setError] = useState<Error | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Log Clerk configuration on mount for debugging
  useEffect(() => {
    console.log('Clerk configuration loaded:', {
      publishableKey: CLERK_CONFIG.publishableKey.substring(0, 10) + '...',
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      developmentMode: CLERK_CONFIG.developmentMode,
      domain: CLERK_CONFIG.clerkDomain
    });

    // Log a warning if using development keys
    if (CLERK_CONFIG.publishableKey.includes('test_')) {
      console.warn('Using Clerk test keys. For production, use live keys.');
    }

    // Initialize Clerk-Supabase synchronization
    initClerkSupabaseSync();

    // Set a timeout to ensure the app continues even if Clerk fails
    const timeout = setTimeout(() => {
      if (!isLoaded) {
        console.warn('Clerk initialization timeout, continuing anyway');
        setIsLoaded(true);
      }
    }, 3000);

    return () => clearTimeout(timeout);
  }, [isLoaded]);

  // Add a user change listener to handle Clerk authentication
  const UserSynchronizer = () => {
    const { user, isLoaded: isUserLoaded } = useUser();

    useEffect(() => {
      if (isUserLoaded && user) {
        console.log('User authenticated with Clerk:', user);

        // Check if the user has the necessary data
        if (!user.primaryEmailAddress) {
          console.warn('User has no primary email address');
          return;
        }

        // Store user info in localStorage for persistence
        try {
          localStorage.setItem('clerk_user_email', user.primaryEmailAddress.emailAddress);
          localStorage.setItem('clerk_user_id', user.id);
          localStorage.setItem('clerk_user_name', `${user.firstName || ''} ${user.lastName || ''}`.trim());
          localStorage.setItem('clerk_auth_time', new Date().toISOString());

          // Store user role if available
          const isHost = localStorage.getItem('registering_as_host') === 'true';
          localStorage.setItem('user_role', isHost ? 'host' : 'guest');

          // Set a flag in localStorage to indicate successful authentication
          localStorage.setItem('auth_success', 'true');
          localStorage.setItem('auth_success_time', new Date().toISOString());

          // Save user profile data to database (without using Supabase auth)
          saveUserProfileToDatabase(user)
            .then(success => {
              if (success) {
                console.log('User profile saved to database successfully');
              } else {
                console.warn('Failed to save user profile to database');
              }
            })
            .catch(error => {
              console.error('Error saving user profile to database:', error);
            });

          // Dispatch a custom event to notify the app that authentication is complete
          window.dispatchEvent(new CustomEvent('auth_complete', {
            detail: { success: true, provider: 'clerk' }
          }));

          // Force a page reload to ensure the auth state is properly updated
          // This is a last resort but often helps with auth state issues
          if (window.location.pathname !== '/auth/callback') {
            console.log('Forcing page reload to update auth state...');
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        } catch (error) {
          console.error('Error storing user info in localStorage:', error);
        }
      } else if (isUserLoaded && !user) {
        // User is loaded but not authenticated
        console.log('No authenticated user found in Clerk');

        // Clear user info from localStorage
        localStorage.removeItem('clerk_user_email');
        localStorage.removeItem('clerk_user_id');
        localStorage.removeItem('clerk_user_name');
        localStorage.removeItem('clerk_auth_time');
        localStorage.removeItem('auth_success');
        localStorage.removeItem('auth_success_time');
        localStorage.removeItem('auth_processing');
        localStorage.removeItem('reload_prevented');
      }
    }, [user, isUserLoaded]);

    // Also listen for auth state changes
    useEffect(() => {
      const handleAuthComplete = () => {
        console.log('Auth complete event detected, checking Clerk user...');

        if (isUserLoaded && user) {
          console.log('User is authenticated in Clerk after auth complete event');

          // Force a reload if we're not on the callback page
          if (window.location.pathname !== '/auth/callback' && !localStorage.getItem('reload_prevented')) {
            console.log('Forcing page reload after auth complete event...');
            localStorage.setItem('reload_prevented', 'true');
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        }
      };

      window.addEventListener('auth_complete', handleAuthComplete);

      return () => {
        window.removeEventListener('auth_complete', handleAuthComplete);
      };
    }, [user, isUserLoaded]);

    return null;
  };

  // Function to save user profile to database without using Supabase auth
  const saveUserProfileToDatabase = async (clerkUser: any): Promise<boolean> => {
    try {
      if (!clerkUser || !clerkUser.primaryEmailAddress) {
        return false;
      }

      const email = clerkUser.primaryEmailAddress.emailAddress;
      const isHost = localStorage.getItem('registering_as_host') === 'true';
      const role = isHost ? 'host' : 'guest';

      // Create user profile object
      const userProfile = {
        clerk_id: clerkUser.id,
        email: email,
        role: role,
        first_name: clerkUser.firstName || '',
        last_name: clerkUser.lastName || '',
        is_host: role === 'host',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Use the Clerk-Supabase client for authenticated operations
      const { error } = await clerkSupabase
        .from('user_profiles')
        .upsert(userProfile, { onConflict: 'clerk_id' });

      if (error) {
        console.error('Error saving user profile to database:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in saveUserProfileToDatabase:', error);
      return false;
    }
  };

  // Handle Clerk errors
  const handleClerkError = (err: Error) => {
    console.error('Clerk error:', err);

    // Prevent auth errors from showing on homepage or breaking the site
    const isHomepage = typeof window !== 'undefined' &&
                      (window.location.pathname === '/' ||
                       window.location.pathname === '/home');

    // Don't set error state on homepage to prevent error messages
    if (!isHomepage) {
      setError(err);
    }

    setIsLoaded(true);
  };

  return (
    <ClerkProviderBase
      publishableKey={CLERK_CONFIG.publishableKey}
      appearance={clerkAppearance}
      developmentMode={CLERK_CONFIG.developmentMode}
      fallbackRedirectUrl={CLERK_CONFIG.fallbackRedirectUrl}
      signInUrl="/login"
      signUpUrl="/signup"
      onError={handleClerkError}
      navigate={(to) => {
        // Add custom navigation logic to handle potential errors
        console.log('ClerkProvider navigating to:', to);

        // Log the navigation for debugging
        try {
          const lastNavigation = {
            timestamp: new Date().toISOString(),
            destination: to,
            referrer: document.referrer || 'unknown'
          };
          sessionStorage.setItem('last_clerk_navigation', JSON.stringify(lastNavigation));
        } catch (e) {
          console.error('Error storing navigation info:', e);
        }

        // Handle Google OAuth callback specifically
        if (to && typeof to === 'string' &&
            (to.includes('google') || to.includes('oauth') || to.includes('callback'))) {
          console.log('ClerkProvider detected OAuth navigation');

          // Check if this is a callback from Google OAuth
          if (to.includes('callback') || to.includes('oauth/google/callback')) {
            console.log('ClerkProvider detected OAuth callback, redirecting to auth callback handler');

            // Redirect to our auth callback handler
            const redirectUrl = '/auth/callback' + (to.includes('?') ? to.substring(to.indexOf('?')) : '');
            console.log('Redirecting to:', redirectUrl);

            // Set auth_success flag to ensure the session is checked
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());

            window.location.href = redirectUrl;
            return;
          }
        }

        // If the URL is invalid or not found, redirect to a safe fallback
        if (to === '/undefined' || to === '/null' || to === 'undefined' || to === 'null') {
          console.warn('ClerkProvider detected invalid navigation target, redirecting to fallback');
          window.location.href = CLERK_CONFIG.fallbackRedirectUrl || '/';
          return;
        }

        // Handle empty paths
        if (!to || to === '/' || to === '') {
          console.warn('ClerkProvider detected empty navigation target, redirecting to fallback');
          window.location.href = CLERK_CONFIG.fallbackRedirectUrl || '/';
          return;
        }

        // Handle relative URLs that don't start with /
        if (to && typeof to === 'string' && !to.startsWith('/') && !to.startsWith('http')) {
          console.warn('ClerkProvider detected relative URL without leading slash, adding slash');
          window.location.href = '/' + to;
          return;
        }

        // Otherwise, proceed with normal navigation
        window.location.href = to;
      }}
    >
      <UserSynchronizer />
      {children}
    </ClerkProviderBase>
  );
}
