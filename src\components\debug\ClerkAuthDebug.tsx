import React, { useEffect, useState } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';

export default function ClerkAuthDebug() {
  const { user, isLoaded: userLoaded } = useUser();
  const { getToken, isLoaded: authLoaded } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const updateDebugInfo = async () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        userLoaded,
        authLoaded,
        hasUser: !!user,
        clerkOnWindow: !!window.Clerk,
        clerkLoaded: window.Clerk?.loaded,
        clerkUser: window.Clerk?.user ? {
          id: window.Clerk.user.id,
          email: window.Clerk.user.primaryEmailAddress?.emailAddress,
          firstName: window.Clerk.user.firstName,
          lastName: window.Clerk.user.lastName
        } : null
      };

      if (user) {
        info.userDetails = {
          id: user.id,
          email: user.primaryEmailAddress?.emailAddress,
          firstName: user.firstName,
          lastName: user.lastName,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        };

        try {
          const token = await getToken();
          info.hasToken = !!token;
          info.tokenLength = token?.length || 0;
        } catch (error) {
          info.tokenError = error instanceof Error ? error.message : 'Unknown error';
        }
      }

      setDebugInfo(info);
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 2000);
    return () => clearInterval(interval);
  }, [user, userLoaded, authLoaded, getToken]);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="font-bold text-sm mb-2">🔍 Clerk Auth Debug</h3>
      <div className="text-xs space-y-1">
        <div>
          <strong>Status:</strong> {userLoaded && authLoaded ? '✅ Loaded' : '⏳ Loading...'}
        </div>
        <div>
          <strong>User:</strong> {debugInfo.hasUser ? '✅ Found' : '❌ None'}
        </div>
        <div>
          <strong>Clerk on Window:</strong> {debugInfo.clerkOnWindow ? '✅ Yes' : '❌ No'}
        </div>
        <div>
          <strong>Clerk Loaded:</strong> {debugInfo.clerkLoaded ? '✅ Yes' : '❌ No'}
        </div>
        {debugInfo.userDetails && (
          <div>
            <strong>Email:</strong> {debugInfo.userDetails.email}
          </div>
        )}
        {debugInfo.hasToken !== undefined && (
          <div>
            <strong>Token:</strong> {debugInfo.hasToken ? `✅ ${debugInfo.tokenLength} chars` : '❌ None'}
          </div>
        )}
        {debugInfo.tokenError && (
          <div className="text-red-600">
            <strong>Token Error:</strong> {debugInfo.tokenError}
          </div>
        )}
        <div className="text-gray-500 text-xs">
          Last updated: {debugInfo.timestamp?.split('T')[1]?.split('.')[0]}
        </div>
      </div>
    </div>
  );
}
