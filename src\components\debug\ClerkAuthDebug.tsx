import React, { useEffect, useState } from 'react';
import { useAuth } from '../../providers/AuthProvider';

export default function ClerkAuthDebug() {
  const { user, isLoading, isAuthenticated, session } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const updateDebugInfo = () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        isLoading,
        isAuthenticated,
        hasUser: !!user,
        hasSession: !!session,
        authSystem: 'Supabase Auth'
      };

      if (user) {
        info.userDetails = {
          id: user.id,
          email: user.email,
          provider: user.app_metadata?.provider,
          createdAt: user.created_at,
          lastSignIn: user.last_sign_in_at,
          userMetadata: user.user_metadata
        };
      }

      if (session) {
        info.sessionDetails = {
          accessToken: !!session.access_token,
          refreshToken: !!session.refresh_token,
          expiresAt: session.expires_at,
          tokenType: session.token_type
        };
      }

      setDebugInfo(info);
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 2000);
    return () => clearInterval(interval);
  }, [user, isLoading, isAuthenticated, session]);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="font-bold text-sm mb-2">🔍 Supabase Auth Debug</h3>
      <div className="text-xs space-y-1">
        <div>
          <strong>Status:</strong> {debugInfo.isLoading ? '⏳ Loading...' : '✅ Loaded'}
        </div>
        <div>
          <strong>Authenticated:</strong> {debugInfo.isAuthenticated ? '✅ Yes' : '❌ No'}
        </div>
        <div>
          <strong>User:</strong> {debugInfo.hasUser ? '✅ Found' : '❌ None'}
        </div>
        <div>
          <strong>Session:</strong> {debugInfo.hasSession ? '✅ Active' : '❌ None'}
        </div>
        <div>
          <strong>Auth System:</strong> {debugInfo.authSystem}
        </div>
        {debugInfo.userDetails && (
          <>
            <div>
              <strong>Email:</strong> {debugInfo.userDetails.email}
            </div>
            <div>
              <strong>Provider:</strong> {debugInfo.userDetails.provider || 'Unknown'}
            </div>
          </>
        )}
        {debugInfo.sessionDetails && (
          <div>
            <strong>Token:</strong> {debugInfo.sessionDetails.accessToken ? '✅ Valid' : '❌ None'}
          </div>
        )}
        <div className="text-gray-500 text-xs">
          Last updated: {debugInfo.timestamp?.split('T')[1]?.split('.')[0]}
        </div>
      </div>
    </div>
  );
}
