#!/usr/bin/env node

/**
 * Setup script for HouseGoing Availability System
 * This script initializes the database with availability tables and functions
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const SUPABASE_URL = 'https://fxqoowlruissctsgbljk.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk';

async function setupAvailabilitySystem() {
  console.log('🚀 Setting up HouseGoing Availability System...\n');

  // Create Supabase client with anon key
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

  try {
    // Step 1: Create availability tables
    console.log('📋 Step 1: Creating availability tables...');

    const availabilitySQL = fs.readFileSync(
      path.join(__dirname, '../supabase/sql/availability_system.sql'),
      'utf8'
    );

    const { error: tablesError } = await supabase.rpc('exec_sql', {
      sql: availabilitySQL
    });

    if (tablesError) {
      console.error('❌ Error creating availability tables:', tablesError);
      throw tablesError;
    }

    console.log('✅ Availability tables created successfully');

    // Step 2: Create availability functions
    console.log('\n🔧 Step 2: Creating availability functions...');

    const functionsSQL = fs.readFileSync(
      path.join(__dirname, '../supabase/sql/availability_functions.sql'),
      'utf8'
    );

    const { error: functionsError } = await supabase.rpc('exec_sql', {
      sql: functionsSQL
    });

    if (functionsError) {
      console.error('❌ Error creating availability functions:', functionsError);
      throw functionsError;
    }

    console.log('✅ Availability functions created successfully');

    // Step 3: Initialize default settings for existing venues
    console.log('\n⚙️ Step 3: Initializing default availability settings...');

    // Get all existing venues
    const { data: venues, error: venuesError } = await supabase
      .from('venues')
      .select('id, title');

    if (venuesError) {
      console.log('⚠️ No existing venues found or error fetching venues:', venuesError.message);
    } else if (venues && venues.length > 0) {
      console.log(`Found ${venues.length} existing venues, creating default availability settings...`);

      // Create default availability settings for each venue
      const defaultSettings = venues.map(venue => ({
        venue_id: venue.id,
        available_days: [1, 2, 3, 4, 5, 6, 0], // All days available
        default_start_time: '09:00:00',
        default_end_time: '23:00:00',
        min_booking_hours: 4,
        max_booking_hours: 12,
        lead_time_hours: 24,
        max_advance_days: 365,
        instant_booking_enabled: false
      }));

      const { error: settingsError } = await supabase
        .from('venue_availability_settings')
        .upsert(defaultSettings);

      if (settingsError) {
        console.error('❌ Error creating default settings:', settingsError);
      } else {
        console.log(`✅ Created default availability settings for ${venues.length} venues`);
      }

      // Create default operating hours (9 AM - 11 PM for all days)
      const operatingHours = [];
      venues.forEach(venue => {
        for (let day = 0; day <= 6; day++) {
          operatingHours.push({
            venue_id: venue.id,
            day_of_week: day,
            start_time: '09:00:00',
            end_time: '23:00:00',
            is_available: true
          });
        }
      });

      const { error: hoursError } = await supabase
        .from('venue_operating_hours')
        .upsert(operatingHours);

      if (hoursError) {
        console.error('❌ Error creating default operating hours:', hoursError);
      } else {
        console.log(`✅ Created default operating hours for ${venues.length} venues`);
      }
    }

    // Step 4: Test the availability system
    console.log('\n🧪 Step 4: Testing availability system...');

    if (venues && venues.length > 0) {
      const testVenueId = venues[0].id;
      const testStartDate = new Date();
      testStartDate.setDate(testStartDate.getDate() + 7); // Next week
      const testEndDate = new Date(testStartDate);
      testEndDate.setHours(testStartDate.getHours() + 4); // 4 hours later

      const { data: testResult, error: testError } = await supabase
        .rpc('check_venue_availability', {
          p_venue_id: testVenueId,
          p_start_datetime: testStartDate.toISOString(),
          p_end_datetime: testEndDate.toISOString()
        });

      if (testError) {
        console.error('❌ Error testing availability function:', testError);
      } else {
        console.log('✅ Availability system test successful');
        console.log(`   Test venue: ${venues[0].title}`);
        console.log(`   Test date: ${testStartDate.toLocaleDateString()}`);
        console.log(`   Available: ${testResult.available ? 'Yes' : 'No'}`);
        if (testResult.conflicts && testResult.conflicts.length > 0) {
          console.log(`   Conflicts: ${testResult.conflicts.join(', ')}`);
        }
      }
    }

    // Step 5: Create sample blocked slots for demonstration
    console.log('\n📅 Step 5: Creating sample blocked slots...');

    if (venues && venues.length > 0) {
      const sampleVenueId = venues[0].id;
      const blockDate = new Date();
      blockDate.setDate(blockDate.getDate() + 14); // Two weeks from now

      const sampleBlockedSlot = {
        venue_id: sampleVenueId,
        start_datetime: `${blockDate.toISOString().split('T')[0]}T10:00:00Z`,
        end_datetime: `${blockDate.toISOString().split('T')[0]}T14:00:00Z`,
        reason: 'Maintenance work',
        block_type: 'maintenance',
        is_recurring: false
      };

      const { error: blockError } = await supabase
        .from('venue_blocked_slots')
        .insert([sampleBlockedSlot]);

      if (blockError) {
        console.error('❌ Error creating sample blocked slot:', blockError);
      } else {
        console.log(`✅ Created sample blocked slot for ${venues[0].title}`);
        console.log(`   Blocked: ${blockDate.toLocaleDateString()} 10:00 AM - 2:00 PM`);
        console.log(`   Reason: Maintenance work`);
      }
    }

    console.log('\n🎉 Availability System Setup Complete!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Availability tables created');
    console.log('   ✅ Availability functions installed');
    console.log('   ✅ Default settings configured');
    console.log('   ✅ Operating hours initialized');
    console.log('   ✅ System tested successfully');
    console.log('   ✅ Sample data created');

    console.log('\n🔧 Next Steps:');
    console.log('   1. Venue owners can now manage their availability through the Owner Portal');
    console.log('   2. Venue search will automatically filter by availability');
    console.log('   3. Booking system will check real-time availability');
    console.log('   4. Blocked time slots will prevent double bookings');

    console.log('\n📖 Usage:');
    console.log('   - Owner Portal: /owner-portal → Availability Management');
    console.log('   - Search with dates: Venues will be filtered by availability');
    console.log('   - Booking: Real-time availability checking');

  } catch (error) {
    console.error('\n❌ Setup failed:', error);
    process.exit(1);
  }
}

// Helper function to execute SQL (fallback if rpc doesn't work)
async function executeSQLDirect(supabase, sql) {
  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  for (const statement of statements) {
    try {
      const { error } = await supabase.rpc('exec', { sql: statement });
      if (error) {
        console.error('Error executing statement:', statement.substring(0, 100) + '...');
        console.error('Error:', error);
      }
    } catch (err) {
      console.error('Error executing statement:', statement.substring(0, 100) + '...');
      console.error('Error:', err);
    }
  }
}

// Run the setup
setupAvailabilitySystem()
  .then(() => {
    console.log('\n✨ Setup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Setup failed:', error);
    process.exit(1);
  });

export { setupAvailabilitySystem };
