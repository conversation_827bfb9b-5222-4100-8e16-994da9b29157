import { signIn } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import GoogleButton from '../../components/auth/GoogleButton';

export default function SignIn() {
  const router = useRouter();
  const [error, setError] = useState('');

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    const result = await signIn('google', { 
      callbackUrl: '/',
      redirect: false 
    });
    
    if (result?.error) {
      setError(result.error);
    }
  };

  // Handle email/password sign-in
  const handleEmailSignIn = async (email: string, password: string) => {
    const result = await signIn('credentials', {
      email,
      password,
      callbackUrl: '/',
      redirect: false
    });

    if (result?.error) {
      setError(result.error);
    } else {
      router.push('/');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <img
            className="mx-auto h-16 w-16 mb-2"
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
          />
        </div>
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Welcome back to{' '}
          <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            HouseGoing
          </span>
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Sign in to find the perfect party venue
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-xl">
        <div className="bg-white py-10 px-6 shadow-xl sm:rounded-xl sm:px-12 border border-gray-100">
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="mb-8">
            <p className="text-center text-base text-gray-600 mb-4">
              Sign in with:
            </p>
            <GoogleButton onClick={handleGoogleSignIn} />
          </div>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center">
              <span className="px-4 py-1 bg-white text-gray-500 text-base">
                Or sign in with email
              </span>
            </div>
          </div>

          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            handleEmailSignIn(
              formData.get('email') as string,
              formData.get('password') as string
            );
          }}>
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                />
              </div>

              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  Sign in
                </button>
              </div>
            </div>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/signup" className="text-purple-600 hover:text-purple-800 font-medium">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
