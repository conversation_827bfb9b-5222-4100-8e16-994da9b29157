import React from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useAuth } from '../providers/AuthProvider';

export default function TestNextAuth() {
  const { data: session, status } = useSession();
  const { user, userProfile, isAuthenticated, isLoading } = useAuth();

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p>Loading authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">NextAuth.js + Supabase Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* NextAuth Session */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">NextAuth.js Session</h2>
            <div className="space-y-2">
              <p><strong>Status:</strong> {status}</p>
              <p><strong>Authenticated:</strong> {session ? 'Yes' : 'No'}</p>
              {session && (
                <>
                  <p><strong>Email:</strong> {session.user?.email}</p>
                  <p><strong>Name:</strong> {session.user?.name}</p>
                  <p><strong>Image:</strong> {session.user?.image}</p>
                  <p><strong>Session Expires:</strong> {session.expires}</p>
                </>
              )}
            </div>
            
            <div className="mt-6">
              {session ? (
                <button
                  onClick={() => signOut()}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
                >
                  Sign Out
                </button>
              ) : (
                <button
                  onClick={() => signIn('google')}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  Sign In with Google
                </button>
              )}
            </div>
          </div>

          {/* AuthProvider State */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">AuthProvider State</h2>
            <div className="space-y-2">
              <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
              <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              {user && (
                <>
                  <p><strong>User Email:</strong> {user.email}</p>
                  <p><strong>User Name:</strong> {user.name}</p>
                </>
              )}
            </div>
          </div>

          {/* Supabase User Profile */}
          <div className="bg-white rounded-lg shadow p-6 md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Supabase User Profile</h2>
            {userProfile ? (
              <div className="space-y-2">
                <p><strong>ID:</strong> {userProfile.id}</p>
                <p><strong>Email:</strong> {userProfile.email}</p>
                <p><strong>First Name:</strong> {userProfile.first_name || 'Not set'}</p>
                <p><strong>Last Name:</strong> {userProfile.last_name || 'Not set'}</p>
                <p><strong>Role:</strong> {userProfile.role}</p>
                <p><strong>Is Host:</strong> {userProfile.is_host ? 'Yes' : 'No'}</p>
                <p><strong>Created At:</strong> {new Date(userProfile.created_at).toLocaleString()}</p>
                <p><strong>Updated At:</strong> {new Date(userProfile.updated_at).toLocaleString()}</p>
              </div>
            ) : (
              <p className="text-gray-500">
                {isAuthenticated ? 'User profile not found in Supabase' : 'Not authenticated'}
              </p>
            )}
          </div>
        </div>

        {/* Raw Data */}
        <div className="mt-8 bg-gray-100 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Raw Data (Debug)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">NextAuth Session:</h3>
              <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="font-medium mb-2">User Profile:</h3>
              <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(userProfile, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
