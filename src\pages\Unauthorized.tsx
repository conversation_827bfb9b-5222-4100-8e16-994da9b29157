import React from 'react';
import { Link } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';

export default function Unauthorized() {
  const { user } = useUser();
  const userEmail = user?.primaryEmailAddress?.emailAddress || 'your email';

  return (
    <div className="min-h-screen pt-32 px-4 flex flex-col items-center">
      <div className="max-w-md mx-auto text-center">
        <h1 className="text-3xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You don't have permission to access this page.
          This area is restricted to administrators only.
        </p>

        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 text-left">
          <h2 className="font-semibold text-amber-800 mb-2">Need Admin Access?</h2>
          <p className="text-amber-700 text-sm mb-2">
            Your current email: <span className="font-mono">{userEmail}</span>
          </p>
          <p className="text-amber-700 text-sm">
            Please contact the site owner to have your email added to the admin list.
          </p>
        </div>

        <div className="flex flex-col space-y-4">
          <Link to="/" className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
            Return to Home
          </Link>
          <Link to="/my-account" className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            Go to My Account
          </Link>
        </div>
      </div>
    </div>
  );
}
