/**
 * Test Component Accuracy
 * 
 * This script compares the accuracy of Layer 8 LGA detection with the current
 * implementations in NSWPlanningMap.js and NSWPartyPlanningUpdated.tsx.
 */

// Test addresses
const testAddresses = [
  '23 John Radley Avenue, Dural, NSW 2158',
  '36 Earle St, Doonside NSW 2767',
  '10 Carlingford Road, Carlingford',
  '10 Darvall Road, Eastwood, NSW 2122',
  '123 King Street, Newtown, NSW 2042',
  '456 George Street, Sydney, NSW 2000',
  '182 Power Street, Glendenning, NSW 2761',
  '15 Wentworth Avenue, Sydney, NSW 2000'
];

// Test Layer 8 LGA detection
async function testLayer8LGADetection(address) {
  try {
    console.log(`Testing Layer 8 for address: ${address}`);
    
    // First, geocode the address
    const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    if (geocodeData.features && geocodeData.features.length > 0) {
      const [lng, lat] = geocodeData.features[0].center;
      console.log(`Geocoded coordinates: ${lat}, ${lng}`);
      
      // NSW Spatial Services API endpoint for LGA (Layer 8)
      const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
      
      const lgaParams = new URLSearchParams({
        geometry: `${lng},${lat}`, // longitude first, then latitude
        geometryType: 'esriGeometryPoint',
        inSR: '4326', // WGS84 coordinate system
        outFields: 'lganame', // We know the field name is 'lganame'
        f: 'json'
      });
      
      const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
      
      if (lgaResponse.ok) {
        const lgaData = await lgaResponse.json();
        
        if (lgaData.features && lgaData.features.length > 0) {
          const lgaName = lgaData.features[0].attributes.lganame;
          console.log('LGA from Layer 8:', lgaName);
          
          // Format the LGA name to title case for consistency
          if (lgaName) {
            // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
            const formattedLgaName = lgaName.toLowerCase().split(' ').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
            
            console.log('Formatted LGA name:', formattedLgaName);
            return { source: 'Layer 8', lga: formattedLgaName, coordinates: { lat, lng } };
          }
        } else {
          console.log('No LGA information found from Layer 8 API');
        }
      } else {
        console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
      }
    } else {
      console.error('Geocoding failed:', geocodeData);
    }
  } catch (error) {
    console.error('Error testing Layer 8 LGA detection:', error);
  }
  
  return { source: 'Layer 8', lga: null, coordinates: null };
}

// Test WFS LGA detection (used in NSWPlanningMap.js)
async function testWFSLGADetection(address, lat, lng) {
  try {
    console.log(`Testing WFS for address: ${address}`);
    
    // WFS endpoint for LGA
    const lgaUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer`;
    
    // This is a simplified version - the actual WFS request would be more complex
    // and would require special handling for XML/GML format
    console.log('WFS endpoints require special handling, using simplified test');
    
    // For testing purposes, we'll make a direct request to a similar REST endpoint
    const restUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/EDP/Administrative_Boundaries/MapServer/1/query`;
    
    const restParams = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: 'LGA_NAME',
      f: 'json'
    });
    
    const restResponse = await fetch(`${restUrl}?${restParams.toString()}`);
    
    if (restResponse.ok) {
      const restData = await restResponse.json();
      
      if (restData.features && restData.features.length > 0) {
        const lgaName = restData.features[0].attributes.LGA_NAME;
        console.log('LGA from WFS (REST):', lgaName);
        return { source: 'WFS', lga: lgaName, coordinates: { lat, lng } };
      } else {
        console.log('No LGA information found from WFS (REST)');
      }
    } else {
      console.error('WFS (REST) response not OK:', restResponse.statusText);
    }
  } catch (error) {
    console.error('Error testing WFS LGA detection:', error);
  }
  
  return { source: 'WFS', lga: null, coordinates: { lat, lng } };
}

// Test text extraction LGA detection (used in NSWPartyPlanningUpdated.tsx)
function testTextExtractionLGADetection(address) {
  console.log(`Testing text extraction for address: ${address}`);
  
  // Common LGA patterns in addresses
  const lgaPatterns = [
    /City of ([A-Za-z\s]+)(?:,|$)/i,       // City of Sydney
    /([A-Za-z\s]+) Council(?:,|$)/i,       // Randwick Council
    /([A-Za-z\s]+) Shire(?:,|$)/i,         // Sutherland Shire
    /([A-Za-z\s]+) Municipal(?:,|$)/i      // Woollahra Municipal
  ];
  
  // Check if the address contains any LGA patterns
  for (const pattern of lgaPatterns) {
    const match = address.match(pattern);
    if (match && match[1]) {
      const lgaName = match[1].trim();
      console.log('LGA from text extraction:', lgaName);
      return { source: 'Text Extraction', lga: lgaName };
    }
  }
  
  // Suburb to LGA mapping (simplified version)
  const suburbToLGA = {
    'sydney': 'City of Sydney',
    'newtown': 'Inner West Council',
    'parramatta': 'City of Parramatta',
    'north parramatta': 'City of Parramatta',
    'harris park': 'City of Parramatta',
    'epping': 'City of Parramatta',
    'carlingford': 'City of Parramatta',
    'eastwood': 'City of Ryde',
    'doonside': 'Blacktown City Council',
    'glendenning': 'Blacktown City Council',
    // Note: Dural can be in either The Hills Shire or Hornsby Shire
  };
  
  // Extract suburb from address
  const addressLower = address.toLowerCase();
  for (const [suburb, lga] of Object.entries(suburbToLGA)) {
    if (addressLower.includes(suburb)) {
      console.log(`LGA from suburb mapping (${suburb}):`, lga);
      return { source: 'Text Extraction (Suburb Mapping)', lga };
    }
  }
  
  console.log('No LGA found from text extraction');
  return { source: 'Text Extraction', lga: null };
}

// Run the tests
async function runTests() {
  console.log('Testing component accuracy...\n');
  
  const results = [];
  
  for (const address of testAddresses) {
    console.log(`\n=== Testing address: ${address} ===`);
    
    // Test Layer 8
    const layer8Result = await testLayer8LGADetection(address);
    
    // Test WFS (if we have coordinates from Layer 8)
    let wfsResult = { source: 'WFS', lga: null, coordinates: null };
    if (layer8Result.coordinates) {
      wfsResult = await testWFSLGADetection(
        address,
        layer8Result.coordinates.lat,
        layer8Result.coordinates.lng
      );
    }
    
    // Test text extraction
    const textResult = testTextExtractionLGADetection(address);
    
    // Compare results
    console.log('\nComparison:');
    console.log(`Layer 8: ${layer8Result.lga || 'Not found'}`);
    console.log(`WFS: ${wfsResult.lga || 'Not found'}`);
    console.log(`Text Extraction: ${textResult.lga || 'Not found'}`);
    
    // Determine if results match
    const match = (layer8Result.lga && wfsResult.lga && layer8Result.lga === wfsResult.lga) ||
                 (layer8Result.lga && textResult.lga && layer8Result.lga === textResult.lga) ||
                 (wfsResult.lga && textResult.lga && wfsResult.lga === textResult.lga);
    
    console.log(`Match: ${match ? 'YES' : 'NO'}`);
    
    // Store results
    results.push({
      address,
      layer8: layer8Result.lga,
      wfs: wfsResult.lga,
      text: textResult.lga,
      match
    });
    
    console.log('='.repeat(50));
  }
  
  // Summary
  console.log('\n=== Summary ===');
  console.log('Address | Layer 8 | WFS | Text Extraction | Match');
  console.log('-'.repeat(80));
  
  for (const result of results) {
    console.log(`${result.address.substring(0, 20)}... | ${result.layer8 || 'Not found'} | ${result.wfs || 'Not found'} | ${result.text || 'Not found'} | ${result.match ? 'YES' : 'NO'}`);
  }
  
  // Count matches and mismatches
  const matches = results.filter(r => r.match).length;
  const mismatches = results.filter(r => !r.match).length;
  
  console.log('\nMatches:', matches);
  console.log('Mismatches:', mismatches);
  console.log('Match rate:', `${(matches / results.length * 100).toFixed(2)}%`);
}

// Run the tests
runTests().catch(console.error);
