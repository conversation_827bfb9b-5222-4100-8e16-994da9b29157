/**
 * Clerk-Supabase JWT Template Utility
 *
 * This file provides information about the proper JWT template configuration
 * for integrating Clerk with Supabase.
 */

/**
 * The recommended JWT template for Supabase integration
 * This should be configured in the Clerk Dashboard under JWT Templates
 */
export const RECOMMENDED_SUPABASE_TEMPLATE = {
  "aud": "authenticated",
  "role": "authenticated",
  "sub": "{{user.external_id}}",
  "email": "{{user.primary_email_address}}",
  "app_metadata": {
    "provider": "clerk"
  },
  "user_metadata": {
    "id": "{{user.id}}",
    "first_name": "{{user.first_name}}",
    "last_name": "{{user.last_name}}",
    "email": "{{user.primary_email_address}}",
    "image_url": "{{user.image_url}}",
    "clerk_id": "{{user.id}}",
    "created_at": "{{user.created_at}}",
    "updated_at": "{{user.updated_at}}"
  },
  "exp": "{{jwt.exp}}"
};

/**
 * Test function to decode and validate a JWT token
 */
export function decodeJWT(token: string) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    const header = JSON.parse(atob(parts[0]));
    const payload = JSON.parse(atob(parts[1]));

    return {
      header,
      payload,
      isValid: true,
      hasExternalId: !!payload.sub && payload.sub !== payload.clerk_id,
      externalId: payload.sub,
      clerkId: payload.user_metadata?.clerk_id || payload.clerk_id
    };
  } catch (error) {
    return {
      header: null,
      payload: null,
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test function to check if the current user's JWT is working
 */
export async function testClerkSupabaseToken() {
  try {
    // Get token from Clerk
    if (typeof window !== 'undefined' && window.Clerk?.session) {
      const token = await window.Clerk.session.getToken({ template: 'supabase' });

      if (!token) {
        return {
          success: false,
          error: 'No token received from Clerk'
        };
      }

      const decoded = decodeJWT(token);

      return {
        success: decoded.isValid,
        token: token.substring(0, 20) + '...',
        decoded,
        hasExternalId: decoded.hasExternalId,
        externalId: decoded.externalId
      };
    } else {
      return {
        success: false,
        error: 'Clerk not available'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Instructions for setting up the JWT template in Clerk
 */
export const SETUP_INSTRUCTIONS = `
To properly configure Clerk to work with Supabase:

1. Go to the Clerk Dashboard (https://dashboard.clerk.dev/)
2. Select your application
3. Navigate to "JWT Templates" in the sidebar
4. Click on the "supabase" template (or create a new one named "supabase")
5. Replace the template with the recommended JSON
6. Click "Save" to update the template
`;

/**
 * Check if a JWT token has the necessary claims for Supabase
 * @param token The JWT token to check
 * @returns An object with validation results
 */
export function validateSupabaseToken(token: string): { 
  valid: boolean; 
  missingClaims: string[];
  message: string;
} {
  try {
    // Decode the token (simple decode, not verification)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return { 
        valid: false, 
        missingClaims: ['invalid_format'],
        message: 'Invalid JWT format' 
      };
    }

    // Decode the payload
    const payload = JSON.parse(atob(parts[1]));
    
    // Check for required claims
    const requiredClaims = ['aud', 'role', 'sub', 'email', 'exp'];
    const missingClaims = requiredClaims.filter(claim => !payload[claim]);
    
    if (missingClaims.length > 0) {
      return {
        valid: false,
        missingClaims,
        message: `Missing required claims: ${missingClaims.join(', ')}`
      };
    }
    
    // Check specific values
    if (payload.aud !== 'authenticated') {
      return {
        valid: false,
        missingClaims: [],
        message: 'The "aud" claim must be "authenticated"'
      };
    }
    
    if (payload.role !== 'authenticated') {
      return {
        valid: false,
        missingClaims: [],
        message: 'The "role" claim must be "authenticated"'
      };
    }
    
    return {
      valid: true,
      missingClaims: [],
      message: 'Token is valid for Supabase'
    };
  } catch (error) {
    return {
      valid: false,
      missingClaims: ['parse_error'],
      message: `Error parsing token: ${error}`
    };
  }
}

export default {
  RECOMMENDED_SUPABASE_TEMPLATE,
  SETUP_INSTRUCTIONS,
  validateSupabaseToken
};
