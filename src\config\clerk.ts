// Clerk configuration for HouseGoing authentication

export const isDevelopmentMode = () => {
  return import.meta.env.DEV || process.env.NODE_ENV === 'development';
};

export const CLERK_CONFIG = {
  developmentMode: isDevelopmentMode(),
  publishableKey: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY || '',

  // Redirect URLs after authentication
  afterSignInURL: '/',
  afterSignUpURL: '/',
  signInRedirectURL: '/',
  signUpRedirectURL: '/',

  // Host-specific redirect URLs
  hostSignInRedirectURL: '/host/dashboard',
  hostSignUpRedirectURL: '/host/dashboard',

  // Fallback redirect
  fallbackRedirectUrl: '/',

  // Sign-in/up paths
  signInPath: '/login',
  signUpPath: '/signup',
  hostSignInPath: '/host/login',
  hostSignUpPath: '/host/signup',
};

export default CLERK_CONFIG;
