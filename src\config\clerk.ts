// Legacy Clerk configuration - replaced with Supa<PERSON> Auth
// This file exists for backward compatibility

export const isDevelopmentMode = () => {
  return import.meta.env.DEV || process.env.NODE_ENV === 'development';
};

// Legacy exports for backward compatibility
export const CLERK_CONFIG = {
  developmentMode: isDevelopmentMode(),
  publishableKey: '',
  fallbackRedirectUrl: '/'
};

export default CLERK_CONFIG;
