# Hybrid Auth.js + Supabase Authentication Setup

This guide explains how to set up a hybrid authentication system using Auth.js for Google OAuth and Supabase for email/magic link authentication.

## 🏗️ Architecture Overview

- **Auth.js**: Handles Google OAuth login (free)
- **Supabase**: Handles email/magic link login and stores user data
- **Express Server**: Backend server for Auth.js endpoints
- **React Frontend**: Unified login experience

## 📋 Prerequisites

1. Google Cloud Console project with OAuth 2.0 credentials
2. Supabase project with database access
3. Node.js 18+ and npm

## 🔧 Environment Variables

Create/update your `.env` file with these variables:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key

# Auth.js Configuration (Backend Server)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
AUTH_SECRET=your-random-secret-key
AUTH_SERVER_PORT=3001
FRONTEND_URL=http://localhost:5175

# Frontend Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_AUTH_SERVER_URL=http://localhost:3001
```

## 🗄️ Database Setup

1. Run the SQL migration to create the customers table:

```sql
-- Execute this in your Supabase SQL editor
-- File: database/create-customers-table.sql

CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies and triggers (see full file for details)
```

2. Disable Supabase Google Auth provider to avoid conflicts:
   - Go to Supabase Dashboard > Authentication > Providers
   - Disable Google provider

## 🔑 Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Create OAuth 2.0 Client ID or use existing one
4. Add these authorized redirect URIs:
   - `http://localhost:3001/auth/callback/google` (development)
   - `https://your-domain.com/auth/callback/google` (production)

## 🚀 Running the Application

### Development Mode

1. **Start both servers:**
   ```bash
   npm run dev:full
   ```
   This runs both the Vite dev server (port 5175) and Auth server (port 3001)

2. **Or run separately:**
   ```bash
   # Terminal 1: Frontend
   npm run dev

   # Terminal 2: Auth Server
   npm run auth-server
   ```

### Production Mode

1. **Build frontend:**
   ```bash
   npm run build
   ```

2. **Deploy auth server** to your hosting platform
3. **Update environment variables** for production URLs

## 🔐 Security Best Practices

1. **Never expose service role key** in frontend code
2. **Use HTTPS** in production
3. **Validate JWT tokens** on server-side
4. **Enable RLS** on Supabase tables
5. **Use secure session cookies** in production

## 📱 Usage Examples

### Google Login Button
```tsx
import GoogleLoginButton from './components/auth/GoogleLoginButton';

function LoginPage() {
  return (
    <GoogleLoginButton>
      Sign in with Google
    </GoogleLoginButton>
  );
}
```

### Email Login Form
```tsx
import EmailLoginForm from './components/auth/EmailLoginForm';

function LoginPage() {
  return (
    <EmailLoginForm 
      onSuccess={() => console.log('Magic link sent!')}
    />
  );
}
```

### Using Auth Context
```tsx
import { useAuth } from './providers/AuthProvider';

function Dashboard() {
  const { 
    isAuthenticated, 
    user, 
    authProvider, 
    customerData,
    signOut 
  } = useAuth();

  if (!isAuthenticated) {
    return <div>Please sign in</div>;
  }

  return (
    <div>
      <h1>Welcome {user?.name || user?.email}</h1>
      <p>Provider: {authProvider}</p>
      <p>Customer ID: {customerData?.id}</p>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

## 🔄 Authentication Flow

### Google OAuth Flow
1. User clicks "Sign in with Google"
2. Redirects to `http://localhost:3001/auth/signin/google`
3. Auth.js handles Google OAuth
4. On success, creates/updates customer record in Supabase
5. Redirects back to frontend with session

### Email Magic Link Flow
1. User enters email and clicks "Send magic link"
2. Supabase sends magic link email
3. User clicks link in email
4. Supabase handles authentication
5. Redirects to frontend with session

## 🐛 Troubleshooting

### Common Issues

1. **CORS errors**: Check AUTH_SERVER_URL and FRONTEND_URL match
2. **Google OAuth fails**: Verify redirect URIs in Google Console
3. **Database errors**: Ensure customers table exists and RLS is configured
4. **Session not persisting**: Check cookie settings and HTTPS in production

### Debug Mode

Enable debug logging by adding to your auth server:
```javascript
console.log('Auth event:', { event, session: !!session });
```

## 📚 Additional Resources

- [Auth.js Documentation](https://authjs.dev/)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth 2.0 Guide](https://developers.google.com/identity/protocols/oauth2)
