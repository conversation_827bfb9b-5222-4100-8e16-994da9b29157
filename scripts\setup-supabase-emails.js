/**
 * Supabase Email Templates Setup Script
 * 
 * This script sets up custom email templates for Supabase authentication.
 * 
 * To use this script:
 * 1. Install the Supabase JS client: npm install @supabase/supabase-js
 * 2. Set your Supabase URL and service role key as environment variables:
 *    - SUPABASE_URL
 *    - SUPABASE_SERVICE_ROLE_KEY
 * 3. Run the script: node setup-supabase-emails.js
 */

const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL || 'https://fxqoowlruissctsgbljk.supabase.co';
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceRoleKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Create Supabase client with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Email templates
const emailTemplates = {
  // Confirmation email template
  confirmation: {
    subject: 'Confirm your HouseGoing account',
    content: `
      <h2>Welcome to HouseGoing!</h2>
      <p>Thank you for signing up. Please confirm your email address by clicking the button below:</p>
      <p>
        <a href="{{ .ConfirmationURL }}" style="display: inline-block; color: white; background: linear-gradient(to right, #8A2BE2, #FF69B4); border-radius: 4px; padding: 12px 24px; text-decoration: none; font-weight: bold;">
          Confirm Your Email
        </a>
      </p>
      <p>Or copy and paste this link into your browser:</p>
      <p>{{ .ConfirmationURL }}</p>
      <p>Your verification code is: <strong>123456</strong></p>
      <p>If you didn't sign up for HouseGoing, you can safely ignore this email.</p>
      <p>Thanks,<br>The HouseGoing Team</p>
    `,
  },
  
  // Magic link email template
  magiclink: {
    subject: 'Your HouseGoing login link',
    content: `
      <h2>Sign In to HouseGoing</h2>
      <p>Click the button below to sign in to your HouseGoing account:</p>
      <p>
        <a href="{{ .SiteURL }}/auth/callback?token={{ .Token }}&type=magiclink&redirect_to={{ .RedirectTo }}" style="display: inline-block; color: white; background: linear-gradient(to right, #8A2BE2, #FF69B4); border-radius: 4px; padding: 12px 24px; text-decoration: none; font-weight: bold;">
          Sign In
        </a>
      </p>
      <p>Or copy and paste this link into your browser:</p>
      <p>{{ .SiteURL }}/auth/callback?token={{ .Token }}&type=magiclink&redirect_to={{ .RedirectTo }}</p>
      <p>If you didn't request this email, you can safely ignore it.</p>
      <p>Thanks,<br>The HouseGoing Team</p>
    `,
  },
  
  // Password recovery email template
  recovery: {
    subject: 'Reset your HouseGoing password',
    content: `
      <h2>Reset Your HouseGoing Password</h2>
      <p>Click the button below to reset your password:</p>
      <p>
        <a href="{{ .SiteURL }}/auth/callback?token={{ .Token }}&type=recovery&redirect_to={{ .RedirectTo }}" style="display: inline-block; color: white; background: linear-gradient(to right, #8A2BE2, #FF69B4); border-radius: 4px; padding: 12px 24px; text-decoration: none; font-weight: bold;">
          Reset Password
        </a>
      </p>
      <p>Or copy and paste this link into your browser:</p>
      <p>{{ .SiteURL }}/auth/callback?token={{ .Token }}&type=recovery&redirect_to={{ .RedirectTo }}</p>
      <p>If you didn't request a password reset, you can safely ignore this email.</p>
      <p>Thanks,<br>The HouseGoing Team</p>
    `,
  },
  
  // Email change confirmation template
  email_change: {
    subject: 'Confirm your new HouseGoing email',
    content: `
      <h2>Confirm Your New Email</h2>
      <p>Click the button below to confirm your new email address for HouseGoing:</p>
      <p>
        <a href="{{ .ConfirmationURL }}" style="display: inline-block; color: white; background: linear-gradient(to right, #8A2BE2, #FF69B4); border-radius: 4px; padding: 12px 24px; text-decoration: none; font-weight: bold;">
          Confirm New Email
        </a>
      </p>
      <p>Or copy and paste this link into your browser:</p>
      <p>{{ .ConfirmationURL }}</p>
      <p>If you didn't request this change, please contact support immediately.</p>
      <p>Thanks,<br>The HouseGoing Team</p>
    `,
  },
};

// Update email templates
async function updateEmailTemplates() {
  try {
    // Update site URL
    const { error: siteUrlError } = await supabase.auth.admin.updateConfig({
      site_url: 'https://housegoing.com.au',
    });
    
    if (siteUrlError) {
      console.error('Error updating site URL:', siteUrlError);
    } else {
      console.log('Site URL updated successfully');
    }
    
    // Update email templates
    for (const [templateName, template] of Object.entries(emailTemplates)) {
      const { error } = await supabase.auth.admin.updateEmailTemplate({
        template_name: templateName,
        subject: template.subject,
        content: template.content,
        sender_name: 'HouseGoing Team',
      });
      
      if (error) {
        console.error(`Error updating ${templateName} template:`, error);
      } else {
        console.log(`${templateName} template updated successfully`);
      }
    }
    
    console.log('Email templates setup complete');
  } catch (error) {
    console.error('Error setting up email templates:', error);
  }
}

// Run the script
updateEmailTemplates();
