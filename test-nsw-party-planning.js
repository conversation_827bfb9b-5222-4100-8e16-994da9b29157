/**
 * Test NSW Party Planning & Noise Guide
 * 
 * This script tests the NSW Party Planning & Noise Guide API
 * to verify that the LGA and zoning detection is working correctly.
 */

// Test addresses
const testAddresses = [
  {
    address: '10 Darvall Road, Eastwood, NSW 2122',
    expectedLGA: 'City of Parramatta',
    expectedZone: 'R2'
  },
  {
    address: '123 King Street, Newtown, NSW 2042',
    expectedLGA: 'Inner West Council',
    expectedZone: 'B2'
  },
  {
    address: '456 George Street, Sydney, NSW 2000',
    expectedLGA: 'City of Sydney',
    expectedZone: 'B8'
  },
  {
    address: '789 Glendenning Road, Glendenning, NSW 2761',
    expectedLGA: 'City of Blacktown',
    expectedZone: 'IN1'
  },
  {
    address: '321 Hemers Road, Dural, NSW 2158',
    expectedLGA: 'The Hills Shire Council',
    expectedZone: 'RU2'
  }
];

// Test the NSW Party Planning & Noise Guide API
async function testNSWPartyPlanningAPI() {
  console.log('Testing NSW Party Planning & Noise Guide API...');
  
  for (const test of testAddresses) {
    console.log(`\nTesting address: ${test.address}`);
    
    try {
      // First, geocode the address
      const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(test.address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;
      
      const geocodeResponse = await fetch(geocodeUrl);
      const geocodeData = await geocodeResponse.json();
      
      if (geocodeData.features && geocodeData.features.length > 0) {
        const [lng, lat] = geocodeData.features[0].center;
        console.log(`Geocoded coordinates: ${lat}, ${lng}`);
        
        // Now call the NSW Zoning API
        const apiUrl = '/api/nsw-zoning';
        const apiResponse = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            address: test.address,
            lat,
            lng
          })
        });
        
        if (apiResponse.ok) {
          const apiData = await apiResponse.json();
          console.log('API response:', apiData);
          
          // Check if the LGA matches the expected value
          const lgaMatches = apiData.lga === test.expectedLGA;
          console.log(`LGA match: ${lgaMatches ? 'YES' : 'NO'} (got "${apiData.lga}", expected "${test.expectedLGA}")`);
          
          // Check if the zoning matches the expected value
          const zoneMatches = apiData.zoning && apiData.zoning.startsWith(test.expectedZone);
          console.log(`Zoning match: ${zoneMatches ? 'YES' : 'NO'} (got "${apiData.zoning}", expected "${test.expectedZone}*")`);
        } else {
          console.error('API error:', await apiResponse.text());
        }
      } else {
        console.error('Geocoding failed:', geocodeData);
      }
    } catch (error) {
      console.error('Test error:', error);
    }
  }
}

// Run the test
testNSWPartyPlanningAPI().catch(console.error);
