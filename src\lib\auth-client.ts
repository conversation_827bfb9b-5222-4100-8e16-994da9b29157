// Simple Auth.js client for Vite/React
export interface User {
  id?: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

export interface Session {
  user?: User;
  expires: string;
}

class AuthClient {
  private baseUrl = '/api/auth';

  async signIn(provider: string, options?: { callbackUrl?: string; redirect?: boolean }) {
    const params = new URLSearchParams({
      provider,
      callbackUrl: options?.callbackUrl || window.location.origin,
    });

    if (options?.redirect !== false) {
      window.location.href = `${this.baseUrl}/signin/${provider}?${params}`;
      return;
    }

    const response = await fetch(`${this.baseUrl}/signin/${provider}?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.json();
  }

  async signOut(options?: { callbackUrl?: string; redirect?: boolean }) {
    const params = new URLSearchParams({
      callbackUrl: options?.callbackUrl || window.location.origin,
    });

    if (options?.redirect !== false) {
      window.location.href = `${this.baseUrl}/signout?${params}`;
      return;
    }

    const response = await fetch(`${this.baseUrl}/signout?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.json();
  }

  async getSession(): Promise<Session | null> {
    try {
      const response = await fetch(`${this.baseUrl}/session`, {
        credentials: 'include',
      });

      if (!response.ok) {
        return null;
      }

      const session = await response.json();
      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  async getCsrfToken(): Promise<string | null> {
    try {
      const response = await fetch(`${this.baseUrl}/csrf`);
      const data = await response.json();
      return data.csrfToken;
    } catch (error) {
      console.error('Error getting CSRF token:', error);
      return null;
    }
  }
}

export const authClient = new AuthClient();

// Helper functions to match NextAuth.js API
export const signIn = (provider: string, options?: { callbackUrl?: string; redirect?: boolean }) => 
  authClient.signIn(provider, options);

export const signOut = (options?: { callbackUrl?: string; redirect?: boolean }) => 
  authClient.signOut(options);

export const getSession = () => authClient.getSession();
