import React from 'react';
import { Link } from 'react-router-dom';
import { X, LogOut, User } from 'lucide-react';
import { useSupabase } from '../../providers/SupabaseProvider';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
}

export default function MobileMenu({ isOpen, onClose, currentPath }: MobileMenuProps) {
  const { isAuthenticated, userProfile, isLoading, signOut } = useSupabase();

  // Don't render anything if the menu is not open
  if (!isOpen) return null;

  // Define menu items at the top of the component
  const menuItems = [
    { path: '/find-venues', label: 'Find Venues', className: '' },
    { path: '/venue-guide', label: 'Venue Guide', className: '' },
    { path: '/nsw-party-planning', label: 'NSW Party Planning', className: '' },
  ];

  // Quick links (moved from main nav)
  const quickLinks = [
    { path: '/how-it-works', label: 'How It Works', className: 'text-gray-500 text-sm' },
    { path: '/venue-assistant', label: 'Venue Assistant', className: 'text-gray-500 text-sm' },
    { path: '/host/portal', label: 'Owner Portal', className: 'text-gray-500 text-sm' },
  ];

  // Log when menu is opened
  React.useEffect(() => {
    if (isOpen) {
      console.log('Mobile menu opened - Menu items:', menuItems);
    }
  }, [isOpen, menuItems]);

  return (
    <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-50 mobile-menu">
      <div className="fixed inset-y-0 right-0 w-80 sm:w-64 bg-white shadow-lg mobile-menu-content">
        <div className="p-4 flex justify-between items-center border-b min-h-[60px]">
          <h2 className="font-semibold text-lg">Menu</h2>
          <button
            onClick={onClose}
            className="btn-reactive focus-enhanced p-2 rounded-lg hover:bg-gray-100 touch-target"
            aria-label="Close menu"
          >
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>
        <nav className="p-4 section-mobile">
          {menuItems.map(({ path, label, className }) => (
            <Link
              key={path}
              to={path}
              className={`nav-link btn-reactive focus-enhanced block py-3 px-4 rounded-lg mb-2 min-h-[44px] flex items-center text-base font-medium touch-target ${
                currentPath === path
                  ? 'bg-purple-50 text-purple-600 border-l-4 border-purple-600'
                  : className || 'text-gray-600 hover:bg-gray-50 active:bg-purple-50'
              } ${className || ''}`}
              onClick={onClose}
            >
              {label}
            </Link>
          ))}

          {/* Quick Links Section */}
          <div className="border-t border-gray-200 my-4 pt-4">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-3 px-4">Quick Links</h3>
            {quickLinks.map(({ path, label, className }) => (
              <Link
                key={path}
                to={path}
                className={`nav-link btn-reactive focus-enhanced block py-3 px-4 rounded-lg mb-2 min-h-[44px] flex items-center text-sm touch-target ${
                  currentPath === path
                    ? 'bg-purple-50 text-purple-600'
                    : className || 'text-gray-500 hover:bg-gray-50 active:bg-purple-50'
                } ${className || ''}`}
                onClick={onClose}
              >
                {label}
              </Link>
            ))}
          </div>

          <div className="border-t border-gray-200 my-4 pt-4">
            {isLoading ? (
              <div className="py-2 px-4">
                <div className="animate-pulse bg-gray-200 h-8 w-full rounded mb-2"></div>
                <div className="animate-pulse bg-gray-200 h-8 w-full rounded"></div>
              </div>
            ) : isAuthenticated && userProfile ? (
              <>
                <div className="py-2 px-4 text-gray-700 font-medium">
                  {userProfile.first_name || userProfile.email}
                </div>
                <Link
                  to="/my-account"
                  className="flex items-center py-2 px-4 rounded-lg mb-2 text-gray-600 hover:bg-gray-50"
                  onClick={onClose}
                >
                  <User className="w-4 h-4 mr-2" />
                  My Account
                </Link>
                <button
                  onClick={() => {
                    signOut();
                    onClose();
                  }}
                  className="flex items-center w-full text-left py-2 px-4 rounded-lg mb-2 text-purple-600 hover:bg-purple-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </button>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="flex items-center py-2 px-4 rounded-lg mb-2 text-gray-600 hover:bg-gray-50"
                  onClick={onClose}
                >
                  <User className="w-4 h-4 mr-2" />
                  Sign In
                </Link>
                <Link
                  to="/signup"
                  className="block py-2 px-4 rounded-lg mb-2 text-purple-600 hover:bg-purple-50"
                  onClick={onClose}
                >
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      </div>
    </div>
  );
}