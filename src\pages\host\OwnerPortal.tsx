import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import { useUser } from '../../hooks/useClerkCompat';
import { Building, Key, DollarSign, Users } from 'lucide-react';
import RegisterAsHost from '../../components/host/RegisterAsHost';
import HostPortalAuth from '../../components/auth/HostPortalAuth';

export default function OwnerPortal() {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const navigate = useNavigate();
  // No longer need showDirectOAuth state as it's handled in the HostPortalAuth component

  // Simplified check for host status
  const userIsHost = user && user.publicMetadata?.role === 'host';

  // Set a timeout to prevent infinite loading
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    if (!isLoaded) {
      const timeoutId = setTimeout(() => {
        setIsCheckingAuth(false);
      }, 3000); // 3 second timeout

      return () => clearTimeout(timeoutId);
    } else {
      setIsCheckingAuth(false);
    }
  }, [isLoaded]);

  // Simplified loading state with timeout
  if (!isLoaded && isCheckingAuth) {
    return <div className="pt-32 flex justify-center">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
        <p className="text-gray-600">Loading owner portal...</p>
      </div>
    </div>;
  }

  return (
    <div className="pt-24 px-4 sm:px-6 bg-gray-50 min-h-screen">
      <div className="max-w-3xl mx-auto">
        {!isSignedIn ? (
          <>
            <div className="mb-12 text-center">
              <h1 className="text-3xl font-semibold text-gray-800 mb-3">Earn $1,200+ Monthly With Your Space</h1>
              <p className="text-gray-600 text-lg max-w-xl mx-auto">
                List your property. Set your hours. Get paid.
              </p>
            </div>

            <div className="bg-white rounded-md border border-gray-200 p-8 mb-12 shadow-sm">
              <div className="flex flex-col md:flex-row gap-8 mb-8">
                <div className="flex-1">
                  <div className="text-gray-800 text-xl font-medium mb-6">Host Benefits</div>
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <div className="mt-1 mr-3 flex-shrink-0 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <div>
                        <span className="font-medium">Higher hourly rates</span>
                        <span className="text-gray-600 block text-sm">3x more than traditional rentals</span>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="mt-1 mr-3 flex-shrink-0 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <div>
                        <span className="font-medium">Complete control</span>
                        <span className="text-gray-600 block text-sm">You set availability & house rules</span>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="mt-1 mr-3 flex-shrink-0 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <div>
                        <span className="font-medium">Verified guests only</span>
                        <span className="text-gray-600 block text-sm">ID verification & security deposits</span>
                      </div>
                    </li>
                  </ul>
                </div>

                <div className="flex-1">
                  <div className="text-gray-800 text-xl font-medium mb-6">What Hosts Say</div>
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
                    <p className="text-gray-700 italic mb-3">"I've made over $15,000 in my first year with just weekend bookings. The platform handles everything."</p>
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gray-300 rounded-full mr-2"></div>
                      <div>
                        <div className="text-sm font-medium">Sarah T.</div>
                        <div className="text-xs text-gray-500">Sydney Host, 1 year</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <HostPortalAuth />
            </div>
          </>
        ) : userIsHost ? (
          <div className="bg-white rounded-md border border-gray-200 p-8 shadow-sm">
            <h2 className="text-2xl font-medium text-gray-800 mb-8">Welcome Back</h2>

            <div className="space-y-4 mb-6">
              <Link
                to="/host/dashboard"
                className="block w-full bg-white hover:bg-gray-50 p-4 rounded-md border border-gray-200 transition-colors"
              >
                <div className="flex items-center">
                  <div className="mr-4 p-2 bg-blue-50 rounded-md">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800">Dashboard</h3>
                    <p className="text-gray-500 text-sm">View your earnings and booking statistics</p>
                  </div>
                </div>
              </Link>

              <Link
                to="/list-space"
                className="block w-full bg-white hover:bg-gray-50 p-4 rounded-md border border-gray-200 transition-colors"
              >
                <div className="flex items-center">
                  <div className="mr-4 p-2 bg-blue-50 rounded-md">
                    <Key className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800">List a New Property</h3>
                    <p className="text-gray-500 text-sm">Add a new venue to your listings</p>
                  </div>
                </div>
              </Link>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">Need help? Contact support</div>
                <Link to="/host/settings" className="text-sm text-blue-600 hover:text-blue-800">Account Settings</Link>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-md border border-gray-200 p-8 shadow-sm">
            <h2 className="text-2xl font-medium text-gray-800 mb-4">Become a Host</h2>
            <p className="text-gray-600 mb-8">
              You're signed in, but you need to register as a host to list your venues.
            </p>

            <div className="mb-8 bg-blue-50 p-4 rounded-md border border-blue-100">
              <div className="flex items-start">
                <div className="mr-3 mt-1">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-1">Earn $1,200+ Monthly</h3>
                  <p className="text-gray-600 text-sm">Our hosts average $1,200 per month with just weekend bookings.</p>
                </div>
              </div>
            </div>

            <RegisterAsHost />
          </div>
        )}
      </div>
    </div>
  );
}
