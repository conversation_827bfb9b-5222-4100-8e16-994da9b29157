-- Analytics Tables for Suburb Tracking
-- Run this in Supabase SQL Editor

-- 1. Search Analytics Table
CREATE TABLE IF NOT EXISTS search_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT, -- Clerk user ID (nullable for anonymous users)
    ip_address INET NOT NULL, -- User's IP address for deduplication
    search_query TEXT NOT NULL, -- What they searched for
    suburb TEXT, -- Extracted suburb from search
    state TEXT, -- Extracted state (NSW, VIC, etc.)
    search_filters JSONB, -- Any filters applied (date, capacity, etc.)
    results_count INTEGER DEFAULT 0, -- Number of results returned
    user_agent TEXT, -- Browser/device info
    referrer TEXT, -- Where they came from
    session_id TEXT, -- Session identifier
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Booking Analytics Table
CREATE TABLE IF NOT EXISTS booking_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    booking_id UUID, -- Reference to actual booking
    user_id TEXT NOT NULL, -- Clerk user ID
    ip_address INET NOT NULL, -- User's IP address
    venue_id UUID NOT NULL, -- Which venue was booked
    venue_suburb TEXT NOT NULL, -- Venue's suburb
    venue_state TEXT NOT NULL, -- Venue's state
    booking_date DATE NOT NULL, -- When the event is happening
    booking_amount DECIMAL(10,2), -- How much they paid
    guest_count INTEGER, -- Number of guests
    booking_status TEXT DEFAULT 'pending', -- pending, confirmed, cancelled
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Suburb Popularity Aggregation Table (for faster queries)
CREATE TABLE IF NOT EXISTS suburb_popularity (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    suburb TEXT NOT NULL,
    state TEXT NOT NULL,
    search_count INTEGER DEFAULT 0, -- Total unique searches
    booking_count INTEGER DEFAULT 0, -- Total bookings
    unique_users INTEGER DEFAULT 0, -- Unique users who searched/booked
    unique_ips INTEGER DEFAULT 0, -- Unique IP addresses
    last_search_date TIMESTAMP WITH TIME ZONE,
    last_booking_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(suburb, state)
);

-- 4. User Session Tracking (to prevent spam/duplicate counting)
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT NOT NULL UNIQUE,
    user_id TEXT, -- Nullable for anonymous users
    ip_address INET NOT NULL,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    page_views INTEGER DEFAULT 1,
    searches_count INTEGER DEFAULT 0,
    bookings_count INTEGER DEFAULT 0,
    user_agent TEXT,
    referrer TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_search_analytics_suburb ON search_analytics(suburb);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at ON search_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_search_analytics_ip ON search_analytics(ip_address);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON search_analytics(user_id);

CREATE INDEX IF NOT EXISTS idx_booking_analytics_suburb ON booking_analytics(venue_suburb);
CREATE INDEX IF NOT EXISTS idx_booking_analytics_created_at ON booking_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_booking_analytics_user_id ON booking_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_booking_analytics_venue_id ON booking_analytics(venue_id);

CREATE INDEX IF NOT EXISTS idx_suburb_popularity_suburb ON suburb_popularity(suburb, state);
CREATE INDEX IF NOT EXISTS idx_suburb_popularity_search_count ON suburb_popularity(search_count DESC);
CREATE INDEX IF NOT EXISTS idx_suburb_popularity_booking_count ON suburb_popularity(booking_count DESC);

CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_ip ON user_sessions(ip_address);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);

-- Enable Row Level Security
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE suburb_popularity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
CREATE POLICY "Admin can view all search analytics" ON search_analytics
    FOR SELECT USING (true); -- Admins can see all data

CREATE POLICY "Admin can view all booking analytics" ON booking_analytics
    FOR SELECT USING (true);

CREATE POLICY "Admin can view suburb popularity" ON suburb_popularity
    FOR SELECT USING (true);

CREATE POLICY "Admin can view user sessions" ON user_sessions
    FOR SELECT USING (true);

-- Allow inserts for tracking (from the application)
CREATE POLICY "Allow search analytics inserts" ON search_analytics
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow booking analytics inserts" ON booking_analytics
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow suburb popularity updates" ON suburb_popularity
    FOR ALL USING (true);

CREATE POLICY "Allow user session tracking" ON user_sessions
    FOR ALL USING (true);
