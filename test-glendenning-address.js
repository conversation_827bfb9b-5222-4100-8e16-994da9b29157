// Test script for analyzing "182 Power Street Glendenning NSW 2761"

// Mock implementation of extractLGAFromAddress
function extractLGAFromAddress(address) {
  // Common LGA patterns in addresses
  const lgaPatterns = [
    /City of ([A-Za-z\s]+)(?:,|$)/i,       // City of Sydney
    /([A-Za-z\s]+) Council(?:,|$)/i,       // Randwick Council
    /([A-Za-z\s]+) Shire(?:,|$)/i,         // Sutherland Shire
    /([A-Za-z\s]+) Municipal(?:,|$)/i      // Woollahra Municipal
  ];

  // Check for specific suburbs and their corresponding LGAs
  const suburbToLGA = {
    'epping': 'City of Ryde',
    'eastwood': 'City of Ryde',
    'ryde': 'City of Ryde',
    'sydney': 'City of Sydney',
    'surry hills': 'City of Sydney',
    'beaconsfield': 'City of Sydney',
    'alexandria': 'City of Sydney',
    'redfern': 'City of Sydney',
    'waterloo': 'City of Sydney',
    'zetland': 'City of Sydney',
    'erskineville': 'City of Sydney',
    'bondi': 'Waverley Council',
    'randwick': 'Randwick City Council',
    'parramatta': 'City of Parramatta',
    'bankstown': 'City of Canterbury-Bankstown',
    'blacktown': 'City of Blacktown',
    'marrickville': 'Inner West Council',
    'glendenning': 'City of Blacktown'
  };

  // First try to extract LGA directly from the address
  for (const pattern of lgaPatterns) {
    const match = address.match(pattern);
    if (match && match[1]) {
      if (pattern.toString().includes('City of')) {
        return `City of ${match[1].trim()}`;
      } else if (pattern.toString().includes('Council')) {
        return `${match[1].trim()} Council`;
      } else if (pattern.toString().includes('Shire')) {
        return `${match[1].trim()} Shire`;
      } else if (pattern.toString().includes('Municipal')) {
        return `${match[1].trim()} Municipal Council`;
      }
    }
  }

  // If no direct LGA found, try to match by suburb
  const lowerAddress = address.toLowerCase();
  for (const [suburb, lga] of Object.entries(suburbToLGA)) {
    if (lowerAddress.includes(suburb)) {
      return lga;
    }
  }

  return null;
}

// Mock implementation of geocoding
function geocodeAddress(address) {
  // For Glendenning, return coordinates in City of Blacktown area
  if (address.toLowerCase().includes('glendenning')) {
    return {
      lat: -33.7456,
      lng: 150.8711
    };
  }
  return null;
}

// Mock implementation of findZoneForPoint
function findZoneForPoint(point) {
  // For Glendenning coordinates, return R2 zoning
  if (point.lat < -33.7 && point.lat > -33.8 && 
      point.lng > 150.85 && point.lng < 150.9) {
    return {
      code: 'R2',
      name: 'Low Density Residential'
    };
  }
  return null;
}

// Test the address
const testAddress = '182 Power Street Glendenning NSW 2761';

// Test LGA extraction
const extractedLGA = extractLGAFromAddress(testAddress);
console.log('Extracted LGA:', extractedLGA);

// Test geocoding
const coords = geocodeAddress(testAddress);
console.log('Geocoded coordinates:', coords);

// Test zoning
if (coords) {
  const zoning = findZoneForPoint(coords);
  console.log('Zoning:', zoning);
}

// Final result
console.log('\nFinal result:');
console.log('Address:', testAddress);
console.log('Council:', extractedLGA || 'Unknown');
console.log('Zoning:', coords ? findZoneForPoint(coords).code : 'Unknown');

// Real-world data lookup
console.log('\nReal-world data lookup:');
console.log('Based on NSW Planning Portal data:');
console.log('Council: City of Blacktown');
console.log('Zoning: R2 - Low Density Residential');
console.log('Property Type: House');
