import React, { useEffect, useState } from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';
import { Link, useLocation } from 'react-router-dom';
import { CLERK_CONFIG } from '../../config/clerk';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';
import OAuthButton from '../../components/auth/OAuthButton';
import GoogleButton from '../../components/auth/GoogleButton';
import { Building } from 'lucide-react';

export default function HostSignIn() {
  const [showDirectOAuth, setShowDirectOAuth] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Check for auth_error parameter in URL
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const hasAuthError = searchParams.get('auth_error') === 'true';

  // Set localStorage flag to indicate host sign-in
  useEffect(() => {
    // Set flag in localStorage
    localStorage.setItem('registering_as_host', 'true');
    console.log('HostSignIn: Set registering_as_host flag in localStorage');

    // Log configuration for debugging
    console.log('HostSignIn: Clerk config:', {
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      hostSignInRedirectURL: CLERK_CONFIG.hostSignInRedirectURL
    });

    // Simulate a webhook event for testing
    simulateWebhookEvent('page.viewed', {
      id: 'host-signin-page',
      page: 'host-signin'
    });

    // Check if we should show the direct OAuth button
    // This is a fallback in case the regular flow isn't working
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('direct') === 'true') {
      setShowDirectOAuth(true);
    }

    return () => {
      // Don't remove the flag on unmount, as we need it for the OAuth callback
    };
  }, []);
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-800 to-indigo-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* Owner badge */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg">
        <span className="text-purple-800 font-semibold flex items-center">
          <Building className="h-4 w-4 mr-2" /> Owner Portal Access
        </span>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <img
            className="mx-auto h-16 w-16 mb-2 bg-white p-2 rounded-full shadow-lg"
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
          />
        </div>
        <h2 className="text-center text-3xl font-bold text-white">
          Owner Portal{' '}
          <span className="bg-gradient-to-r from-pink-400 to-yellow-300 bg-clip-text text-transparent">
            HouseGoing
          </span>
        </h2>
        <p className="mt-2 text-center text-sm text-gray-200">
          Sign in to manage your venues and bookings
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-xl">
        <div className="bg-white/95 backdrop-blur-sm py-10 px-6 shadow-xl sm:rounded-xl sm:px-12 border border-purple-200">
          {showDirectOAuth ? (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900">Sign in as a Host</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose one of the following options to sign in to your host account
                </p>
              </div>

              <div className="space-y-3">
                <OAuthButton
                  provider="google"
                  registrationType="host"
                  className="w-full"
                  label="Sign in with Google"
                />

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or continue with Clerk</span>
                  </div>
                </div>

                <button
                  onClick={() => setShowDirectOAuth(false)}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Use Standard Sign In
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Host Portal</h1>
                <p className="text-lg text-gray-600">
                  Sign in to manage your venues and bookings
                </p>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>Host Portal</strong> - Manage your venues and earn money
                  </p>
                </div>

                {hasAuthError && (
                  <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
                    There was an issue with your sign-in. Please try again or use a different sign-in method.
                  </div>
                )}
              </div>

              <div className="mb-8">
                <p className="text-center text-base text-gray-600 mb-4">Sign in with:</p>
                <GoogleButton registrationType="host" label="Continue with Google" />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 py-1 bg-white text-gray-500 text-base">Or sign in with email</span>
                </div>
              </div>

              <ClerkSignIn
                appearance={clerkAppearance}
                routing="path"
                path="/host/login"
                forceRedirectUrl={CLERK_CONFIG.redirectUrls.hostSignIn}
                signUpUrl="/host/signup"
              />

              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowDirectOAuth(true)}
                  className="text-sm text-purple-600 hover:text-purple-800"
                >
                  Having trouble? Try direct sign in
                </button>
              </div>
            </>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/host/signup" className="text-purple-600 hover:text-purple-800 font-medium">
                Sign up
              </Link>
            </p>
          </div>

          {/* Customer Portal Link */}
          <div className="mt-8 text-center border-t border-gray-200 pt-6">
            <p className="text-sm text-gray-600 mb-2">
              Looking to book a venue instead?
            </p>
            <Link
              to="/login"
              className="inline-flex items-center px-4 py-2 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
            >
              Customer Sign In
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
