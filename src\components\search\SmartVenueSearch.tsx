import React, { useState, useEffect, useCallback } from 'react';
import { Search, Calendar, Users, MapPin, DollarSign, Clock, Sparkles, X } from 'lucide-react';
import { searchNSWSuburbs } from '../../services/nsw-suburbs';
import { trackSearchEvent } from '../../api/analytics';
import { useUserSafe } from '../../hooks/useClerkSafe';

// Enhanced mock venue data with more searchable properties
export const venues = [
  {
    id: 1,
    name: 'Harbour View Terrace',
    description: 'Stunning waterfront venue with panoramic harbour views and modern amenities.',
    location: 'Sydney Harbour',
    suburb: 'Circular Quay',
    capacity: { min: 20, max: 80 },
    pricePerHour: 250,
    features: ['waterfront', 'outdoor', 'deck', 'sound system', 'bar', 'harbour views', 'modern'],
    eventTypes: ['wedding', 'corporate', 'birthday', 'anniversary', 'engagement'],
    amenities: ['parking', 'catering', 'wifi', 'air conditioning', 'wheelchair accessible'],
    atmosphere: ['elegant', 'sophisticated', 'romantic', 'scenic'],
    timeSlots: ['morning', 'afternoon', 'evening', 'night'],
    image: '/venues/harbour-view.jpg'
  },
  {
    id: 8,
    name: 'Ryde Community Centre',
    description: 'Modern community venue perfect for parties and celebrations in Ryde.',
    location: 'Ryde',
    suburb: 'Ryde',
    capacity: { min: 30, max: 120 },
    pricePerHour: 180,
    features: ['indoor', 'sound system', 'kitchen', 'parking', 'accessible'],
    eventTypes: ['birthday', 'party', 'corporate', 'community'],
    amenities: ['parking', 'catering', 'wifi', 'air conditioning', 'wheelchair accessible'],
    atmosphere: ['casual', 'friendly', 'community'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/ryde-community.jpg'
  },
  {
    id: 9,
    name: 'Parramatta Function Hall',
    description: 'Large function hall in the heart of Parramatta with excellent facilities.',
    location: 'Parramatta CBD',
    suburb: 'Parramatta',
    capacity: { min: 50, max: 200 },
    pricePerHour: 220,
    features: ['indoor', 'large space', 'sound system', 'stage', 'bar'],
    eventTypes: ['wedding', 'corporate', 'birthday', 'celebration'],
    amenities: ['parking', 'catering', 'wifi', 'air conditioning', 'wheelchair accessible'],
    atmosphere: ['formal', 'spacious', 'professional'],
    timeSlots: ['morning', 'afternoon', 'evening', 'night'],
    image: '/venues/parramatta-hall.jpg'
  },
  {
    id: 2,
    name: 'The Garden Pavilion',
    description: 'Elegant garden venue surrounded by lush greenery and flowering plants.',
    location: 'Eastern Suburbs',
    suburb: 'Bondi Junction',
    capacity: { min: 30, max: 120 },
    pricePerHour: 320,
    features: ['garden', 'outdoor', 'marquee', 'catering kitchen', 'parking', 'greenery'],
    eventTypes: ['wedding', 'birthday', 'baby shower', 'garden party', 'brunch'],
    amenities: ['catering kitchen', 'parking', 'restrooms', 'sound system'],
    atmosphere: ['natural', 'peaceful', 'elegant', 'fresh'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/garden-pavilion.jpg'
  },
  {
    id: 3,
    name: 'Urban Loft Space',
    description: 'Industrial chic warehouse conversion with exposed brick and high ceilings.',
    location: 'Inner West',
    suburb: 'Newtown',
    capacity: { min: 50, max: 150 },
    pricePerHour: 380,
    features: ['industrial', 'warehouse', 'exposed brick', 'high ceilings', 'dj booth', 'lighting rig', 'rooftop'],
    eventTypes: ['party', 'corporate', 'product launch', 'art exhibition', 'nightlife'],
    amenities: ['sound system', 'lighting', 'bar', 'dj equipment', 'wifi'],
    atmosphere: ['trendy', 'urban', 'cool', 'edgy', 'modern'],
    timeSlots: ['afternoon', 'evening', 'night', 'late night'],
    image: '/venues/urban-loft.jpg'
  },
  {
    id: 4,
    name: 'Beachside Cabana',
    description: 'Relaxed beach venue with direct sand access and coastal vibes.',
    location: 'Northern Beaches',
    suburb: 'Manly',
    capacity: { min: 15, max: 60 },
    pricePerHour: 200,
    features: ['beach', 'sand access', 'bbq', 'outdoor shower', 'covered area', 'coastal'],
    eventTypes: ['birthday', 'bbq', 'beach party', 'casual gathering', 'summer party'],
    amenities: ['bbq facilities', 'outdoor shower', 'parking', 'beach access'],
    atmosphere: ['relaxed', 'casual', 'beachy', 'fun', 'laid back'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/beachside-cabana.jpg'
  },
  {
    id: 5,
    name: 'Heritage Hall',
    description: 'Stunning restored heritage building with classic architecture and modern facilities.',
    location: 'CBD',
    suburb: 'Sydney CBD',
    capacity: { min: 80, max: 200 },
    pricePerHour: 450,
    features: ['heritage', 'historic', 'grand staircase', 'chandeliers', 'dance floor', 'classic architecture'],
    eventTypes: ['wedding', 'gala', 'corporate', 'formal dinner', 'charity event'],
    amenities: ['dance floor', 'sound system', 'catering', 'parking', 'wheelchair accessible'],
    atmosphere: ['elegant', 'grand', 'sophisticated', 'formal', 'historic'],
    timeSlots: ['afternoon', 'evening', 'night'],
    image: '/venues/heritage-hall.jpg'
  },
  {
    id: 6,
    name: 'Poolside Paradise',
    description: 'Modern venue with a large swimming pool and entertainment deck.',
    location: 'Eastern Suburbs',
    suburb: 'Bondi',
    capacity: { min: 25, max: 80 },
    pricePerHour: 300,
    features: ['pool', 'swimming', 'cabanas', 'outdoor speakers', 'bbq', 'deck'],
    eventTypes: ['pool party', 'birthday', 'summer party', 'bbq', 'casual gathering'],
    amenities: ['swimming pool', 'bbq area', 'sound system', 'parking', 'changing rooms'],
    atmosphere: ['fun', 'relaxed', 'summery', 'party', 'energetic'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/poolside-paradise.jpg'
  },
  {
    id: 7,
    name: 'Parramatta Community Centre',
    description: 'Large community hall perfect for celebrations and events in the heart of Parramatta.',
    location: 'Western Sydney',
    suburb: 'Parramatta',
    capacity: { min: 50, max: 150 },
    pricePerHour: 180,
    features: ['large hall', 'stage', 'kitchen', 'parking', 'accessible'],
    eventTypes: ['wedding', 'birthday', 'community event', 'corporate', 'celebration'],
    amenities: ['kitchen facilities', 'sound system', 'parking', 'wheelchair accessible', 'tables and chairs'],
    atmosphere: ['community', 'spacious', 'versatile', 'welcoming'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/parramatta-community.jpg'
  },
  {
    id: 8,
    name: 'Riverside Function Room',
    description: 'Beautiful function room overlooking the Parramatta River with modern facilities.',
    location: 'Parramatta CBD',
    suburb: 'Parramatta',
    capacity: { min: 30, max: 100 },
    pricePerHour: 250,
    features: ['river views', 'modern', 'catering kitchen', 'balcony', 'air conditioning'],
    eventTypes: ['wedding', 'corporate', 'birthday', 'anniversary', 'business meeting'],
    amenities: ['catering kitchen', 'sound system', 'projector', 'parking', 'balcony'],
    atmosphere: ['elegant', 'modern', 'scenic', 'professional'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/riverside-function.jpg'
  },
  {
    id: 9,
    name: 'Ryde Community Hall',
    description: 'Spacious community hall in the heart of Ryde, perfect for local celebrations and events.',
    location: 'Northern Sydney',
    suburb: 'Ryde',
    capacity: { min: 40, max: 120 },
    pricePerHour: 160,
    features: ['large hall', 'stage', 'kitchen', 'parking', 'accessible', 'community'],
    eventTypes: ['birthday', 'wedding', 'community event', 'celebration', 'meeting'],
    amenities: ['kitchen facilities', 'sound system', 'parking', 'wheelchair accessible', 'tables and chairs'],
    atmosphere: ['community', 'spacious', 'welcoming', 'local'],
    timeSlots: ['morning', 'afternoon', 'evening'],
    image: '/venues/ryde-community.jpg'
  },
  {
    id: 10,
    name: 'Ryde Park Pavilion',
    description: 'Beautiful pavilion surrounded by parkland in Ryde, ideal for outdoor celebrations.',
    location: 'Ryde Park',
    suburb: 'Ryde',
    capacity: { min: 25, max: 80 },
    pricePerHour: 200,
    features: ['park setting', 'outdoor', 'pavilion', 'bbq area', 'playground nearby'],
    eventTypes: ['birthday', 'family gathering', 'picnic', 'celebration', 'children\'s party'],
    amenities: ['bbq facilities', 'parking', 'playground access', 'picnic tables', 'restrooms'],
    atmosphere: ['family-friendly', 'outdoor', 'relaxed', 'natural'],
    timeSlots: ['morning', 'afternoon', 'early evening'],
    image: '/venues/ryde-park.jpg'
  },
  {
    id: 11,
    name: 'Ryde RSL Function Centre',
    description: 'Modern function centre at Ryde RSL with excellent facilities and catering options.',
    location: 'Ryde RSL',
    suburb: 'Ryde',
    capacity: { min: 50, max: 180 },
    pricePerHour: 280,
    features: ['modern', 'catering', 'bar', 'dance floor', 'sound system', 'air conditioning'],
    eventTypes: ['wedding', 'birthday', 'anniversary', 'corporate', 'celebration'],
    amenities: ['full catering', 'bar service', 'sound system', 'dance floor', 'parking', 'wheelchair accessible'],
    atmosphere: ['modern', 'professional', 'celebratory', 'spacious'],
    timeSlots: ['afternoon', 'evening', 'night'],
    image: '/venues/ryde-rsl.jpg'
  }
];

// Keyword mapping system
const keywordMappings = {
  // Event types
  wedding: ['wedding', 'marriage', 'bride', 'groom', 'ceremony', 'reception'],
  birthday: ['birthday', 'bday', 'celebration', 'party'],
  corporate: ['corporate', 'business', 'meeting', 'conference', 'work', 'company'],
  party: ['party', 'celebration', 'fun', 'dance', 'music'],

  // Locations
  harbour: ['harbour', 'harbor', 'water', 'waterfront', 'ocean', 'sea'],
  beach: ['beach', 'sand', 'coastal', 'seaside', 'surf'],
  garden: ['garden', 'outdoor', 'nature', 'greenery', 'plants', 'flowers'],
  city: ['city', 'urban', 'cbd', 'downtown', 'central'],

  // Atmosphere
  elegant: ['elegant', 'sophisticated', 'classy', 'upscale', 'fancy', 'formal'],
  casual: ['casual', 'relaxed', 'laid back', 'informal', 'chill'],
  romantic: ['romantic', 'intimate', 'cozy', 'private'],
  fun: ['fun', 'energetic', 'lively', 'exciting', 'vibrant'],

  // Features
  pool: ['pool', 'swimming', 'water'],
  outdoor: ['outdoor', 'outside', 'open air', 'al fresco'],
  indoor: ['indoor', 'inside', 'covered'],
  parking: ['parking', 'car', 'vehicle'],
  catering: ['catering', 'food', 'kitchen', 'dining'],

  // Time
  morning: ['morning', 'am', 'breakfast', 'brunch'],
  afternoon: ['afternoon', 'lunch', 'pm'],
  evening: ['evening', 'dinner', 'night'],
  weekend: ['weekend', 'saturday', 'sunday'],
  weekday: ['weekday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday']
};

interface SmartVenueSearchProps {
  onVenuesFound: (venues: any[]) => void;
  initialQuery?: string;
}

export default function SmartVenueSearch({ onVenuesFound, initialQuery = '' }: SmartVenueSearchProps) {
  const { user } = useUserSafe();
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [lastSearch, setLastSearch] = useState('');
  const [hasProcessedInitialQuery, setHasProcessedInitialQuery] = useState(false);

  // Enhanced smart search with suburb detection, date extraction, and irrelevant word filtering
  const analyzeQuery = async (query: string) => {
    const lowerQuery = query.toLowerCase();
    const words = lowerQuery.split(/\s+/);

    // Define irrelevant words to ignore
    const irrelevantWords = new Set([
      'i', 'have', 'a', 'an', 'the', 'for', 'on', 'in', 'at', 'to', 'with', 'and', 'or', 'but',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'do', 'does', 'did', 'will', 'would',
      'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'of', 'from', 'by', 'about',
      'into', 'through', 'during', 'before', 'after', 'above', 'below', 'up', 'down', 'out',
      'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when',
      'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other',
      'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
      'just', 'now', 'looking', 'need', 'want', 'like', 'get', 'find', 'search', 'venue'
    ]);

    const analysis = {
      suburbs: [] as string[],
      dates: [] as string[],
      eventTypes: [] as string[],
      capacity: null as number | null,
      budget: null as number | null,
      features: [] as string[],
      relevantKeywords: [] as string[]
    };

    // 1. Extract and validate suburbs using NSW suburbs API
    const potentialSuburbs = words.filter(word =>
      word.length >= 3 &&
      !irrelevantWords.has(word) &&
      !/^\d+$/.test(word) && // Not just numbers
      !Object.values(keywordMappings).flat().includes(word) // Not already a mapped keyword
    );

    for (const suburb of potentialSuburbs) {
      try {
        const suburbResults = await searchNSWSuburbs(suburb);
        if (suburbResults.length > 0) {
          // Use the name property from the NSW suburbs API
          analysis.suburbs.push(suburbResults[0].name);
        }
      } catch (error) {
        // Fallback: check if it's a known suburb manually
        const knownSuburbs = ['parramatta', 'ryde', 'sydney', 'bondi', 'manly', 'chatswood', 'hornsby', 'penrith', 'blacktown', 'liverpool'];
        if (knownSuburbs.includes(suburb.toLowerCase())) {
          analysis.suburbs.push(suburb);
        }
      }
    }

    // 2. Extract and parse dates (various formats with proper interpretation)
    const extractAndParseDates = (text: string) => {
      const dates: string[] = [];

      // Pattern 1: DD/MM/YYYY or DD-MM-YYYY (Australian format)
      const ddmmyyyy = text.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/g);
      if (ddmmyyyy) {
        ddmmyyyy.forEach(match => {
          const [, day, month, year] = match.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/)!;
          dates.push(`${day}/${month}/${year} (${day} ${getMonthName(parseInt(month))} ${year})`);
        });
      }

      // Pattern 2: YYYY/MM/DD or YYYY-MM-DD (ISO format)
      const yyyymmdd = text.match(/(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})/g);
      if (yyyymmdd) {
        yyyymmdd.forEach(match => {
          const [, year, month, day] = match.match(/(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})/)!;
          dates.push(`${day}/${month}/${year} (${day} ${getMonthName(parseInt(month))} ${year})`);
        });
      }

      // Pattern 3: DD/MM (assume current year, Australian format)
      const ddmm = text.match(/\b(\d{1,2})[\/\-](\d{1,2})\b/g);
      if (ddmm) {
        ddmm.forEach(match => {
          const [, day, month] = match.match(/(\d{1,2})[\/\-](\d{1,2})/)!;
          const currentYear = new Date().getFullYear();
          // Only add if it's a valid month (1-12) and day (1-31)
          if (parseInt(month) >= 1 && parseInt(month) <= 12 && parseInt(day) >= 1 && parseInt(day) <= 31) {
            dates.push(`${day}/${month}/${currentYear} (${day} ${getMonthName(parseInt(month))} ${currentYear})`);
          }
        });
      }

      // Pattern 4: Natural language dates
      const naturalDates = [
        /(\d{1,2})(?:st|nd|rd|th)?\s+(?:of\s+)?(january|february|march|april|may|june|july|august|september|october|november|december)/gi,
        /(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})(?:st|nd|rd|th)?/gi,
        /(next|this)\s+(week|weekend|month|year)/gi,
        /(tomorrow|today)/gi
      ];

      naturalDates.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          dates.push(...matches);
        }
      });

      return dates;
    };

    // Helper function to get month name
    const getMonthName = (monthNum: number): string => {
      const months = [
        '', 'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return months[monthNum] || 'Invalid';
    };

    analysis.dates = extractAndParseDates(query);

    // 3. Extract event types using keyword mappings
    Object.entries(keywordMappings).forEach(([category, keywords]) => {
      keywords.forEach(keyword => {
        if (lowerQuery.includes(keyword)) {
          if (['wedding', 'birthday', 'corporate', 'party'].includes(category)) {
            analysis.eventTypes.push(category);
          } else {
            analysis.features.push(category);
          }
        }
      });
    });

    // 4. Extract capacity (numbers that could represent guest count)
    const numbers = query.match(/\d+/g)?.map(Number) || [];
    analysis.capacity = numbers.find(n => n >= 5 && n <= 500) || null;
    analysis.budget = numbers.find(n => n >= 50 && n <= 2000) || null;

    // 5. Filter relevant keywords (exclude irrelevant words and already processed items)
    analysis.relevantKeywords = words.filter(word =>
      word.length > 2 &&
      !irrelevantWords.has(word) &&
      !analysis.suburbs.some(suburb => suburb.toLowerCase().includes(word)) &&
      !analysis.dates.some(date => date.toLowerCase().includes(word)) &&
      !/^\d+$/.test(word)
    );

    return analysis;
  };

  // Smart keyword extraction (legacy function for compatibility)
  const extractKeywords = (query: string): string[] => {
    const words = query.toLowerCase().split(/\s+/);
    const extractedKeywords: string[] = [];

    // Check each word against our keyword mappings
    Object.entries(keywordMappings).forEach(([category, keywords]) => {
      keywords.forEach(keyword => {
        if (words.some(word => word.includes(keyword) || keyword.includes(word))) {
          extractedKeywords.push(category);
        }
      });
    });

    return [...new Set(extractedKeywords)];
  };

  // Extract numbers for capacity and budget
  const extractNumbers = (query: string) => {
    const numbers = query.match(/\d+/g);
    return numbers ? numbers.map(Number) : [];
  };

  // Enhanced venue filtering using smart analysis
  const filterVenues = async (query: string) => {
    if (!query.trim()) return venues;

    const analysis = await analyzeQuery(query);

    console.log('🔍 Smart Search Analysis:', {
      ...analysis,
      query: query,
      interpretation: {
        suburbs: analysis.suburbs.length > 0 ? `Found ${analysis.suburbs.length} suburb(s): ${analysis.suburbs.join(', ')}` : 'No suburbs detected',
        dates: analysis.dates.length > 0 ? `Found ${analysis.dates.length} date(s): ${analysis.dates.join(', ')}` : 'No dates detected',
        eventTypes: analysis.eventTypes.length > 0 ? `Event types: ${analysis.eventTypes.join(', ')}` : 'No event types detected',
        capacity: analysis.capacity ? `Capacity: ${analysis.capacity} people` : 'No capacity specified',
        budget: analysis.budget ? `Budget: $${analysis.budget}` : 'No budget specified'
      }
    });

    return venues.filter(venue => {
      let score = 0;

      // 1. HIGHEST PRIORITY: Suburb matches (exact location)
      if (analysis.suburbs.length > 0) {
        const suburbMatch = analysis.suburbs.some(suburb => {
          const suburbLower = suburb.toLowerCase();
          const venueLower = venue.suburb.toLowerCase();
          const locationLower = venue.location.toLowerCase();

          // Check for exact matches or partial matches
          return venueLower.includes(suburbLower) ||
                 locationLower.includes(suburbLower) ||
                 suburbLower.includes(venueLower) ||
                 // Special case for common variations
                 (suburbLower === 'parramatta' && (venueLower.includes('parra') || locationLower.includes('parra'))) ||
                 (suburbLower === 'ryde' && (venueLower.includes('ryde') || locationLower.includes('ryde')));
        });

        if (suburbMatch) {
          score += 20; // Very high priority for location match
        } else {
          // If they specified a suburb but venue doesn't match, don't heavily penalize
          // Instead, just don't give bonus points
          score += 0;
        }
      }

      // 2. HIGH PRIORITY: Date availability (if dates specified)
      if (analysis.dates.length > 0) {
        // For now, assume all venues are available (in real app, check availability)
        score += 15; // High priority for date-specific searches
      }

      // 3. MEDIUM-HIGH PRIORITY: Capacity match
      if (analysis.capacity) {
        if (venue.capacity.min <= analysis.capacity && venue.capacity.max >= analysis.capacity) {
          score += 12; // High priority for capacity match
        } else if (venue.capacity.max < analysis.capacity) {
          score -= 5; // Penalize if venue is too small
        }
      }

      // 4. MEDIUM PRIORITY: Budget match
      if (analysis.budget) {
        if (venue.pricePerHour <= analysis.budget) {
          score += 10; // Good priority for budget match
        } else {
          score -= 3; // Light penalty for over budget
        }
      }

      // 5. MEDIUM PRIORITY: Event type match
      analysis.eventTypes.forEach(eventType => {
        if (venue.eventTypes.some(type => type.includes(eventType))) {
          score += 8;
        }
      });

      // 6. LOWER PRIORITY: Feature matches
      analysis.features.forEach(feature => {
        if (venue.features.some(f => f.includes(feature))) score += 6;
        if (venue.amenities.some(a => a.includes(feature))) score += 4;
        if (venue.atmosphere.some(mood => mood.includes(feature))) score += 3;
      });

      // 7. LOWEST PRIORITY: General keyword matches
      analysis.relevantKeywords.forEach(keyword => {
        if (venue.description.toLowerCase().includes(keyword)) score += 1;
        if (venue.name.toLowerCase().includes(keyword)) score += 2;
      });

      // Return venues with positive scores OR if no suburb was specified
      return score > 0 || analysis.suburbs.length === 0;
    }).sort((a, b) => {
      // Calculate final scores for sorting
      let scoreA = 0, scoreB = 0;

      [a, b].forEach((venue, index) => {
        let venueScore = 0;

        // Recalculate scores for sorting
        if (analysis.suburbs.length > 0) {
          const suburbMatch = analysis.suburbs.some(suburb =>
            venue.suburb.toLowerCase().includes(suburb.toLowerCase()) ||
            venue.location.toLowerCase().includes(suburb.toLowerCase())
          );
          if (suburbMatch) venueScore += 20;
          else venueScore -= 10;
        }

        if (analysis.capacity && venue.capacity.min <= analysis.capacity && venue.capacity.max >= analysis.capacity) {
          venueScore += 12;
        }

        if (analysis.budget && venue.pricePerHour <= analysis.budget) {
          venueScore += 10;
        }

        analysis.eventTypes.forEach(eventType => {
          if (venue.eventTypes.some(type => type.includes(eventType))) {
            venueScore += 8;
          }
        });

        if (index === 0) scoreA = venueScore;
        else scoreB = venueScore;
      });

      return scoreB - scoreA; // Sort by relevance score
    });
  };

  // Legacy function for backward compatibility
  const filterVenuesLegacy = (query: string) => {
    if (!query.trim()) return venues;

    const lowerQuery = query.toLowerCase();
    const keywords = extractKeywords(query);
    const numbers = extractNumbers(query);

    const possibleCapacity = numbers.find(n => n >= 5 && n <= 500);
    const possibleBudget = numbers.find(n => n >= 50 && n <= 1000);

    return venues.filter(venue => {
      let score = 0;

      // Check for direct suburb/location matches
      if (venue.suburb.toLowerCase().includes(lowerQuery) ||
          venue.location.toLowerCase().includes(lowerQuery) ||
          lowerQuery.includes(venue.suburb.toLowerCase())) {
        score += 15; // High score for location match
      }

      if (possibleCapacity) {
        if (venue.capacity.min <= possibleCapacity && venue.capacity.max >= possibleCapacity) {
          score += 10;
        }
      }

      if (possibleBudget) {
        if (venue.pricePerHour <= possibleBudget) {
          score += 8;
        }
      }

      keywords.forEach(keyword => {
        if (venue.eventTypes.some(type => type.includes(keyword))) score += 5;
        if (venue.features.some(feature => feature.includes(keyword))) score += 4;
        if (venue.atmosphere.some(mood => mood.includes(keyword))) score += 3;
        if (venue.location.toLowerCase().includes(keyword) ||
            venue.suburb.toLowerCase().includes(keyword)) score += 6;
        if (venue.amenities.some(amenity => amenity.includes(keyword))) score += 2;
        if (venue.description.toLowerCase().includes(keyword)) score += 1;
      });

      return score > 0;
    }).sort((a, b) => {
      let scoreA = 0, scoreB = 0;

      [a, b].forEach((venue, index) => {
        let venueScore = 0;

        // Prioritize location matches
        if (venue.suburb.toLowerCase().includes(lowerQuery) ||
            venue.location.toLowerCase().includes(lowerQuery)) {
          venueScore += 15;
        }

        keywords.forEach(keyword => {
          if (venue.eventTypes.some(type => type.includes(keyword))) venueScore += 5;
          if (venue.features.some(feature => feature.includes(keyword))) venueScore += 4;
          if (venue.atmosphere.some(mood => mood.includes(keyword))) venueScore += 3;
          if (venue.location.toLowerCase().includes(keyword)) venueScore += 6;
        });

        if (index === 0) scoreA = venueScore;
        else scoreB = venueScore;
      });

      return scoreB - scoreA;
    });
  };

  // Generate smart suggestions based on partial input
  const generateSuggestions = (query: string): string[] => {
    if (query.length < 2) return [];

    const suggestions: string[] = [];
    const lowerQuery = query.toLowerCase();

    // Suggest event types
    if (lowerQuery.includes('wedding') || lowerQuery.includes('marry')) {
      suggestions.push('wedding reception for 100 guests');
    }
    if (lowerQuery.includes('birthday') || lowerQuery.includes('party')) {
      suggestions.push('birthday party for 30 people');
    }
    if (lowerQuery.includes('corporate') || lowerQuery.includes('business')) {
      suggestions.push('corporate event with catering');
    }

    // Suggest locations
    if (lowerQuery.includes('beach') || lowerQuery.includes('water')) {
      suggestions.push('beachside venue with ocean views');
    }
    if (lowerQuery.includes('garden') || lowerQuery.includes('outdoor')) {
      suggestions.push('outdoor garden venue');
    }
    if (lowerQuery.includes('city') || lowerQuery.includes('cbd')) {
      suggestions.push('city venue with parking');
    }

    // Suggest capacity-based searches
    const numbers = extractNumbers(query);
    if (numbers.length > 0) {
      const num = numbers[0];
      if (num >= 10 && num <= 500) {
        suggestions.push(`venue for ${num} guests with catering`);
        suggestions.push(`${num} people outdoor venue`);
      }
    }

    return suggestions.slice(0, 4); // Limit to 4 suggestions
  };

  // Handle search with enhanced analysis
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;

    console.log('🔍 SMART SEARCH: Starting search for query:', searchQuery);
    setIsSearching(true);
    setLastSearch(searchQuery);
    setSuggestions([]); // Clear suggestions when searching

    try {
      // Use enhanced filtering with suburb detection and date extraction
      const results = await filterVenues(searchQuery);
      console.log('🔍 SMART SEARCH: Found', results.length, 'venues for query:', searchQuery);

      // Track the search event for analytics
      await trackSearchEvent({
        user_id: user?.id,
        search_query: searchQuery,
        results_count: results.length
      });

      onVenuesFound(results);
    } catch (error) {
      console.error('Smart search error:', error);
      // Fallback to legacy search if enhanced search fails
      const results = filterVenuesLegacy(searchQuery);
      console.log('🔍 SMART SEARCH: Fallback search found', results.length, 'venues');

      // Track the fallback search event
      await trackSearchEvent({
        user_id: user?.id,
        search_query: searchQuery,
        results_count: results.length
      });

      onVenuesFound(results);
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, onVenuesFound, user]);

  // Update suggestions as user types
  useEffect(() => {
    const newSuggestions = generateSuggestions(searchQuery);
    setSuggestions(newSuggestions);
  }, [searchQuery]);

  // Handle initial query from homepage (only once)
  useEffect(() => {
    console.log('🔍 SMART SEARCH: useEffect triggered with initialQuery:', initialQuery, 'hasProcessedInitialQuery:', hasProcessedInitialQuery);
    if (initialQuery && initialQuery.trim() && !hasProcessedInitialQuery) {
      console.log('🔍 SMART SEARCH: Processing initial query from homepage:', initialQuery);
      setSearchQuery(initialQuery);
      setHasProcessedInitialQuery(true);

      // Auto-trigger search with the initial query directly
      const performInitialSearch = async () => {
        console.log('🔍 SMART SEARCH: Auto-triggering search for initial query:', initialQuery);
        setIsSearching(true);
        setLastSearch(initialQuery);
        setSuggestions([]);

        try {
          const results = await filterVenues(initialQuery);
          console.log('🔍 SMART SEARCH: Found', results.length, 'venues for initial query:', initialQuery);
          onVenuesFound(results);
        } catch (error) {
          console.error('Smart search error:', error);
          const results = filterVenuesLegacy(initialQuery);
          console.log('🔍 SMART SEARCH: Fallback search found', results.length, 'venues');
          onVenuesFound(results);
        } finally {
          setIsSearching(false);
        }
      };

      // Small delay to ensure component is fully mounted
      setTimeout(performInitialSearch, 100);
    }
  }, [initialQuery, hasProcessedInitialQuery, onVenuesFound]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setSuggestions([]); // Clear suggestions when Enter is pressed
      handleSearch();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="bg-purple-100 p-3 rounded-lg">
          <Sparkles className="h-6 w-6 text-purple-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Smart Venue Search</h3>
          <p className="text-sm text-gray-600">Describe what you're looking for in natural language</p>
        </div>
      </div>

      {/* Search Input */}
      <div className="relative mb-4 search-container">
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., 'Ryde birthday party 30 people'"
              className="search-input w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base min-h-[44px] focus-enhanced"
            />
            {searchQuery && (
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSuggestions([]);
                  setLastSearch('');
                  setHasProcessedInitialQuery(true); // Prevent re-processing of initial query
                  onVenuesFound([]); // Reset to show no venues (will show regular search results)
                }}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          <button
            onClick={handleSearch}
            disabled={!searchQuery.trim() || isSearching}
            className="btn btn-reactive px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center min-h-[44px] w-full sm:w-auto touch-target focus-enhanced"
          >
            {isSearching ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Search className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Suggestions */}
        {suggestions.length > 0 && searchQuery.length > 1 && (
          <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-10">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => {
                  setSearchQuery(suggestion);
                  setSuggestions([]);
                  // Auto-search when suggestion is clicked
                  setTimeout(() => handleSearch(), 100);
                }}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 text-sm text-gray-700 border-b border-gray-100 last:border-b-0"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Search Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2 flex items-center">
            <Users className="h-4 w-4 mr-2 text-purple-600" />
            By Event & Capacity
          </h4>
          <div className="space-y-1 text-sm text-gray-600">
            <p>"Ryde birthday party 30 people"</p>
            <p>"Parramatta wedding 120 guests"</p>
            <p>"birthday party on 11/9/2025"</p>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2 flex items-center">
            <MapPin className="h-4 w-4 mr-2 text-purple-600" />
            By Location & Features
          </h4>
          <div className="space-y-1 text-sm text-gray-600">
            <p>"Circular Quay harbour views"</p>
            <p>"Ryde community centre"</p>
            <p>"outdoor garden venue"</p>
          </div>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="flex flex-wrap gap-2">
        {[
          'Ryde birthday party',
          'Parramatta wedding',
          'party on 22/5',
          'wedding 2025/6/15',
          'birthday party 11/9/2025',
          'outdoor garden',
          'budget under $200'
        ].map((filter) => (
          <button
            key={filter}
            onClick={() => setSearchQuery(filter)}
            className="btn-reactive px-4 py-2 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors min-h-[40px] touch-target focus-enhanced"
          >
            {filter}
          </button>
        ))}
      </div>

      {lastSearch && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-800">
            <strong>Last search:</strong> "{lastSearch}" - Found venues matching your criteria below
          </p>
          <p className="text-xs text-green-600 mt-1">
            Check the browser console to see what was detected from your search
          </p>
        </div>
      )}
    </div>
  );
}
