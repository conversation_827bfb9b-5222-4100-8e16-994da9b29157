# Google OAuth Configuration Guide

## Step 1: Go to Google Cloud Console
1. Navigate to: APIs & Services > Credentials
2. Select your OAuth 2.0 Client ID

## Step 2: Add Authorized JavaScript Origins
Under "Authorized JavaScript origins", add:
```
https://housegoing.com.au
http://localhost:5173
```

## Step 3: Add Authorized Redirect URIs 
Under "Authorized redirect URIs", add:
```
https://housegoing.com.au/api/auth/callback/google
http://localhost:5173/api/auth/callback/google
```

## Step 4: Save Changes
Click "Save" and wait a few minutes for changes to propagate

## Verification
- Development: Try signing in at http://localhost:5173
- Production: Test at https://housegoing.com.au
