import React, { useState } from 'react';
import { useAuth } from '../providers/AuthProvider';

type SettingsTab = 'profile' | 'account' | 'notifications' | 'security';

export default function UserSettings() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  if (isLoading) {
    return <div className="pt-32 flex justify-center">Loading...</div>;
  }

  if (!isAuthenticated || !user) {
    return <div className="pt-32 flex justify-center">Please sign in to view your settings</div>;
  }

  // For now, assume all users can be hosts - you can add host logic later
  const userIsHost = false;

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage(null);

    try {
      // In a real application, you would update the user's profile
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="pt-32 px-4 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Account Settings</h1>

      {message && (
        <div 
          className={`mb-6 p-4 rounded-md ${
            message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar */}
        <div className="w-full md:w-64 bg-white shadow-md rounded-lg p-4 h-fit">
          <nav className="space-y-1">
            <button
              onClick={() => setActiveTab('profile')}
              className={`w-full text-left px-3 py-2 rounded-md ${
                activeTab === 'profile' 
                  ? 'bg-purple-50 text-purple-700 font-medium' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              Profile Information
            </button>
            <button
              onClick={() => setActiveTab('account')}
              className={`w-full text-left px-3 py-2 rounded-md ${
                activeTab === 'account' 
                  ? 'bg-purple-50 text-purple-700 font-medium' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              Account Preferences
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`w-full text-left px-3 py-2 rounded-md ${
                activeTab === 'notifications' 
                  ? 'bg-purple-50 text-purple-700 font-medium' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              Notifications
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`w-full text-left px-3 py-2 rounded-md ${
                activeTab === 'security' 
                  ? 'bg-purple-50 text-purple-700 font-medium' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              Security
            </button>
          </nav>

          {userIsHost && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="font-medium text-gray-900 mb-2">Host Settings</h3>
              <a 
                href="/host/onboarding" 
                className="block px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md"
              >
                Host Dashboard
              </a>
              <a 
                href="/host/listings" 
                className="block px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md"
              >
                Manage Listings
              </a>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 bg-white shadow-md rounded-lg p-6">
          {activeTab === 'profile' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Profile Information</h2>
              <form onSubmit={handleSaveProfile} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Profile Picture
                  </label>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-lg">
                        {user.email?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                    <button
                      type="button"
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Change
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    defaultValue={user.user_metadata?.full_name || user.user_metadata?.name || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    defaultValue={user.email || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    disabled
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    To change your email, go to the Security tab.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    defaultValue={user.user_metadata?.phone || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Tell us a bit about yourself..."
                  ></textarea>
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSaving}
                    className={`px-4 py-2 rounded-md text-white ${
                      isSaving
                        ? 'bg-purple-400 cursor-not-allowed'
                        : 'bg-purple-600 hover:bg-purple-700'
                    }`}
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {activeTab === 'account' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Account Preferences</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Language</h3>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                  </select>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Time Zone</h3>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="utc">UTC (Coordinated Universal Time)</option>
                    <option value="est">EST (Eastern Standard Time)</option>
                    <option value="pst">PST (Pacific Standard Time)</option>
                    <option value="aest">AEST (Australian Eastern Standard Time)</option>
                  </select>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Currency</h3>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="usd">USD ($)</option>
                    <option value="eur">EUR (€)</option>
                    <option value="gbp">GBP (£)</option>
                    <option value="aud">AUD ($)</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Notification Settings</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="email-notifications"
                    className="mt-1 mr-2"
                    defaultChecked
                  />
                  <div>
                    <label htmlFor="email-notifications" className="font-medium text-gray-700">
                      Email Notifications
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive emails about your account activity, bookings, and promotions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="sms-notifications"
                    className="mt-1 mr-2"
                  />
                  <div>
                    <label htmlFor="sms-notifications" className="font-medium text-gray-700">
                      SMS Notifications
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive text messages for booking confirmations and important updates.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="marketing-emails"
                    className="mt-1 mr-2"
                  />
                  <div>
                    <label htmlFor="marketing-emails" className="font-medium text-gray-700">
                      Marketing Emails
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive promotional emails about special offers and new features.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Security Settings</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Change Password</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Update your password to keep your account secure.
                  </p>
                  <a 
                    href="/reset-password" 
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md"
                  >
                    Change Password
                  </a>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="text-lg font-medium mb-2">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Add an extra layer of security to your account by enabling two-factor authentication.
                  </p>
                  <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md">
                    Enable 2FA
                  </button>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="text-lg font-medium mb-2">Connected Accounts</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Manage third-party services connected to your account.
                  </p>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <p className="font-medium">Google</p>
                        <p className="text-sm text-gray-500">Not connected</p>
                      </div>
                      <button className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md text-sm">
                        Connect
                      </button>
                    </div>
                    <div className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <p className="font-medium">Facebook</p>
                        <p className="text-sm text-gray-500">Not connected</p>
                      </div>
                      <button className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md text-sm">
                        Connect
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
