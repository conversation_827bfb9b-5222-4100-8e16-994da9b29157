import React, { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isLoaded } = useUser();
  const location = useLocation();
  
  // Check if the current user is an admin
  const isAdmin = isLoaded && user?.publicMetadata?.role === 'admin';
  
  if (!isLoaded) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }
  
  if (!isAdmin) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen p-4">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
        <p className="text-gray-700 mb-6">You do not have permission to access the admin portal.</p>
        <Link to="/" className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
          Return to Home
        </Link>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link to="/admin" className="text-xl font-bold text-purple-600">
                  HouseGoing Admin
                </Link>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <NavLink to="/admin" exact>
                  Dashboard
                </NavLink>
                <NavLink to="/admin/properties">
                  Properties
                </NavLink>
                <NavLink to="/admin/users">
                  Users
                </NavLink>
                <NavLink to="/admin/bookings">
                  Bookings
                </NavLink>
              </div>
            </div>
            <div className="flex items-center">
              <div className="ml-3 relative">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 mr-2">
                    {user?.firstName} {user?.lastName}
                  </span>
                  <img
                    className="h-8 w-8 rounded-full"
                    src={user?.imageUrl || 'https://via.placeholder.com/40'}
                    alt="User avatar"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
      
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </div>
    </div>
  );
}

interface NavLinkProps {
  to: string;
  exact?: boolean;
  children: ReactNode;
}

function NavLink({ to, exact = false, children }: NavLinkProps) {
  const location = useLocation();
  const isActive = exact ? location.pathname === to : location.pathname.startsWith(to);
  
  return (
    <Link
      to={to}
      className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
        isActive
          ? 'border-purple-500 text-gray-900'
          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
      }`}
    >
      {children}
    </Link>
  );
}
