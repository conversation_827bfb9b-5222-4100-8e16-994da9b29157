import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import {
  Settings,
  Save,
  Globe,
  Mail,
  Palette,
  Code,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { getSupabaseClient } from '../../lib/supabase-client';

// DIRECT OVERRIDE: List of admin emails
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

export default function SiteSettings() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      siteName: 'HouseGoing',
      siteDescription: 'Find the perfect party venue',
      contactEmail: '<EMAIL>',
      supportPhone: '',
      defaultCurrency: 'AUD'
    },
    appearance: {
      primaryColor: '#7c3aed',
      secondaryColor: '#a78bfa',
      logoUrl: '',
      faviconUrl: '',
      enableDarkMode: false
    },
    email: {
      senderName: 'HouseGoing',
      senderEmail: '<EMAIL>',
      enableEmailNotifications: true,
      bookingConfirmationTemplate: '',
      hostNotificationTemplate: ''
    },
    integrations: {
      googleMapsApiKey: '',
      stripePublicKey: 'pk_test_51RNPjZGGj0zoACi7E0rkbGUlMsiMPmkiODVpKJBroPsdLEzZf95WJ2rSUz9GdixosG1C9XTQquTcF1r3rZigcr3q00bteGkuZy',
      stripeSecretKey: '', // Secret key removed for security
      huggingFaceApiKey: '',
      langChainApiKey: ''
    },
    advanced: {
      enableCaching: true,
      cacheLifetime: 3600,
      debugMode: false,
      maintenanceMode: false,
      maintenanceMessage: 'We are currently performing maintenance. Please check back soon.'
    }
  });
  const [errors, setErrors] = useState({});
  const [saveMessage, setSaveMessage] = useState('');

  // Check if user is admin
  useEffect(() => {
    if (user) {
      // Get user email
      const userEmail = user.primaryEmailAddress?.emailAddress || '';

      // Simple check: user is admin if email is in the list or we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';
      const userIsAdmin = isDevelopment || ADMIN_EMAILS.includes(userEmail.toLowerCase());

      // Log for debugging
      console.log('SiteSettings admin check:', { email: userEmail, isDevelopment, isAdmin: userIsAdmin });

      setIsAdmin(userIsAdmin);

      if (!userIsAdmin) {
        navigate('/unauthorized');
      } else {
        fetchSettings();
      }
    }
  }, [user, navigate]);

  // Fetch settings
  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      // Get settings from Supabase
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        throw error;
      }

      if (data) {
        // Merge with default settings
        setSettings({
          general: { ...settings.general, ...data.general },
          appearance: { ...settings.appearance, ...data.appearance },
          email: { ...settings.email, ...data.email },
          integrations: { ...settings.integrations, ...data.integrations },
          advanced: { ...settings.advanced, ...data.advanced }
        });
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      // Validate settings
      const validationErrors = validateSettings();
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        throw new Error('Please fix the validation errors');
      }

      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      // Save to Supabase
      const { data, error } = await supabase
        .from('site_settings')
        .upsert({
          id: 1, // Single row for site settings
          general: settings.general,
          appearance: settings.appearance,
          email: settings.email,
          integrations: settings.integrations,
          advanced: settings.advanced,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      setSaveMessage('Settings saved successfully!');
      setErrors({});

      // If maintenance mode was toggled, show a warning
      if (settings.advanced.maintenanceMode) {
        alert('Warning: Maintenance mode is enabled. The site will be inaccessible to regular users.');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSaveMessage(`Error: ${error.message}`);
    } finally {
      setIsSaving(false);

      // Clear success message after 3 seconds
      if (!error) {
        setTimeout(() => {
          setSaveMessage('');
        }, 3000);
      }
    }
  };

  // Validate settings
  const validateSettings = () => {
    const errors = {};

    // Validate general settings
    if (!settings.general.siteName) {
      errors.siteName = 'Site name is required';
    }

    if (!settings.general.contactEmail) {
      errors.contactEmail = 'Contact email is required';
    } else if (!/\S+@\S+\.\S+/.test(settings.general.contactEmail)) {
      errors.contactEmail = 'Invalid email format';
    }

    // Validate email settings
    if (settings.email.enableEmailNotifications) {
      if (!settings.email.senderEmail) {
        errors.senderEmail = 'Sender email is required';
      } else if (!/\S+@\S+\.\S+/.test(settings.email.senderEmail)) {
        errors.senderEmail = 'Invalid email format';
      }

      if (!settings.email.senderName) {
        errors.senderName = 'Sender name is required';
      }
    }

    return errors;
  };

  // Handle input change
  const handleChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return null; // Navigate will redirect, this prevents flash of content
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Site Settings</h1>
          <button
            onClick={saveSettings}
            disabled={isSaving}
            className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
          >
            {isSaving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            Save Settings
          </button>
        </div>

        {saveMessage && (
          <div className={`mb-6 p-4 rounded-md ${
            saveMessage.startsWith('Error')
              ? 'bg-red-100 text-red-700'
              : 'bg-green-100 text-green-700'
          }`}>
            {saveMessage.startsWith('Error') && (
              <AlertTriangle className="inline-block w-5 h-5 mr-2" />
            )}
            {saveMessage}
          </div>
        )}

        <div className="bg-white rounded-lg shadow overflow-hidden">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px">
              <button
                onClick={() => setActiveTab('general')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'general'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Globe className="w-5 h-5 inline-block mr-2" />
                General
              </button>
              <button
                onClick={() => setActiveTab('appearance')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'appearance'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Palette className="w-5 h-5 inline-block mr-2" />
                Appearance
              </button>
              <button
                onClick={() => setActiveTab('email')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'email'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Mail className="w-5 h-5 inline-block mr-2" />
                Email
              </button>
              <button
                onClick={() => setActiveTab('integrations')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'integrations'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Code className="w-5 h-5 inline-block mr-2" />
                Integrations
              </button>
              <button
                onClick={() => setActiveTab('advanced')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'advanced'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Settings className="w-5 h-5 inline-block mr-2" />
                Advanced
              </button>
            </nav>
          </div>

          {/* Tab content */}
          <div className="p-6">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Basic information about your website.</p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="siteName" className="block text-sm font-medium text-gray-700">
                      Site Name
                    </label>
                    <input
                      type="text"
                      id="siteName"
                      value={settings.general.siteName}
                      onChange={(e) => handleChange('general', 'siteName', e.target.value)}
                      className={`mt-1 block w-full border ${errors.siteName ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm`}
                    />
                    {errors.siteName && (
                      <p className="mt-1 text-sm text-red-600">{errors.siteName}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="defaultCurrency" className="block text-sm font-medium text-gray-700">
                      Default Currency
                    </label>
                    <select
                      id="defaultCurrency"
                      value={settings.general.defaultCurrency}
                      onChange={(e) => handleChange('general', 'defaultCurrency', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    >
                      <option value="AUD">Australian Dollar (AUD)</option>
                      <option value="USD">US Dollar (USD)</option>
                      <option value="EUR">Euro (EUR)</option>
                      <option value="GBP">British Pound (GBP)</option>
                      <option value="NZD">New Zealand Dollar (NZD)</option>
                    </select>
                  </div>

                  <div className="sm:col-span-2">
                    <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700">
                      Site Description
                    </label>
                    <textarea
                      id="siteDescription"
                      rows={3}
                      value={settings.general.siteDescription}
                      onChange={(e) => handleChange('general', 'siteDescription', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      id="contactEmail"
                      value={settings.general.contactEmail}
                      onChange={(e) => handleChange('general', 'contactEmail', e.target.value)}
                      className={`mt-1 block w-full border ${errors.contactEmail ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm`}
                    />
                    {errors.contactEmail && (
                      <p className="mt-1 text-sm text-red-600">{errors.contactEmail}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="supportPhone" className="block text-sm font-medium text-gray-700">
                      Support Phone (optional)
                    </label>
                    <input
                      type="text"
                      id="supportPhone"
                      value={settings.general.supportPhone}
                      onChange={(e) => handleChange('general', 'supportPhone', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Appearance Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Customize the look and feel of your website.</p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="primaryColor" className="block text-sm font-medium text-gray-700">
                      Primary Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="primaryColor"
                        value={settings.appearance.primaryColor}
                        onChange={(e) => handleChange('appearance', 'primaryColor', e.target.value)}
                        className="h-8 w-8 rounded-md border border-gray-300 cursor-pointer"
                      />
                      <input
                        type="text"
                        value={settings.appearance.primaryColor}
                        onChange={(e) => handleChange('appearance', 'primaryColor', e.target.value)}
                        className="ml-2 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="secondaryColor" className="block text-sm font-medium text-gray-700">
                      Secondary Color
                    </label>
                    <div className="mt-1 flex items-center">
                      <input
                        type="color"
                        id="secondaryColor"
                        value={settings.appearance.secondaryColor}
                        onChange={(e) => handleChange('appearance', 'secondaryColor', e.target.value)}
                        className="h-8 w-8 rounded-md border border-gray-300 cursor-pointer"
                      />
                      <input
                        type="text"
                        value={settings.appearance.secondaryColor}
                        onChange={(e) => handleChange('appearance', 'secondaryColor', e.target.value)}
                        className="ml-2 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="logoUrl" className="block text-sm font-medium text-gray-700">
                      Logo URL
                    </label>
                    <input
                      type="text"
                      id="logoUrl"
                      value={settings.appearance.logoUrl}
                      onChange={(e) => handleChange('appearance', 'logoUrl', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                      placeholder="https://example.com/logo.png"
                    />
                    {settings.appearance.logoUrl && (
                      <div className="mt-2">
                        <img
                          src={settings.appearance.logoUrl}
                          alt="Logo preview"
                          className="h-10 object-contain"
                          onError={(e) => e.target.style.display = 'none'}
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="faviconUrl" className="block text-sm font-medium text-gray-700">
                      Favicon URL
                    </label>
                    <input
                      type="text"
                      id="faviconUrl"
                      value={settings.appearance.faviconUrl}
                      onChange={(e) => handleChange('appearance', 'faviconUrl', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                      placeholder="https://example.com/favicon.ico"
                    />
                    {settings.appearance.faviconUrl && (
                      <div className="mt-2">
                        <img
                          src={settings.appearance.faviconUrl}
                          alt="Favicon preview"
                          className="h-6 w-6 object-contain"
                          onError={(e) => e.target.style.display = 'none'}
                        />
                      </div>
                    )}
                  </div>

                  <div className="sm:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableDarkMode"
                        checked={settings.appearance.enableDarkMode}
                        onChange={(e) => handleChange('appearance', 'enableDarkMode', e.target.checked)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableDarkMode" className="ml-2 block text-sm text-gray-700">
                        Enable dark mode option for users
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Email Settings */}
            {activeTab === 'email' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Email Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Configure email notifications and templates.</p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableEmailNotifications"
                        checked={settings.email.enableEmailNotifications}
                        onChange={(e) => handleChange('email', 'enableEmailNotifications', e.target.checked)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableEmailNotifications" className="ml-2 block text-sm text-gray-700">
                        Enable email notifications
                      </label>
                    </div>
                  </div>

                  {settings.email.enableEmailNotifications && (
                    <>
                      <div>
                        <label htmlFor="senderName" className="block text-sm font-medium text-gray-700">
                          Sender Name
                        </label>
                        <input
                          type="text"
                          id="senderName"
                          value={settings.email.senderName}
                          onChange={(e) => handleChange('email', 'senderName', e.target.value)}
                          className={`mt-1 block w-full border ${errors.senderName ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm`}
                        />
                        {errors.senderName && (
                          <p className="mt-1 text-sm text-red-600">{errors.senderName}</p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="senderEmail" className="block text-sm font-medium text-gray-700">
                          Sender Email
                        </label>
                        <input
                          type="email"
                          id="senderEmail"
                          value={settings.email.senderEmail}
                          onChange={(e) => handleChange('email', 'senderEmail', e.target.value)}
                          className={`mt-1 block w-full border ${errors.senderEmail ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm`}
                        />
                        {errors.senderEmail && (
                          <p className="mt-1 text-sm text-red-600">{errors.senderEmail}</p>
                        )}
                      </div>

                      <div className="sm:col-span-2">
                        <label htmlFor="bookingConfirmationTemplate" className="block text-sm font-medium text-gray-700">
                          Booking Confirmation Template
                        </label>
                        <textarea
                          id="bookingConfirmationTemplate"
                          rows={5}
                          value={settings.email.bookingConfirmationTemplate}
                          onChange={(e) => handleChange('email', 'bookingConfirmationTemplate', e.target.value)}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm font-mono"
                          placeholder="Dear {{name}},\n\nThank you for booking with HouseGoing. Your booking for {{venue}} on {{date}} has been confirmed.\n\nRegards,\nThe HouseGoing Team"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Available variables: {{name}}, {{venue}}, {{date}}, {{time}}, {{guests}}, {{total}}
                        </p>
                      </div>

                      <div className="sm:col-span-2">
                        <label htmlFor="hostNotificationTemplate" className="block text-sm font-medium text-gray-700">
                          Host Notification Template
                        </label>
                        <textarea
                          id="hostNotificationTemplate"
                          rows={5}
                          value={settings.email.hostNotificationTemplate}
                          onChange={(e) => handleChange('email', 'hostNotificationTemplate', e.target.value)}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm font-mono"
                          placeholder="Dear {{host}},\n\nYou have a new booking for {{venue}} on {{date}}. The guest has booked for {{guests}} people.\n\nRegards,\nThe HouseGoing Team"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Available variables: {{host}}, {{guest}}, {{venue}}, {{date}}, {{time}}, {{guests}}, {{total}}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Integrations Settings */}
            {activeTab === 'integrations' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Integrations Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Configure third-party service integrations.</p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="googleMapsApiKey" className="block text-sm font-medium text-gray-700">
                      Google Maps API Key
                    </label>
                    <input
                      type="text"
                      id="googleMapsApiKey"
                      value={settings.integrations.googleMapsApiKey}
                      onChange={(e) => handleChange('integrations', 'googleMapsApiKey', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Used for maps and location services.
                    </p>
                  </div>

                  <div>
                    <label htmlFor="stripePublicKey" className="block text-sm font-medium text-gray-700">
                      Stripe Public Key
                    </label>
                    <input
                      type="text"
                      id="stripePublicKey"
                      value={settings.integrations.stripePublicKey}
                      onChange={(e) => handleChange('integrations', 'stripePublicKey', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Used for payment processing.
                    </p>
                  </div>

                  <div>
                    <label htmlFor="stripeSecretKey" className="block text-sm font-medium text-gray-700">
                      Stripe Secret Key
                    </label>
                    <input
                      type="password"
                      id="stripeSecretKey"
                      value={settings.integrations.stripeSecretKey}
                      onChange={(e) => handleChange('integrations', 'stripeSecretKey', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Keep this secret and never expose it to the client.
                    </p>
                  </div>

                  <div>
                    <label htmlFor="huggingFaceApiKey" className="block text-sm font-medium text-gray-700">
                      Hugging Face API Key
                    </label>
                    <input
                      type="password"
                      id="huggingFaceApiKey"
                      value={settings.integrations.huggingFaceApiKey}
                      onChange={(e) => handleChange('integrations', 'huggingFaceApiKey', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Used for AI features and chatbot.
                    </p>
                  </div>

                  <div>
                    <label htmlFor="langChainApiKey" className="block text-sm font-medium text-gray-700">
                      LangChain API Key
                    </label>
                    <input
                      type="password"
                      id="langChainApiKey"
                      value={settings.integrations.langChainApiKey}
                      onChange={(e) => handleChange('integrations', 'langChainApiKey', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Used for AI assistant features.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Settings */}
            {activeTab === 'advanced' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Advanced Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Configure advanced system settings. Use with caution.</p>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableCaching"
                        checked={settings.advanced.enableCaching}
                        onChange={(e) => handleChange('advanced', 'enableCaching', e.target.checked)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableCaching" className="ml-2 block text-sm text-gray-700">
                        Enable page caching
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 ml-6">
                      Improves performance but may cause delays in content updates.
                    </p>
                  </div>

                  {settings.advanced.enableCaching && (
                    <div>
                      <label htmlFor="cacheLifetime" className="block text-sm font-medium text-gray-700">
                        Cache Lifetime (seconds)
                      </label>
                      <input
                        type="number"
                        id="cacheLifetime"
                        value={settings.advanced.cacheLifetime}
                        onChange={(e) => handleChange('advanced', 'cacheLifetime', parseInt(e.target.value) || 0)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                        min="0"
                      />
                    </div>
                  )}

                  <div className="sm:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="debugMode"
                        checked={settings.advanced.debugMode}
                        onChange={(e) => handleChange('advanced', 'debugMode', e.target.checked)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <label htmlFor="debugMode" className="ml-2 block text-sm text-gray-700">
                        Enable debug mode
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 ml-6">
                      Shows detailed error messages and logs additional information.
                    </p>
                  </div>

                  <div className="sm:col-span-2 border-t pt-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="maintenanceMode"
                        checked={settings.advanced.maintenanceMode}
                        onChange={(e) => handleChange('advanced', 'maintenanceMode', e.target.checked)}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                      />
                      <label htmlFor="maintenanceMode" className="ml-2 block text-sm font-medium text-red-700">
                        Enable maintenance mode
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-red-500 ml-6">
                      This will make the site inaccessible to regular users. Only administrators will be able to access the site.
                    </p>
                  </div>

                  {settings.advanced.maintenanceMode && (
                    <div className="sm:col-span-2">
                      <label htmlFor="maintenanceMessage" className="block text-sm font-medium text-gray-700">
                        Maintenance Message
                      </label>
                      <textarea
                        id="maintenanceMessage"
                        rows={3}
                        value={settings.advanced.maintenanceMessage}
                        onChange={(e) => handleChange('advanced', 'maintenanceMessage', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
