/**
 * Custom HMR (Hot Module Replacement) client
 *
 * This file provides a more robust HMR client that handles WebSocket connection issues
 * and provides better error reporting.
 */

// Get the HMR port from environment variables or use a default
const HMR_PORT = import.meta.env.VITE_DEV_SERVER_PORT || 5173;
const HMR_HOST = import.meta.env.VITE_DEV_SERVER_HOST || 'localhost';

// Create a WebSocket connection to the Vite dev server
let socket;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

function setupWebSocket() {
  try {
    // Only setup WebSocket in development mode
    if (import.meta.env.DEV) {
      // If we've exceeded max reconnect attempts, don't try again
      if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        console.log('[HMR] Maximum reconnection attempts reached. HMR disabled.');
        return;
      }

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const url = `${protocol}//${HMR_HOST}:${HMR_PORT}`;

      console.log(`[HMR] Connecting to WebSocket at ${url} (attempt ${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);

      // Create the socket with a timeout
      socket = new WebSocket(url);

      // Set a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (socket.readyState !== WebSocket.OPEN) {
          console.log('[HMR] Connection timeout, closing socket');
          socket.close();
        }
      }, 5000);

      socket.addEventListener('open', () => {
        console.log('[HMR] WebSocket connected');
        clearTimeout(connectionTimeout);
        reconnectAttempts = 0; // Reset reconnect attempts on successful connection
      });

      socket.addEventListener('message', (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'update') {
            // Handle module updates more gracefully
            console.log('[HMR] Update received, refreshing page');
            // Use a small delay to allow console messages to be seen
            setTimeout(() => window.location.reload(), 100);
          }
        } catch (err) {
          console.error('[HMR] Error processing message:', err);
        }
      });

      socket.addEventListener('close', () => {
        console.log('[HMR] WebSocket disconnected');
        clearTimeout(connectionTimeout);

        // Increment reconnect attempts
        reconnectAttempts++;

        // Use exponential backoff for reconnection
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 30000);
        console.log(`[HMR] Attempting to reconnect in ${delay/1000}s...`);

        setTimeout(setupWebSocket, delay);
      });

      socket.addEventListener('error', (error) => {
        console.error('[HMR] WebSocket error:', error);
        // Don't close the socket here, let the 'close' event handle reconnection
      });
    }
  } catch (error) {
    console.error('[HMR] Failed to setup WebSocket:', error);
    reconnectAttempts++;

    // Try again with backoff
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 30000);
    setTimeout(setupWebSocket, delay);
  }
}

// Initialize the WebSocket connection
setupWebSocket();

// Export a function to manually trigger a reload
export function triggerReload() {
  window.location.reload();
}

// Export the socket for external use
export { socket };
