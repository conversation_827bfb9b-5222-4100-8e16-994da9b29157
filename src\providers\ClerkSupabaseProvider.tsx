import React, { useEffect, ReactNode, useState } from 'react';
import { useAuth } from '@clerk/clerk-react';
import { supabase } from '../lib/supabase-client';
import { validateSupabaseToken } from '../utils/clerk-supabase-template';

interface ClerkSupabaseProviderProps {
  children: ReactNode;
}

/**
 * ClerkSupabaseProvider
 *
 * This component sets the Clerk JWT token on the Supabase client
 * whenever the user changes. It should be placed inside the ClerkProvider
 * but outside of any components that need to use Supabase.
 */
export function ClerkSupabaseProvider({ children }: ClerkSupabaseProviderProps) {
  const { getToken, isLoaded, userId } = useAuth();
  const [tokenValidationResult, setTokenValidationResult] = useState<{
    valid: boolean;
    missingClaims: string[];
    message: string;
  } | null>(null);

  // Set the Clerk token on the Supabase client whenever the user changes
  useEffect(() => {
    if (!isLoaded) return;

    const setSupabaseToken = async () => {
      try {
        if (userId) {
          console.log('Setting Clerk token on Supabase client for user:', userId);

          // Get the token with NATIVE INTEGRATION (no template needed)
          const token = await getToken();
          console.log("Clerk token retrieved (native):", token ? token.substring(0, 20) + '...' : 'null');

          // Native integration doesn't need token validation
          if (token) {
            console.log("✅ Clerk token obtained successfully with native integration");
          }

          // Also try to get token directly from window.Clerk (NATIVE INTEGRATION)
          if (typeof window !== 'undefined' && window.Clerk && window.Clerk.session) {
            const directToken = await window.Clerk.session.getToken();
            console.log("Clerk token from window.Clerk (native):", directToken ? directToken.substring(0, 20) + '...' : 'null');
          }

          if (token) {
            // Set the token on the Supabase client
            try {
              // Use the correct method based on Supabase version
              if (typeof supabase.auth.setAuth === 'function') {
                // For older versions of Supabase
                (supabase.auth as any).setAuth(token);
              } else if (typeof supabase.auth.setSession === 'function') {
                // For newer versions of Supabase
                await supabase.auth.setSession({
                  access_token: token,
                  refresh_token: ''
                });
              } else {
                // Fallback - set the token in the headers
                (supabase as any).headers = {
                  ...(supabase as any).headers,
                  'Authorization': `Bearer ${token}`
                };
              }

              console.log('Successfully set Clerk token on Supabase client');

              // Dispatch an event to notify the app that authentication is complete
              window.dispatchEvent(new CustomEvent('auth_complete', {
                detail: {
                  success: true,
                  provider: 'clerk-supabase-direct'
                }
              }));
            } catch (error) {
              console.error('Error setting Clerk token on Supabase client:', error);
            }
          } else {
            console.warn('No Clerk token available for Supabase');
          }
        } else {
          // Only clear the session if we're not in the middle of authentication
          const isAuthenticating = window.location.pathname.includes('/auth/callback') ||
                                  localStorage.getItem('auth_processing') === 'true';

          if (!isAuthenticating) {
            console.log('No authenticated user and not in authentication flow, clearing Supabase session');

            // Clear the Supabase session when the user logs out
            try {
              await supabase.auth.signOut();
            } catch (error) {
              console.error('Error clearing Supabase session:', error);
            }
          } else {
            console.log('No authenticated user but in authentication flow, skipping session clear');
          }
        }
      } catch (error) {
        console.error('Error in setSupabaseToken:', error);
      }
    };

    setSupabaseToken();
  }, [isLoaded, userId, getToken]);

  // Show a warning if the token is invalid
  useEffect(() => {
    if (tokenValidationResult && !tokenValidationResult.valid) {
      console.error("⚠️ Clerk JWT template for Supabase is not configured correctly!");
      console.error("Missing claims:", tokenValidationResult.missingClaims);
      console.error("Message:", tokenValidationResult.message);
      console.error("Please update your JWT template in the Clerk Dashboard.");
    }
  }, [tokenValidationResult]);

  return (
    <>
      {tokenValidationResult && !tokenValidationResult.valid && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          backgroundColor: '#FEF2F2',
          color: '#B91C1C',
          padding: '12px 16px',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          zIndex: 9999,
          maxWidth: '400px',
          fontSize: '14px'
        }}>
          <strong>Authentication Error:</strong> Clerk JWT template is not configured correctly.
          <div style={{ marginTop: '8px' }}>
            {tokenValidationResult.message}
          </div>
          <div style={{ marginTop: '8px', fontSize: '12px' }}>
            Please check the browser console for instructions.
          </div>
        </div>
      )}
      {children}
    </>
  );
}

export default ClerkSupabaseProvider;
