CREATE TABLE special_event_zones (
  id SERIAL PRIMARY KEY,
  zone_name VARCHAR(100) NOT NULL UNIQUE,
  standard_start TIME,
  standard_end TIME,
  extended_start TIME,
  extended_end TIME,
  special_conditions TEXT,
  lga_name VARCHAR(100) REFERENCES lga_rules(lga_name)
);

INSERT INTO special_event_zones (zone_name, standard_start, standard_end, extended_start, extended_end, special_conditions, lga_name)
VALUES
  ('Enmore Road', '07:00:00', '00:00:00', '07:00:00', '02:00:00', 'Sound attenuation requirements for venues', 'Inner West Council'),
  ('Kings Cross', '07:00:00', '01:30:00', '07:00:00', '03:30:00', 'Subject to freeze on new liquor licenses', 'City of Sydney'),
  ('Byron Bay', '07:00:00', '00:00:00', '07:00:00', '02:00:00', 'First regional SEP in NSW', 'Byron Shire Council'),
  ('Darlinghurst', '07:00:00', '00:00:00', '07:00:00', '02:00:00', 'Special provisions for Oxford Street cultural precinct', 'City of Sydney');
