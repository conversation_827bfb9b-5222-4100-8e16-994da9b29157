<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing Supabase Setup</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #6366f1;
    }
    button {
      background-color: #6366f1;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    button:hover {
      background-color: #4f46e5;
    }
    button:disabled {
      background-color: #a5a6f6;
      cursor: not-allowed;
    }
    .log {
      margin-top: 20px;
      border: 1px solid #e2e8f0;
      padding: 15px;
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;
      background-color: #f8fafc;
      font-family: monospace;
    }
    .success {
      color: #10b981;
    }
    .error {
      color: #ef4444;
    }
    .warning {
      color: #f59e0b;
    }
    .info {
      color: #3b82f6;
    }
    .step {
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f1f5f9;
    }
    .step.completed {
      background-color: #ecfdf5;
      border-left: 4px solid #10b981;
    }
    .step.failed {
      background-color: #fef2f2;
      border-left: 4px solid #ef4444;
    }
    .step.pending {
      background-color: #f8fafc;
      border-left: 4px solid #94a3b8;
    }
    .step-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .step-status {
      font-size: 14px;
      margin-top: 5px;
    }
    .table-info {
      margin-top: 20px;
      border-collapse: collapse;
      width: 100%;
    }
    .table-info th, .table-info td {
      border: 1px solid #e2e8f0;
      padding: 8px;
      text-align: left;
    }
    .table-info th {
      background-color: #f1f5f9;
    }
    .table-info tr:nth-child(even) {
      background-color: #f8fafc;
    }
  </style>
</head>
<body>
  <h1>HouseGoing Supabase Database Setup</h1>
  <p>This tool will set up the Supabase database for the HouseGoing application using the Supabase API directly.</p>
  
  <div>
    <h2>Configuration</h2>
    <div>
      <label for="supabaseUrl">Supabase URL:</label>
      <input type="text" id="supabaseUrl" value="https://fxqoowlruissctsgbljk.supabase.co" style="width: 100%; padding: 8px; margin: 5px 0;">
    </div>
    <div>
      <label for="supabaseKey">Supabase Service Role Key:</label>
      <input type="password" id="supabaseKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk0MzI1NCwiZXhwIjoyMDU5NTE5MjU0fQ.9qjOAPoSojpa-91l9jlAI79Rkw3Och4gCjuTWM6KBLI" style="width: 100%; padding: 8px; margin: 5px 0;">
    </div>
  </div>
  
  <button id="setupButton">Set Up Database</button>
  
  <div id="steps">
    <h2>Setup Steps</h2>
    <div id="step1" class="step pending">
      <div class="step-title">1. Test Connection</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step2" class="step pending">
      <div class="step-title">2. Create pg_query Function</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step3" class="step pending">
      <div class="step-title">3. Create Trigger Function</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step4" class="step pending">
      <div class="step-title">4. Create user_profiles Table</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step5" class="step pending">
      <div class="step-title">5. Create venues Table</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step6" class="step pending">
      <div class="step-title">6. Create bookings Table</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step7" class="step pending">
      <div class="step-title">7. Create reviews Table</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step8" class="step pending">
      <div class="step-title">8. Create messages Table</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step9" class="step pending">
      <div class="step-title">9. Set Up Row Level Security</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step10" class="step pending">
      <div class="step-title">10. Insert Pre-registered Host</div>
      <div class="step-status">Pending</div>
    </div>
    <div id="step11" class="step pending">
      <div class="step-title">11. Verify Tables</div>
      <div class="step-status">Pending</div>
    </div>
  </div>
  
  <div id="tableInfo" style="display: none;">
    <h2>Database Tables</h2>
    <table class="table-info">
      <thead>
        <tr>
          <th>Table</th>
          <th>Status</th>
          <th>Row Count</th>
        </tr>
      </thead>
      <tbody id="tableInfoBody">
      </tbody>
    </table>
  </div>
  
  <h2>Log</h2>
  <div id="log" class="log"></div>
  
  <script>
    // SQL statements for database setup
    const SQL_STATEMENTS = {
      // Create the pg_query function
      createPgQueryFunction: `
        CREATE OR REPLACE FUNCTION pg_query(query text)
        RETURNS JSONB
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE query;
          RETURN jsonb_build_object('success', true);
        EXCEPTION WHEN OTHERS THEN
          RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM
          );
        END;
        $$;

        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION pg_query TO authenticated;
        GRANT EXECUTE ON FUNCTION pg_query TO anon;
        GRANT EXECUTE ON FUNCTION pg_query TO service_role;
      `,

      // Create the updated_at trigger function
      createTriggerFunction: `
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `,

      // Create user_profiles table
      createUserProfilesTable: `
        CREATE TABLE IF NOT EXISTS user_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          clerk_id TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL DEFAULT 'guest',
          full_name TEXT,
          avatar_url TEXT,
          bio TEXT,
          phone TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);
        CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

        DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
        CREATE TRIGGER update_user_profiles_updated_at
        BEFORE UPDATE ON user_profiles
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `,

      // Create venues table
      createVenuesTable: `
        CREATE TABLE IF NOT EXISTS venues (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          owner_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
          title TEXT NOT NULL,
          description TEXT,
          location TEXT NOT NULL,
          address TEXT NOT NULL,
          city TEXT NOT NULL,
          state TEXT NOT NULL,
          zip_code TEXT NOT NULL,
          coordinates JSONB,
          price DECIMAL(10,2) NOT NULL,
          capacity INTEGER NOT NULL,
          amenities JSONB,
          house_rules TEXT,
          images TEXT[],
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_venues_owner_id ON venues(owner_id);
        CREATE INDEX IF NOT EXISTS idx_venues_location ON venues(location);

        DROP TRIGGER IF EXISTS update_venues_updated_at ON venues;
        CREATE TRIGGER update_venues_updated_at
        BEFORE UPDATE ON venues
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `,

      // Create bookings table
      createBookingsTable: `
        CREATE TABLE IF NOT EXISTS bookings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
          guest_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
          start_date TIMESTAMP WITH TIME ZONE NOT NULL,
          end_date TIMESTAMP WITH TIME ZONE NOT NULL,
          guests_count INTEGER NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_bookings_venue_id ON bookings(venue_id);
        CREATE INDEX IF NOT EXISTS idx_bookings_guest_id ON bookings(guest_id);
        CREATE INDEX IF NOT EXISTS idx_bookings_dates ON bookings(start_date, end_date);

        DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
        CREATE TRIGGER update_bookings_updated_at
        BEFORE UPDATE ON bookings
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `,

      // Create reviews table
      createReviewsTable: `
        CREATE TABLE IF NOT EXISTS reviews (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
          venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
          reviewer_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
          rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
          comment TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON reviews(booking_id);
        CREATE INDEX IF NOT EXISTS idx_reviews_venue_id ON reviews(venue_id);
        CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);

        DROP TRIGGER IF EXISTS update_reviews_updated_at ON reviews;
        CREATE TRIGGER update_reviews_updated_at
        BEFORE UPDATE ON reviews
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `,

      // Create messages table
      createMessagesTable: `
        CREATE TABLE IF NOT EXISTS messages (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
          sender_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
          recipient_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
          content TEXT NOT NULL,
          is_read BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_messages_booking_id ON messages(booking_id);
        CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
        CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON messages(recipient_id);

        DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
        CREATE TRIGGER update_messages_updated_at
        BEFORE UPDATE ON messages
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `,

      // Enable RLS and create policies
      setupRLS: `
        -- Enable RLS on all tables
        ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE venues ENABLE ROW LEVEL SECURITY;
        ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
        ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
        ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies for user_profiles
        CREATE POLICY IF NOT EXISTS read_own_profile ON user_profiles
          FOR SELECT
          USING (auth.uid()::text = clerk_id);

        CREATE POLICY IF NOT EXISTS update_own_profile ON user_profiles
          FOR UPDATE
          USING (auth.uid()::text = clerk_id);

        CREATE POLICY IF NOT EXISTS service_read_all_profiles ON user_profiles
          FOR SELECT
          TO service_role
          USING (true);

        CREATE POLICY IF NOT EXISTS service_update_all_profiles ON user_profiles
          FOR UPDATE
          TO service_role
          USING (true);

        CREATE POLICY IF NOT EXISTS service_insert_profiles ON user_profiles
          FOR INSERT
          TO service_role
          WITH CHECK (true);

        -- Create RLS policies for venues
        CREATE POLICY IF NOT EXISTS read_venues ON venues
          FOR SELECT
          USING (true);

        CREATE POLICY IF NOT EXISTS insert_own_venues ON venues
          FOR INSERT
          WITH CHECK (owner_id = auth.uid()::text);

        CREATE POLICY IF NOT EXISTS update_own_venues ON venues
          FOR UPDATE
          USING (owner_id = auth.uid()::text);

        CREATE POLICY IF NOT EXISTS delete_own_venues ON venues
          FOR DELETE
          USING (owner_id = auth.uid()::text);

        -- Create RLS policies for bookings
        CREATE POLICY IF NOT EXISTS read_own_bookings ON bookings
          FOR SELECT
          USING (guest_id = auth.uid()::text OR EXISTS (
            SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
          ));

        CREATE POLICY IF NOT EXISTS insert_bookings ON bookings
          FOR INSERT
          WITH CHECK (guest_id = auth.uid()::text);

        CREATE POLICY IF NOT EXISTS update_own_bookings ON bookings
          FOR UPDATE
          USING (guest_id = auth.uid()::text OR EXISTS (
            SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
          ));
      `,

      // Insert pre-registered host
      insertPreregisteredHost: `
        INSERT INTO user_profiles (clerk_id, email, role, full_name)
        VALUES 
          ('pre-registered-host-1', '<EMAIL>', 'host', 'Tom C')
        ON CONFLICT (email) 
        DO UPDATE SET role = 'host', updated_at = NOW();
      `
    };

    // DOM elements
    const setupButton = document.getElementById('setupButton');
    const logElement = document.getElementById('log');
    const tableInfoElement = document.getElementById('tableInfo');
    const tableInfoBodyElement = document.getElementById('tableInfoBody');
    
    // Tables to verify
    const TABLES_TO_VERIFY = [
      'user_profiles',
      'venues',
      'bookings',
      'reviews',
      'messages'
    ];
    
    // Log function
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logEntry.classList.add(type);
      logElement.appendChild(logEntry);
      logElement.scrollTop = logElement.scrollHeight;
    }
    
    // Update step status
    function updateStepStatus(stepNumber, status, message) {
      const stepElement = document.getElementById(`step${stepNumber}`);
      const statusElement = stepElement.querySelector('.step-status');
      
      stepElement.className = `step ${status}`;
      statusElement.textContent = message;
      
      if (status === 'completed') {
        statusElement.classList.add('success');
      } else if (status === 'failed') {
        statusElement.classList.add('error');
      }
    }
    
    // Create Supabase client
    function createSupabaseClient() {
      const supabaseUrl = document.getElementById('supabaseUrl').value;
      const supabaseKey = document.getElementById('supabaseKey').value;
      
      return supabase.createClient(supabaseUrl, supabaseKey);
    }
    
    // Execute SQL using the Supabase REST API
    async function executeSQL(supabase, sql, description, stepNumber) {
      log(`Executing: ${description}...`);
      updateStepStatus(stepNumber, 'pending', 'In progress...');
      
      try {
        // Try using the pg_query function if it exists
        const { data, error } = await supabase.rpc('pg_query', { query: sql });
        
        if (error) {
          log(`Error executing ${description}: ${error.message}`, 'error');
          updateStepStatus(stepNumber, 'failed', `Failed: ${error.message}`);
          return false;
        }
        
        log(`${description} completed successfully`, 'success');
        updateStepStatus(stepNumber, 'completed', 'Completed successfully');
        return true;
      } catch (error) {
        // If pg_query doesn't exist, try direct SQL
        try {
          log(`Trying alternative method for ${description}...`, 'warning');
          
          // Use direct fetch for SQL execution
          const supabaseUrl = document.getElementById('supabaseUrl').value;
          const supabaseKey = document.getElementById('supabaseKey').value;
          
          const response = await fetch(`${supabaseUrl}/rest/v1/rpc/pg_query`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${supabaseKey}`,
              'apikey': supabaseKey
            },
            body: JSON.stringify({ query: sql })
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            log(`Alternative method failed: ${errorText}`, 'error');
            updateStepStatus(stepNumber, 'failed', `Failed: ${errorText}`);
            return false;
          }
          
          log(`${description} completed successfully (alternative method)`, 'success');
          updateStepStatus(stepNumber, 'completed', 'Completed successfully (alternative method)');
          return true;
        } catch (altError) {
          log(`Exception executing ${description}: ${altError.message}`, 'error');
          updateStepStatus(stepNumber, 'failed', `Failed: ${altError.message}`);
          return false;
        }
      }
    }
    
    // Check if a table exists
    async function checkTableExists(supabase, tableName) {
      try {
        const { data, error } = await supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .eq('table_name', tableName);
        
        if (error) {
          log(`Error checking if table ${tableName} exists: ${error.message}`, 'error');
          return false;
        }
        
        return data && data.length > 0;
      } catch (error) {
        log(`Exception checking if table ${tableName} exists: ${error.message}`, 'error');
        return false;
      }
    }
    
    // Count rows in a table
    async function countRows(supabase, tableName) {
      try {
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          log(`Error counting rows in ${tableName}: ${error.message}`, 'error');
          return 0;
        }
        
        return count || 0;
      } catch (error) {
        log(`Exception counting rows in ${tableName}: ${error.message}`, 'error');
        return 0;
      }
    }
    
    // Verify tables
    async function verifyTables(supabase) {
      log('Verifying tables...');
      updateStepStatus(11, 'pending', 'In progress...');
      
      try {
        // Check if each table exists
        const results = await Promise.all(
          TABLES_TO_VERIFY.map(async (tableName) => {
            const exists = await checkTableExists(supabase, tableName);
            const count = exists ? await countRows(supabase, tableName) : 0;
            return { tableName, exists, count };
          })
        );
        
        // Check if all tables exist
        const allTablesExist = results.every((result) => result.exists);
        
        // Update table info
        tableInfoElement.style.display = 'block';
        tableInfoBodyElement.innerHTML = '';
        
        results.forEach((result) => {
          const row = document.createElement('tr');
          
          const nameCell = document.createElement('td');
          nameCell.textContent = result.tableName;
          
          const statusCell = document.createElement('td');
          statusCell.textContent = result.exists ? 'Exists' : 'Missing';
          statusCell.className = result.exists ? 'success' : 'error';
          
          const countCell = document.createElement('td');
          countCell.textContent = result.count;
          
          row.appendChild(nameCell);
          row.appendChild(statusCell);
          row.appendChild(countCell);
          
          tableInfoBodyElement.appendChild(row);
          
          log(`Table ${result.tableName}: ${result.exists ? 'Exists' : 'Missing'} (${result.count} rows)`, result.exists ? 'success' : 'error');
        });
        
        if (allTablesExist) {
          log('All tables verified successfully', 'success');
          updateStepStatus(11, 'completed', 'All tables verified successfully');
          return true;
        } else {
          log('Some tables are missing', 'error');
          updateStepStatus(11, 'failed', 'Some tables are missing');
          return false;
        }
      } catch (error) {
        log(`Exception verifying tables: ${error.message}`, 'error');
        updateStepStatus(11, 'failed', `Failed: ${error.message}`);
        return false;
      }
    }
    
    // Main setup function
    async function setupDatabase() {
      // Disable the setup button
      setupButton.disabled = true;
      
      // Clear the log
      logElement.innerHTML = '';
      
      // Create Supabase client
      const supabase = createSupabaseClient();
      
      // Step 1: Test connection
      log('Testing connection to Supabase...');
      updateStepStatus(1, 'pending', 'In progress...');
      
      try {
        const { data, error } = await supabase.from('pg_catalog.pg_tables').select('*').limit(1);
        
        if (error) {
          log(`Failed to connect to Supabase: ${error.message}`, 'error');
          updateStepStatus(1, 'failed', `Failed: ${error.message}`);
          setupButton.disabled = false;
          return;
        }
        
        log('Connected to Supabase successfully', 'success');
        updateStepStatus(1, 'completed', 'Connected successfully');
      } catch (error) {
        log(`Exception connecting to Supabase: ${error.message}`, 'error');
        updateStepStatus(1, 'failed', `Failed: ${error.message}`);
        setupButton.disabled = false;
        return;
      }
      
      // Step 2: Create pg_query function
      await executeSQL(supabase, SQL_STATEMENTS.createPgQueryFunction, 'Creating pg_query function', 2);
      
      // Step 3: Create trigger function
      await executeSQL(supabase, SQL_STATEMENTS.createTriggerFunction, 'Creating trigger function', 3);
      
      // Step 4: Create user_profiles table
      await executeSQL(supabase, SQL_STATEMENTS.createUserProfilesTable, 'Creating user_profiles table', 4);
      
      // Step 5: Create venues table
      await executeSQL(supabase, SQL_STATEMENTS.createVenuesTable, 'Creating venues table', 5);
      
      // Step 6: Create bookings table
      await executeSQL(supabase, SQL_STATEMENTS.createBookingsTable, 'Creating bookings table', 6);
      
      // Step 7: Create reviews table
      await executeSQL(supabase, SQL_STATEMENTS.createReviewsTable, 'Creating reviews table', 7);
      
      // Step 8: Create messages table
      await executeSQL(supabase, SQL_STATEMENTS.createMessagesTable, 'Creating messages table', 8);
      
      // Step 9: Set up RLS
      await executeSQL(supabase, SQL_STATEMENTS.setupRLS, 'Setting up Row Level Security', 9);
      
      // Step 10: Insert pre-registered host
      await executeSQL(supabase, SQL_STATEMENTS.insertPreregisteredHost, 'Inserting pre-registered host', 10);
      
      // Step 11: Verify tables
      await verifyTables(supabase);
      
      // Re-enable the setup button
      setupButton.disabled = false;
      
      log('Database setup completed!', 'success');
    }
    
    // Add event listener to the setup button
    setupButton.addEventListener('click', setupDatabase);
  </script>
</body>
</html>
