import React from 'react';
import { useAuth } from '../providers/AuthProvider';
import GoogleLoginButton from '../components/auth/GoogleLoginButton';
import EmailLoginForm from '../components/auth/EmailLoginForm';

export default function TestHybridAuth() {
  const { 
    isAuthenticated, 
    user, 
    session,
    authProvider, 
    customerData,
    isLoading,
    error,
    signOut 
  } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p>Loading authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Hybrid Auth.js + Supabase Test</h1>
        
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-600">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {isAuthenticated ? (
          <div className="space-y-6">
            {/* Authentication Status */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-green-800 mb-4">✅ Authenticated</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>Provider:</strong> {authProvider}</p>
                  <p><strong>Email:</strong> {user?.email}</p>
                  <p><strong>Name:</strong> {user?.name || 'Not set'}</p>
                </div>
                <div>
                  <p><strong>User ID:</strong> {user?.id || 'N/A'}</p>
                  <p><strong>Customer ID:</strong> {customerData?.id || 'Not found'}</p>
                </div>
              </div>
              
              <button
                onClick={signOut}
                className="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
              >
                Sign Out
              </button>
            </div>

            {/* Session Data */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Session Data</h2>
              <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-60">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>

            {/* Customer Data */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Customer Data (Supabase)</h2>
              {customerData ? (
                <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-60">
                  {JSON.stringify(customerData, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500">No customer data found</p>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Login Options */}
            <div className="bg-white rounded-lg shadow p-8">
              <h2 className="text-xl font-semibold mb-6">Sign In Options</h2>
              
              <div className="space-y-6">
                {/* Google Login */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Google OAuth (via Auth.js)</h3>
                  <GoogleLoginButton />
                  <p className="text-sm text-gray-500 mt-2">
                    Uses Auth.js backend server for Google OAuth authentication
                  </p>
                </div>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or</span>
                  </div>
                </div>

                {/* Email Login */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Email Magic Link (via Supabase)</h3>
                  <EmailLoginForm />
                  <p className="text-sm text-gray-500 mt-2">
                    Uses Supabase Auth for email/magic link authentication
                  </p>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">System Status</h2>
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span>Frontend: Running on port 5176</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span>Auth Server: Running on port 3001</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <span>Supabase: Connected</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Debug Information */}
        <div className="mt-8 bg-gray-100 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="text-sm space-y-1">
            <p><strong>Auth Provider:</strong> {authProvider || 'None'}</p>
            <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            <p><strong>Has Error:</strong> {error ? 'Yes' : 'No'}</p>
            <p><strong>User Object:</strong> {user ? 'Present' : 'Null'}</p>
            <p><strong>Session Object:</strong> {session ? 'Present' : 'Null'}</p>
            <p><strong>Customer Data:</strong> {customerData ? 'Present' : 'Null'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
