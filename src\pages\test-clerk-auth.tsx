import React, { useState } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { supabase, createClerkSupabaseClient } from '../lib/supabase-client';

export default function TestClerkAuth() {
  const { user, isLoaded, isSignedIn } = useUser();
  const { getToken } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results: any[] = [];

    // Test 1: Check Clerk user data
    results.push({
      test: 'Clerk User Data',
      status: isSignedIn ? 'PASS' : 'FAIL',
      data: user ? {
        id: user.id,
        email: user.primaryEmailAddress?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName
      } : null
    });

    // Test 2: Get Clerk token
    try {
      const token = await getToken({ template: 'supabase' });
      results.push({
        test: 'Clerk Token Generation',
        status: token ? 'PASS' : 'FAIL',
        data: token ? `${token.substring(0, 20)}...` : 'No token'
      });

      // Test 3: Test Supabase with Clerk token (using official pattern)
      if (token) {
        try {
          const clerkSupabase = createClerkSupabaseClient(async () => token);
          const { data, error } = await clerkSupabase
            .from('user_profiles')
            .select('count(*)')
            .limit(1);

          results.push({
            test: 'Supabase with Clerk Token (Official Pattern)',
            status: error ? 'FAIL' : 'PASS',
            data: error ? error.message : 'Successfully connected'
          });
        } catch (error) {
          results.push({
            test: 'Supabase with Clerk Token (Official Pattern)',
            status: 'FAIL',
            data: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } catch (error) {
      results.push({
        test: 'Clerk Token Generation',
        status: 'FAIL',
        data: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Test regular Supabase connection
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count(*)')
        .limit(1);

      results.push({
        test: 'Regular Supabase Connection',
        status: error ? 'FAIL' : 'PASS',
        data: error ? error.message : 'Successfully connected'
      });
    } catch (error) {
      results.push({
        test: 'Regular Supabase Connection',
        status: 'FAIL',
        data: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  if (!isLoaded) {
    return <div className="p-8">Loading Clerk...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Clerk Authentication Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Signed In:</span>
              <span className={`ml-2 px-2 py-1 rounded text-sm ${isSignedIn ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {isSignedIn ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">User Email:</span>
              <span className="ml-2">{user?.primaryEmailAddress?.emailAddress || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Integration Tests</h2>
            <button
              onClick={runTests}
              disabled={loading || !isSignedIn}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Running Tests...' : 'Run Tests'}
            </button>
          </div>

          {!isSignedIn && (
            <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-4">
              <p className="text-yellow-800">Please sign in to run integration tests.</p>
              <a href="/login" className="text-purple-600 hover:text-purple-800 underline">
                Go to Login
              </a>
            </div>
          )}

          {testResults.length > 0 && (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">{result.test}</h3>
                    <span className={`px-2 py-1 rounded text-sm ${
                      result.status === 'PASS' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  <pre className="bg-gray-50 p-2 rounded text-sm overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded p-4">
          <h3 className="font-medium text-blue-900 mb-2">Setup Requirements</h3>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>• Clerk JWT template named 'supabase' must be configured</li>
            <li>• Supabase must be configured to accept Clerk JWT tokens</li>
            <li>• Environment variables must be properly set</li>
            <li>• This will only work in production with proper domain configuration</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
