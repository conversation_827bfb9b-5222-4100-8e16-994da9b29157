CREATE TABLE suburb_classifications (
  id SERIAL PRIMARY KEY,
  suburb_name VARCHAR(100) NOT NULL,
  classification VARCHAR(50) NOT NULL,
  lga_name VARCHAR(100) REFERENCES lga_rules(lga_name)
);

INSERT INTO suburb_classifications (suburb_name, classification, lga_name)
VALUES
  ('Sydney CBD', 'High-Density Urban', 'City of Sydney'),
  ('North Sydney', 'High-Density Urban', 'North Sydney Council'),
  ('Chatswood', 'High-Density Urban', 'Willoughby City Council'),
  ('Paddington', 'Medium-Density Urban', 'City of Sydney'),
  ('Newtown', 'Medium-Density Urban', 'Inner West Council'),
  ('Castle Hill', 'Low-Density Suburban', 'The Hills Shire'),
  ('Baulkham Hills', 'Low-Density Suburban', 'The Hills Shire'),
  ('Silverwater', 'Industrial', 'City of Parramatta'),
  ('Manly', 'Tourist', 'Northern Beaches Council'),
  ('Byron Bay', 'Tourist', 'Byron Shire Council');
