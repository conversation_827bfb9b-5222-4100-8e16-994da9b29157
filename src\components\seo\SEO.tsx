import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noindex?: boolean;
  children?: React.ReactNode;
}

/**
 * SEO component for managing all meta tags and document head elements
 * 
 * @param {string} title - The page title
 * @param {string} description - Meta description
 * @param {string} keywords - Meta keywords
 * @param {string} image - OG image URL
 * @param {string} url - Canonical URL
 * @param {string} type - OG type (website, article, etc.)
 * @param {boolean} noindex - Whether to add noindex meta tag
 * @param {React.ReactNode} children - Additional head elements
 */
export default function SEO({
  title = 'Find Your Perfect Venue | HouseGoing',
  description = 'HouseGoing helps you find the perfect venue for your next party or event in NSW, Australia. Browse venues, check noise restrictions, and book with confidence.',
  keywords = 'venue rental, party venues, event spaces, NSW party planning, noise restrictions, venue booking',
  image = 'https://housegoing.com.au/og-image.svg',
  url = 'https://housegoing.com.au',
  type = 'website',
  noindex = false,
  children,
}: SEOProps) {
  // Ensure title has the brand name
  const fullTitle = title.includes('HouseGoing') ? title : `${title} | HouseGoing`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="HouseGoing" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />

      {/* Enhanced SEO Meta Tags */}
      <meta name="geo.region" content="AU-NSW" />
      <meta name="geo.placename" content="New South Wales, Australia" />
      <meta name="geo.position" content="-33.8688;151.2093" />
      <meta name="ICBM" content="-33.8688, 151.2093" />

      {/* Business Information */}
      <meta name="business:contact_data:locality" content="Sydney" />
      <meta name="business:contact_data:region" content="NSW" />
      <meta name="business:contact_data:country_name" content="Australia" />

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Alternate URLs for mobile */}
      <link rel="alternate" media="only screen and (max-width: 640px)" href={url} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={`${fullTitle} - Party venue rental platform`} />
      <meta property="og:site_name" content="HouseGoing" />
      <meta property="og:locale" content="en_AU" />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@housegoing" />
      <meta name="twitter:creator" content="@housegoing" />
      <meta name="twitter:url" content={url} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={`${fullTitle} - Party venue rental platform`} />

      {/* Additional Social Media */}
      <meta property="fb:app_id" content="your-facebook-app-id" />

      {/* Apple Touch Icons */}
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />

      {/* Theme Color */}
      <meta name="theme-color" content="#7c3aed" />
      <meta name="msapplication-TileColor" content="#7c3aed" />

      {/* Robots */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      )}

      {/* Additional SEO Tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />

      {/* Additional head elements */}
      {children}
    </Helmet>
  );
}
