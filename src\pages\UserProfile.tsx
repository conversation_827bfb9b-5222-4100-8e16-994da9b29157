import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { isHost } from '../utils/user-roles';
import { getUserBookings } from '../api/bookings';
import { Link, useNavigate } from 'react-router-dom';
import { Calendar, Clock, Users, DollarSign, ChevronRight, AlertCircle, LogOut, MessageCircle, Star, Heart, CreditCard, Settings, User, FileText, Menu, X, Shield } from 'lucide-react';
import { supabase } from '../lib/supabase-client';
import MessagesSection from '../components/account/MessagesSection';
import ReviewsSection from '../components/account/ReviewsSection';
import SavedVenuesSection from '../components/account/SavedVenuesSection';
import PaymentMethodsSection from '../components/account/PaymentMethodsSection';
import CustomerVerification from '../components/verification/CustomerVerification';

export default function UserProfile() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'profile' | 'bookings' | 'messages' | 'reviews' | 'saved' | 'payments' | 'security' | 'verification' | 'preferences'>('profile');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Check if we're in development mode
  const isDevelopmentMode = typeof window !== 'undefined' &&
                           (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('local'));

  // Development mode: use mock data
  let user, isAuthenticated, isLoading, signOut;

  if (isDevelopmentMode) {
    // Mock user data for development (load from localStorage if available)
    const firstName = localStorage.getItem('dev_user_firstName') || 'John';
    const lastName = localStorage.getItem('dev_user_lastName') || 'Developer';
    const bio = localStorage.getItem('dev_user_bio') || 'Development user for testing';
    const location = localStorage.getItem('dev_user_location') || 'Sydney, NSW';

    user = {
      id: 'dev-user-123',
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`,
      primaryEmailAddress: { emailAddress: '<EMAIL>' },
      imageUrl: 'https://via.placeholder.com/150',
      createdAt: new Date().toISOString(),
      publicMetadata: {
        bio,
        location
      }
    };
    isAuthenticated = true;
    isLoading = false;
    signOut = async () => {
      console.log('Development mode: Mock sign out');
      navigate('/');
    };
  } else {
    // Production mode: use real auth
    const { user: clerkUser, isLoaded, isSignedIn } = useUser();
    user = clerkUser;
    isAuthenticated = isSignedIn;
    isLoading = !isLoaded;
    signOut = () => {
      console.log('Sign out functionality needs to be implemented');
      navigate('/');
    };
  }

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };
  const [isEditing, setIsEditing] = useState(false);
  const [bookings, setBookings] = useState<any[]>([]);
  const [loadingBookings, setLoadingBookings] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    location: '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Use the user directly (mock in development, real in production)
  const effectiveUser = user;

  // Initialize form data when user data is loaded
  useEffect(() => {
    if (effectiveUser) {
      // Load user profile from Supabase first, then fallback to Clerk/localStorage
      loadUserProfile();

      // Fetch user bookings if on bookings tab
      if (activeTab === 'bookings') {
        fetchUserBookings();
      }
    }
  }, [effectiveUser, activeTab]);

  // Load user profile from Supabase
  const loadUserProfile = async () => {
    if (!effectiveUser) return;

    try {
      const userId = effectiveUser.id || localStorage.getItem('clerk_user_id');
      if (!userId) {
        console.log('No user ID found, using fallback data');
        setFormData({
          firstName: effectiveUser.firstName || localStorage.getItem('first_name') || '',
          lastName: effectiveUser.lastName || localStorage.getItem('last_name') || '',
          bio: effectiveUser.publicMetadata?.bio as string || '',
          location: effectiveUser.publicMetadata?.location as string || '',
        });
        return;
      }

      // Try to load from Supabase first - select all possible name columns
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('first_name, last_name, full_name, bio, phone, email, role, is_host')
        .eq('clerk_id', userId)
        .maybeSingle();

      if (!error && profile) {
        console.log('Loaded profile from Supabase:', profile);

        // Handle both schema formats
        let firstName = profile.first_name || '';
        let lastName = profile.last_name || '';

        // If we don't have first_name/last_name but have full_name, try to split it
        if (!firstName && !lastName && profile.full_name) {
          const nameParts = profile.full_name.split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.slice(1).join(' ') || '';
        }

        // Use Supabase data
        setFormData({
          firstName: firstName,
          lastName: lastName,
          bio: profile.bio || '',
          location: profile.phone || '', // Using phone field for location temporarily
        });
      } else {
        console.log('No Supabase profile found, using fallback data:', error);
        // Fallback to Clerk/localStorage data
        setFormData({
          firstName: effectiveUser.firstName || localStorage.getItem('first_name') || '',
          lastName: effectiveUser.lastName || localStorage.getItem('last_name') || '',
          bio: effectiveUser.publicMetadata?.bio as string || '',
          location: effectiveUser.publicMetadata?.location as string || '',
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Fallback to Clerk/localStorage data
      try {
        setFormData({
          firstName: effectiveUser?.firstName || localStorage.getItem('first_name') || '',
          lastName: effectiveUser?.lastName || localStorage.getItem('last_name') || '',
          bio: (effectiveUser?.publicMetadata?.bio as string) || '',
          location: (effectiveUser?.publicMetadata?.location as string) || '',
        });
      } catch (fallbackError) {
        console.error('Error in fallback data loading:', fallbackError);
        // Final fallback with empty data
        setFormData({
          firstName: '',
          lastName: '',
          bio: '',
          location: '',
        });
      }
    }
  };

  // Fetch user bookings
  const fetchUserBookings = async () => {
    if (!effectiveUser) return;

    try {
      setLoadingBookings(true);
      setBookingsError(null);

      const userId = effectiveUser.id || localStorage.getItem('clerk_user_id');
      const userEmail = effectiveUser.primaryEmailAddress?.emailAddress ||
                       localStorage.getItem('clerk_user_email') ||
                       localStorage.getItem('email');

      if (!userId) {
        throw new Error('User ID not found');
      }

      console.log('Fetching bookings for user:', userId, 'with email:', userEmail);
      const data = await getUserBookings(userId, userEmail);
      console.log('Bookings data:', data);
      setBookings(data);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setBookingsError('Failed to load your bookings');
    } finally {
      setLoadingBookings(false);
    }
  };

  // Show loading state (only in production mode)
  if (!isDevelopmentMode && isLoading) {
    return <div className="pt-32 flex justify-center">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
        <p className="text-gray-600">Loading your profile...</p>
      </div>
    </div>;
  }

  // If no user in production mode, show sign in message
  if (!isDevelopmentMode && !effectiveUser) {
    return <div className="pt-32 flex justify-center">
      <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
        <h2 className="text-2xl font-bold mb-4">Please Sign In</h2>
        <p className="text-gray-600 mb-6">You need to be signed in to view your profile.</p>
        <a href="/login" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
          Sign In
        </a>
      </div>
    </div>;
  }

  // Determine if user is a host
  const userIsHost = effectiveUser ?
    (isHost(effectiveUser) || localStorage.getItem('user_role') === 'host') :
    false;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage(null);

    try {
      if (!effectiveUser) {
        throw new Error('No user found');
      }

      // Get the user ID
      const userId = effectiveUser.id || localStorage.getItem('clerk_user_id');

      if (!userId) {
        throw new Error('User ID not found');
      }

      // First, try to update in Supabase
      try {

        // Check if user profile exists using clerk_id
        const { data: existingProfile, error: checkError } = await supabase
          .from('user_profiles')
          .select('id, clerk_id')
          .eq('clerk_id', userId)
          .maybeSingle();

        console.log('Existing profile check:', { existingProfile, checkError });

        let profileUpdateResult;
        const fullName = `${formData.firstName || ''} ${formData.lastName || ''}`.trim();

        if (existingProfile) {
          // Update existing profile with first_name and last_name
          profileUpdateResult = await supabase
            .from('user_profiles')
            .update({
              first_name: formData.firstName,
              last_name: formData.lastName,
              bio: formData.bio,
              phone: formData.location, // Store location in phone field temporarily
              updated_at: new Date().toISOString()
            })
            .eq('clerk_id', userId);
        } else {
          // Create new profile
          profileUpdateResult = await supabase
            .from('user_profiles')
            .insert({
              clerk_id: userId,
              email: effectiveUser.primaryEmailAddress?.emailAddress || localStorage.getItem('clerk_user_email'),
              first_name: formData.firstName,
              last_name: formData.lastName,
              bio: formData.bio,
              phone: formData.location, // Store location in phone field temporarily
              role: localStorage.getItem('user_role') || 'guest',
              is_host: localStorage.getItem('user_role') === 'host',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
        }

        console.log('Profile update result:', profileUpdateResult);

        if (profileUpdateResult.error) {
          throw profileUpdateResult.error;
        }
      } catch (supabaseError) {
        console.error('Error updating profile in Supabase:', supabaseError);
        // Continue with Clerk update even if Supabase fails
      }

      // Check if we have a real user with update capabilities
      if (user && typeof user.update === 'function') {
        // Update user data - only update what's allowed
        try {
          // Update firstName if it's different
          if (user.firstName !== formData.firstName) {
            await user.update({
              firstName: formData.firstName,
            });
            console.log('User firstName updated successfully');
          }

          // Update lastName if it's different
          if (user.lastName !== formData.lastName) {
            await user.update({
              lastName: formData.lastName,
            });
            console.log('User lastName updated successfully');
          }

          // Update public metadata if available
          if (user.publicMetadata !== undefined) {
            await user.update({
              publicMetadata: {
                ...(user.publicMetadata || {}),
                bio: formData.bio,
                location: formData.location,
                first_name: formData.firstName, // Store in metadata as backup
                last_name: formData.lastName,   // Store in metadata as backup
              },
            });
            console.log('User metadata updated successfully');
          }
        } catch (userError) {
          console.error('Error updating user:', userError);
          // Continue even if user update fails
        }
      }

      // Always update localStorage for consistency
      localStorage.setItem('first_name', formData.firstName);
      localStorage.setItem('last_name', formData.lastName);
      localStorage.setItem('user_full_name', `${formData.firstName || ''} ${formData.lastName || ''}`.trim());
      localStorage.setItem('user_display_name', formData.firstName || 'User');

      if (isDevelopmentMode) {
        // In development mode, just update localStorage for persistence
        localStorage.setItem('dev_user_firstName', formData.firstName);
        localStorage.setItem('dev_user_lastName', formData.lastName);
        localStorage.setItem('dev_user_bio', formData.bio);
        localStorage.setItem('dev_user_location', formData.location);
        console.log('Development mode: Profile updated in localStorage');
      }

      setMessage({ type: 'success', text: 'Profile updated successfully!' });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="pt-32 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Profile Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0">
                <img
                  src={effectiveUser?.imageUrl || 'https://via.placeholder.com/150'}
                  alt={effectiveUser?.fullName || 'User'}
                  className="w-20 h-20 rounded-full border-4 border-gray-100"
                />
              </div>
              <div className="flex-grow">
                <h1 className="text-2xl font-semibold text-gray-900 mb-2 font-inter">
                  {formData.firstName && formData.lastName
                    ? `${formData.firstName} ${formData.lastName}`
                    : effectiveUser?.fullName || effectiveUser?.username || 'User'}
                </h1>
                <p className="text-base text-gray-600 mb-3 font-inter">
                  {effectiveUser?.primaryEmailAddress?.emailAddress || 'No email'}
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium font-inter">
                    Member since {new Date(effectiveUser?.createdAt || Date.now()).toLocaleDateString()}
                  </span>
                  {userIsHost && (
                    <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium font-inter">
                      Host
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden mb-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm font-inter font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Menu className="w-5 h-5" />
              Menu
            </button>
          </div>

          {/* Main Layout with Sidebar */}
          <div className="flex gap-8">
            {/* Desktop Sidebar Navigation */}
            <div className="hidden lg:block w-64 flex-shrink-0">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sticky top-8">
                <nav className="space-y-1">
                  {[
                    { key: 'profile', label: 'Personal Info', icon: User },
                    { key: 'bookings', label: 'My Bookings', icon: Calendar },
                    { key: 'messages', label: 'Messages', icon: MessageCircle },
                    { key: 'reviews', label: 'Reviews', icon: Star },
                    { key: 'saved', label: 'Saved Venues', icon: Heart },
                    { key: 'payments', label: 'Payment Methods', icon: CreditCard },
                    { key: 'verification', label: 'Verification', icon: Shield },
                    { key: 'security', label: 'Security', icon: Settings },
                    { key: 'preferences', label: 'Preferences', icon: FileText }
                  ].map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.key}
                        className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors font-inter ${
                          activeTab === item.key
                            ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600 font-medium'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 font-medium'
                        }`}
                        onClick={() => setActiveTab(item.key as any)}
                      >
                        <Icon className="w-5 h-5" />
                        <span className="text-sm">{item.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>

            {/* Mobile Sidebar Overlay */}
            {sidebarOpen && (
              <div className="lg:hidden fixed inset-0 z-50 flex">
                <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
                <div className="relative flex flex-col w-64 bg-white shadow-xl">
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900 font-inter">Menu</h2>
                    <button
                      onClick={() => setSidebarOpen(false)}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                  <nav className="flex-1 p-4 space-y-1">
                    {[
                      { key: 'profile', label: 'Personal Info', icon: User },
                      { key: 'bookings', label: 'My Bookings', icon: Calendar },
                      { key: 'messages', label: 'Messages', icon: MessageCircle },
                      { key: 'reviews', label: 'Reviews', icon: Star },
                      { key: 'saved', label: 'Saved Venues', icon: Heart },
                      { key: 'payments', label: 'Payment Methods', icon: CreditCard },
                      { key: 'verification', label: 'Verification', icon: Shield },
                      { key: 'security', label: 'Security', icon: Settings },
                      { key: 'preferences', label: 'Preferences', icon: FileText }
                    ].map((item) => {
                      const Icon = item.icon;
                      return (
                        <button
                          key={item.key}
                          className={`w-full flex items-center gap-3 px-3 py-3 text-left rounded-md transition-colors font-inter ${
                            activeTab === item.key
                              ? 'bg-purple-50 text-purple-700 font-medium'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 font-medium'
                          }`}
                          onClick={() => {
                            setActiveTab(item.key as any);
                            setSidebarOpen(false);
                          }}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="text-sm">{item.label}</span>
                        </button>
                      );
                    })}
                  </nav>
                </div>
              </div>
            )}

            {/* Main Content Area */}
            <div className="flex-1">
              {/* Message */}
              {message && (
                <div
                  className={`mb-6 p-4 rounded-lg border ${
                    message.type === 'success'
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-red-50 text-red-700 border-red-200'
                  }`}
                >
                  {message.text}
                </div>
              )}

              {/* Tab content */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            {activeTab === 'profile' && (
              <div className="p-8">
                <div className="flex justify-between items-start mb-8">
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 font-inter">Profile Information</h2>
                    <p className="text-gray-600 mt-2 font-inter">Manage your personal information</p>
                  </div>
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors font-inter"
                    >
                      Edit Profile
                    </button>
                  ) : (
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors font-inter"
                    >
                      Cancel
                    </button>
                  )}
                </div>

                {isEditing ? (
                  <form onSubmit={handleSaveProfile} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                          First Name
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-inter"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                          Last Name
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-inter"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={effectiveUser?.primaryEmailAddress?.emailAddress || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed font-inter"
                        disabled
                      />
                      <p className="text-sm text-gray-500 mt-2 font-inter">
                        To change your email, go to the Security tab.
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                        Location
                      </label>
                      <input
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-inter"
                        placeholder="City, Country"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                        Bio
                      </label>
                      <textarea
                        name="bio"
                        value={formData.bio}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-inter"
                        placeholder="Tell us a bit about yourself..."
                      ></textarea>
                    </div>

                    <div className="pt-6">
                      <button
                        type="submit"
                        disabled={isSaving}
                        className={`px-6 py-3 rounded-lg text-white font-medium font-inter ${
                          isSaving
                            ? 'bg-purple-400 cursor-not-allowed'
                            : 'bg-purple-600 hover:bg-purple-700'
                        }`}
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">First Name</h3>
                        <p className="text-gray-900 font-medium font-inter">{formData.firstName || effectiveUser?.firstName || 'Not provided'}</p>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Last Name</h3>
                        <p className="text-gray-900 font-medium font-inter">{formData.lastName || effectiveUser?.lastName || 'Not provided'}</p>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Email Address</h3>
                        <p className="text-gray-900 font-medium font-inter">{effectiveUser?.primaryEmailAddress?.emailAddress || 'No email provided'}</p>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Location</h3>
                        <p className="text-gray-900 font-medium font-inter">
                          {effectiveUser?.publicMetadata?.location || 'Not provided'}
                        </p>
                      </div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Bio</h3>
                      <p className="text-gray-900 font-inter leading-relaxed">
                        {effectiveUser?.publicMetadata?.bio || 'No bio provided'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'messages' && (
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 font-inter">Messages</h2>
                  <p className="text-gray-600 mt-2 font-inter">Communicate with venue hosts</p>
                </div>
                <MessagesSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 font-inter">Reviews</h2>
                  <p className="text-gray-600 mt-2 font-inter">Your reviews and ratings</p>
                </div>
                <ReviewsSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
              </div>
            )}

            {activeTab === 'saved' && (
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 font-inter">Saved Venues</h2>
                  <p className="text-gray-600 mt-2 font-inter">Your favorite venues</p>
                </div>
                <SavedVenuesSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
              </div>
            )}

            {activeTab === 'payments' && (
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 font-inter">Payment Methods</h2>
                  <p className="text-gray-600 mt-2 font-inter">Manage your payment options</p>
                </div>
                <PaymentMethodsSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
              </div>
            )}

        {activeTab === 'security' && (
          <div className="p-8">
            <div className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2 font-inter">Security Settings</h2>
              <p className="text-gray-600 font-inter">Manage your account security and privacy settings</p>
            </div>

            <div className="space-y-10">
              {/* Change Password Section */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Settings className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">Change Password</h3>
                    <p className="text-gray-600 mb-6 font-inter">
                      Update your password to keep your account secure.
                    </p>
                    <button className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors font-inter">
                      Change Password
                    </button>
                  </div>
                </div>
              </div>

              {/* Email Addresses Section */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">Email Addresses</h3>
                  <p className="text-gray-600 font-inter">
                    Manage your email addresses.
                  </p>
                </div>
                <div className="space-y-4">
                  {effectiveUser?.emailAddresses ? (
                    effectiveUser.emailAddresses.map((email) => (
                      <div
                        key={email.id}
                        className="flex justify-between items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm"
                      >
                        <div>
                          <p className="font-medium text-gray-900 font-inter">{email.emailAddress}</p>
                          <p className="text-sm text-gray-500 mt-1 font-inter">
                            {email.id === effectiveUser.primaryEmailAddressId ? 'Primary' : 'Secondary'}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          {!email.verified && (
                            <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                              Unverified
                            </span>
                          )}
                          {email.verified && (
                            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                              Verified
                            </span>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex justify-between items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                      <div>
                        <p className="font-medium text-gray-900 font-inter">{effectiveUser?.primaryEmailAddress?.emailAddress || 'No email'}</p>
                        <p className="text-sm text-gray-500 mt-1 font-inter">Primary</p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                          Verified
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Connected Accounts Section */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">Connected Accounts</h3>
                  <p className="text-gray-600 font-inter">
                    Manage third-party services connected to your account.
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <span className="text-red-600 font-bold text-sm">G</span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 font-inter">Google</p>
                        <p className="text-sm text-gray-500 font-inter">
                          {effectiveUser?.externalAccounts?.some((account) => account.provider === 'google')
                            ? 'Connected'
                            : 'Not connected'}
                        </p>
                      </div>
                    </div>
                    <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg text-sm font-medium transition-colors font-inter">
                      {effectiveUser?.externalAccounts?.some((account) => account.provider === 'google')
                        ? 'Disconnect'
                        : 'Connect'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'bookings' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">My Bookings</h2>
              <Link to="/my-bookings" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
                View All Bookings
              </Link>
            </div>

            {loadingBookings ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
              </div>
            ) : bookingsError ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                {bookingsError}
              </div>
            ) : bookings.length === 0 ? (
              <div className="text-center py-8">
                <h3 className="text-lg font-medium mb-2">No Bookings Yet</h3>
                <p className="text-gray-600 mb-4">You haven't made any bookings yet.</p>
                <Link to="/" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
                  Explore Venues
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Upcoming Bookings</h3>
                  <div className="space-y-4">
                    {bookings
                      .filter(b => b.status !== 'cancelled' && new Date(b.start_date) > new Date())
                      .slice(0, 3)
                      .map(booking => (
                        <div key={booking.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex flex-col md:flex-row justify-between">
                            <div>
                              <h4 className="font-semibold text-lg">{booking.venue?.title || 'Venue'}</h4>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Calendar className="w-4 h-4 mr-1" />
                                <span className="text-sm">
                                  {new Date(booking.start_date).toLocaleDateString()} at {new Date(booking.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Clock className="w-4 h-4 mr-1" />
                                <span className="text-sm">
                                  {new Date(booking.end_date).toLocaleDateString()} at {new Date(booking.end_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Users className="w-4 h-4 mr-1" />
                                <span className="text-sm">{booking.guests_count} guests</span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <DollarSign className="w-4 h-4 mr-1" />
                                <span className="text-sm">${booking.total_price}</span>
                              </div>
                            </div>
                            <div className="mt-4 md:mt-0 flex flex-col items-start md:items-end">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </span>
                              <Link to={`/booking-confirmation/${booking.id}`} className="mt-2 flex items-center text-purple-600 hover:text-purple-800">
                                View Details <ChevronRight className="w-4 h-4 ml-1" />
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    {bookings.filter(b => b.status !== 'cancelled' && new Date(b.start_date) > new Date()).length === 0 && (
                      <p className="text-gray-600">No upcoming bookings</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Past Bookings</h3>
                  <div className="space-y-4">
                    {bookings
                      .filter(b => b.status !== 'cancelled' && new Date(b.start_date) <= new Date())
                      .slice(0, 3)
                      .map(booking => (
                        <div key={booking.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex flex-col md:flex-row justify-between">
                            <div>
                              <h4 className="font-semibold text-lg">{booking.venue?.title || 'Venue'}</h4>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Calendar className="w-4 h-4 mr-1" />
                                <span className="text-sm">
                                  {new Date(booking.start_date).toLocaleDateString()} at {new Date(booking.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Clock className="w-4 h-4 mr-1" />
                                <span className="text-sm">
                                  {new Date(booking.end_date).toLocaleDateString()} at {new Date(booking.end_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <Users className="w-4 h-4 mr-1" />
                                <span className="text-sm">{booking.guests_count} guests</span>
                              </div>
                              <div className="flex items-center text-gray-600 mt-1">
                                <DollarSign className="w-4 h-4 mr-1" />
                                <span className="text-sm">${booking.total_price}</span>
                              </div>
                            </div>
                            <div className="mt-4 md:mt-0 flex flex-col items-start md:items-end">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                booking.status === 'completed' ? 'bg-green-100 text-green-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </span>
                              <Link to={`/booking-confirmation/${booking.id}`} className="mt-2 flex items-center text-purple-600 hover:text-purple-800">
                                View Details <ChevronRight className="w-4 h-4 ml-1" />
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    {bookings.filter(b => b.status !== 'cancelled' && new Date(b.start_date) <= new Date()).length === 0 && (
                      <p className="text-gray-600">No past bookings</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'verification' && (
          <div className="p-8">
            <div className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 font-inter">Identity Verification</h2>
              <p className="text-gray-600 mt-2 font-inter">Verify your identity to increase trust and security</p>
            </div>
            <CustomerVerification
              onComplete={(verificationData) => {
                console.log('Verification completed:', verificationData);
                // Handle verification completion
                // You can save the verification data to Supabase here
              }}
              onSkip={() => {
                console.log('Verification skipped');
                // Handle verification skip
              }}
              isRequired={false}
            />
          </div>
        )}

        {activeTab === 'preferences' && (
          <div>
            <h2 className="text-xl font-semibold mb-6">Preferences</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Notifications</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="email-notifications"
                      className="mt-1 mr-2"
                      defaultChecked
                    />
                    <div>
                      <label htmlFor="email-notifications" className="font-medium text-gray-700">
                        Email Notifications
                      </label>
                      <p className="text-sm text-gray-500">
                        Receive emails about your account activity, bookings, and promotions.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="marketing-emails"
                      className="mt-1 mr-2"
                    />
                    <div>
                      <label htmlFor="marketing-emails" className="font-medium text-gray-700">
                        Marketing Emails
                      </label>
                      <p className="text-sm text-gray-500">
                        Receive promotional emails about special offers and new features.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-2">Language</h3>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                </select>
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-2">Time Zone</h3>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="utc">UTC (Coordinated Universal Time)</option>
                  <option value="est">EST (Eastern Standard Time)</option>
                  <option value="pst">PST (Pacific Standard Time)</option>
                  <option value="aest">AEST (Australian Eastern Standard Time)</option>
                </select>
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-2">Currency</h3>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="usd">USD ($)</option>
                  <option value="eur">EUR (€)</option>
                  <option value="gbp">GBP (£)</option>
                  <option value="aud">AUD ($)</option>
                </select>
              </div>
            </div>
          </div>
                )}
              </div>

              {/* Host section */}
              {userIsHost && (
                <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-semibold mb-4">Host Dashboard</h2>
                  <p className="text-gray-600 mb-4">
                    You are registered as a host. You can manage your venues and bookings from the host dashboard.
                  </p>
                  <a
                    href="/host/onboarding"
                    className="inline-block px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm font-medium transition-colors"
                  >
                    Go to Host Dashboard
                  </a>
                </div>
              )}

              {/* Become a host section */}
              {!userIsHost && (
                <div className="mt-8 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl shadow-sm border border-purple-100 p-8">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <span className="text-purple-600 text-xl">🏠</span>
                    </div>
                    <div className="flex-grow">
                      <h2 className="text-2xl font-semibold text-gray-900 mb-3 font-inter">Become a Host</h2>
                      <p className="text-gray-600 mb-6 font-inter leading-relaxed">
                        Want to list your venue on HouseGoing? Become a host and start earning today.
                      </p>
                      <a
                        href="/host/signup"
                        className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors font-inter"
                      >
                        <span>Become a Property Owner</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {/* Sign Out button */}
              <div className="mt-6 text-center">
                <button
                  onClick={handleSignOut}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm font-medium transition-colors"
                >
                  <LogOut className="w-4 h-4" />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
