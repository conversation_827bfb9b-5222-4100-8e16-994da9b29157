import React from 'react';
import { Star, MapPin, Users, Heart, Shield, Zap, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Venue } from '../../types/venue';
import { useFavorites } from '../../hooks/useFavorites';

interface VenueCardProps {
  venue: Venue & {
    hoursAvailable?: number;
    distanceKm?: number;
    radiusCategory?: 'exact' | 'nearby' | 'extended' | 'distant';
  };
}

export default function VenueCard({ venue }: VenueCardProps) {
  const { favorites, toggleFavorite } = useFavorites();
  const isFavorite = favorites.includes(venue.id);

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    toggleFavorite(venue.id);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  // Get venue type from event types or use a default
  const getVenueType = () => {
    if (!venue.eventTypes || !Array.isArray(venue.eventTypes)) return 'Event Space';
    if (venue.eventTypes.includes('Wedding')) return 'Wedding Venue';
    if (venue.eventTypes.includes('Corporate')) return 'Function Centre';
    if (venue.eventTypes.includes('Birthday')) return 'Party Venue';
    return venue.eventTypes[0] || 'Event Space';
  };

  // Get emoji for venue type
  const getVenueEmoji = () => {
    const venueType = getVenueType();
    if (venueType.includes('Wedding')) return '💒';
    if (venueType.includes('Function')) return '🏢';
    if (venueType.includes('Party')) return '🎉';
    if (venueType.includes('Beach')) return '🏖️';
    if (venueType.includes('Rooftop')) return '🏙️';
    if (venueType.includes('Garden')) return '🌿';
    return '🎪';
  };

  // Get gradient colors based on venue type
  const getGradientClass = () => {
    const venueType = getVenueType();
    if (venueType.includes('Wedding')) return 'bg-gradient-to-br from-pink-100 to-rose-300';
    if (venueType.includes('Function')) return 'bg-gradient-to-br from-indigo-100 to-purple-300';
    if (venueType.includes('Party')) return 'bg-gradient-to-br from-purple-100 to-purple-300';
    if (venueType.includes('Beach')) return 'bg-gradient-to-br from-blue-100 to-cyan-300';
    if (venueType.includes('Rooftop')) return 'bg-gradient-to-br from-purple-100 to-purple-300';
    if (venueType.includes('Garden')) return 'bg-gradient-to-br from-green-100 to-emerald-300';
    return 'bg-gradient-to-br from-purple-100 to-purple-300';
  };

  return (
    <Link to={`/venue/${venue.id}`} className="group">
      <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 border border-gray-100">
        {/* Venue Image */}
        <div className={`relative h-64 overflow-hidden ${getGradientClass()}`}>
          <div className="absolute inset-0 bg-gradient-to-br from-black/10 to-black/30"></div>

          {/* Use actual image if available, otherwise show placeholder */}
          {venue.images && venue.images.length > 0 ? (
            <img
              src={venue.images[0]}
              alt={venue.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="text-4xl mb-2">{getVenueEmoji()}</div>
                <div className="text-sm font-medium opacity-90">{getVenueType()}</div>
              </div>
            </div>
          )}

          {/* Top badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {venue.isExactMatch && (
              <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                🎯 Exact Match
              </span>
            )}
            {venue.isSuggestion && (
              <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                📍 Nearby
              </span>
            )}
            {venue.host?.verified && (
              <span className="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <Shield className="h-3 w-3" />
                Verified
              </span>
            )}
            {venue.instantBook && (
              <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <Zap className="h-3 w-3" />
                Instant Book
              </span>
            )}
          </div>

          {/* Party Score */}
          {venue.partyScore && (
            <div className="absolute top-3 right-3">
              <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 text-xs font-semibold text-purple-600">
                🎉 {venue.partyScore.score.toFixed(1)}
              </div>
            </div>
          )}

          {/* Price overlay */}
          <div className="absolute bottom-3 right-3">
            <div className="bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 text-right">
              <div className="text-lg font-bold text-gray-900">
                {formatPrice(venue.price)}<span className="text-sm font-normal">/hr</span>
              </div>
              {venue.hoursAvailable && (
                <div className="text-xs text-green-600">
                  Up to {venue.hoursAvailable}hrs
                </div>
              )}
            </div>
          </div>

          {/* Favorite button */}
          <button
            onClick={handleFavorite}
            className="absolute top-3 right-14 p-2 rounded-full bg-white/90 hover:bg-white transition-colors shadow-md"
          >
            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
          </button>
        </div>

        {/* Venue Content */}
        <div className="p-5">
          {/* Location & Title */}
          <div className="mb-3">
            <div className="flex items-center justify-between text-gray-500 text-sm mb-1">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {venue.location}
              </div>
              {venue.distanceKm !== undefined && (
                <div className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                  {venue.distanceKm < 1
                    ? `${Math.round(venue.distanceKm * 1000)}m away`
                    : `${venue.distanceKm.toFixed(1)}km away`
                  }
                </div>
              )}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
              {venue.title}
            </h3>
          </div>

          {/* Capacity */}
          <div className="flex items-center text-gray-600 text-sm mb-3">
            <Users className="h-4 w-4 mr-1" />
            Up to {venue.capacity} guests
          </div>

          {/* Key Features */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {venue.amenities && Array.isArray(venue.amenities) && venue.amenities.slice(0, 3).map((amenity, index) => (
                <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                  {amenity}
                </span>
              ))}
              {venue.amenities && venue.amenities.length > 3 && (
                <span className="text-gray-500 text-xs px-2 py-1">
                  +{venue.amenities.length - 3} more
                </span>
              )}
              {(!venue.amenities || venue.amenities.length === 0) && (
                <span className="text-gray-500 text-xs px-2 py-1">
                  No amenities listed
                </span>
              )}
            </div>
          </div>

          {/* Rating & Response */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <span className="text-sm font-medium text-gray-900">{formatRating(venue.rating)}</span>
              <span className="text-sm text-gray-500">({venue.reviews})</span>
            </div>
            <div className="flex items-center text-gray-500 text-sm">
              <Clock className="h-4 w-4 mr-1" />
              Quick response
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Link
              to={`/venue/${venue.id}`}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors block text-center"
            >
              View Details
            </Link>
            {venue.instantBook && (
              <Link
                to={`/enhanced-booking/${venue.id}`}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors block text-center text-sm"
              >
                Quick Book
              </Link>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}