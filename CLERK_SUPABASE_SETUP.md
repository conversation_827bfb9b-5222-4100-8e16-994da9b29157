# Clerk-Supabase Native Integration Setup Guide

## Overview

This guide shows how to set up the **native Clerk-Supabase integration** (no JWT template needed) for HouseGoing. This is the recommended approach as of April 2025.

## ✅ What We've Implemented

### 1. **Native Integration Pattern**
- Uses `accessToken()` function in Supabase client
- No JWT template configuration needed in Clerk
- Automatic token injection for all Supabase requests

### 2. **Updated Code Structure**
```typescript
// src/lib/supabase-client.ts
export function createClerkSupabaseClient(getToken: () => Promise<string | null>) {
  return createClient(supabaseUrl, supabaseKey, {
    global: {
      async accessToken() {
        return await getToken(); // No template parameter needed
      },
    },
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
  });
}

// src/providers/AuthProvider.tsx
const clerkSupabase = createClerkSupabaseClient(async () => {
  try {
    return await getToken(); // Native integration - no template
  } catch (error) {
    console.warn('Could not get Clerk token:', error);
    return null;
  }
});
```

## 🔧 Required Configuration

### 1. **Clerk Dashboard Setup**

1. **Navigate to Supabase Integration**:
   - Go to Clerk Dashboard → Integrations → Supabase
   - Click "Activate Supabase integration"
   - Copy the Clerk domain (e.g., `https://clerk.housegoing.com.au`)

2. **No JWT Template Needed**:
   - ❌ Do NOT create a JWT template
   - ✅ Use the native integration instead

### 2. **Supabase Dashboard Setup**

1. **Add Clerk as Third-Party Auth Provider**:
   - Go to Supabase Dashboard → Authentication → Sign In / Up
   - Click "Add provider"
   - Select "Clerk" from the list
   - Paste your Clerk domain: `https://clerk.housegoing.com.au`

2. **Configure Site URL**:
   - Set Site URL to: `https://housegoing.com.au`
   - Add redirect URLs: `https://housegoing.com.au/auth/callback`

### 3. **Environment Variables**

```env
# Clerk
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuaG91c2Vnb2luZy5jb20uYXUk

# Supabase
VITE_SUPABASE_URL=https://fxqoowlruissctsgbljk.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🛡️ Row Level Security (RLS) Setup

### 1. **Update user_profiles Table**

Add a `user_id` column that defaults to the Clerk user ID:

```sql
-- Add user_id column if it doesn't exist
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS user_id TEXT DEFAULT auth.jwt()->>'sub';

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
```

### 2. **Create RLS Policies**

```sql
-- Policy: Users can view their own profile
CREATE POLICY "Users can view their own profile"
ON "public"."user_profiles"
FOR SELECT
TO authenticated
USING (
  ((SELECT auth.jwt()->>'sub') = (user_id)::text)
);

-- Policy: Users can update their own profile
CREATE POLICY "Users can update their own profile"
ON "public"."user_profiles"
FOR UPDATE
TO authenticated
USING (
  ((SELECT auth.jwt()->>'sub') = (user_id)::text)
);

-- Policy: Users can insert their own profile
CREATE POLICY "Users can insert their own profile"
ON "public"."user_profiles"
FOR INSERT
TO authenticated
WITH CHECK (
  ((SELECT auth.jwt()->>'sub') = (user_id)::text)
);
```

### 3. **Apply to Other Tables**

For any table that should be user-specific:

```sql
-- Example: venues table
ALTER TABLE venues 
ADD COLUMN IF NOT EXISTS user_id TEXT DEFAULT auth.jwt()->>'sub';

ALTER TABLE venues ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own venues"
ON "public"."venues"
FOR SELECT
TO authenticated
USING (
  ((SELECT auth.jwt()->>'sub') = (user_id)::text)
);
```

## 🧪 Testing

### 1. **Local Testing (Limited)**
- Visit: `http://localhost:5175/test-clerk-auth`
- Sign in with Clerk
- Run integration tests
- **Note**: Full functionality requires production deployment

### 2. **Production Testing**
- Deploy to `housegoing.com.au`
- Test Google OAuth flow
- Test email/password authentication
- Verify user data sync to Supabase
- Test RLS policies

## 🔍 Key Benefits of Native Integration

1. **No JWT Template**: Simpler setup, no template configuration
2. **Automatic Token Refresh**: Clerk handles token lifecycle
3. **Better Security**: No need to share JWT secrets
4. **Easier Maintenance**: Less configuration to manage

## 🚨 Important Notes

1. **Production Only**: Clerk authentication only works on `housegoing.com.au`
2. **Domain Restrictions**: Clerk publishable key is domain-restricted
3. **RLS Required**: Use RLS policies for data security
4. **User Sync**: Consider webhooks for additional user data sync

## 📋 Verification Checklist

- [ ] Clerk Supabase integration activated in Clerk Dashboard
- [ ] Clerk added as third-party auth provider in Supabase
- [ ] Environment variables configured
- [ ] RLS policies created for user_profiles table
- [ ] Code updated to use native integration pattern
- [ ] Testing completed in production environment

## 🔗 References

- [Clerk Supabase Integration Docs](https://clerk.com/docs/integrations/databases/supabase)
- [Supabase Third-Party Auth Docs](https://supabase.com/docs/guides/auth/third-party-auth)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
