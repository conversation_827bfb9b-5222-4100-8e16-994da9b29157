# Database Setup Guide for My Account Features

This guide will help you set up the database tables and functions needed for the new My Account features.

## Prerequisites

- Access to your Supabase dashboard
- SQL Editor access in Supabase

## Setup Instructions

### 1. Run the Database Schema

1. Open your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `src/database/schema.sql`
4. Execute the SQL script

This will create the following tables:
- `messages` - For user-to-user communication
- `reviews` - For guest and host reviews
- `saved_venues` - For user wishlists
- `payment_methods` - For user payment information
- `notifications` - For user notifications
- `user_preferences` - For user settings

### 2. Verify Table Creation

After running the schema, verify that all tables were created:

```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('messages', 'reviews', 'saved_venues', 'payment_methods', 'notifications', 'user_preferences');
```

### 3. Test the Functions

Test the helper functions:

```sql
-- Test notification creation
SELECT create_notification(
  'test_user_id',
  'test',
  'Test Notification',
  'This is a test notification'
);

-- Test unread message count
SELECT get_unread_message_count('test_user_id');

-- Test user average rating
SELECT get_user_average_rating('test_user_id');
```

### 4. Set Up Row Level Security (RLS)

The schema automatically enables RLS and creates policies. Verify they're active:

```sql
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('messages', 'reviews', 'saved_venues', 'payment_methods', 'notifications', 'user_preferences');
```

### 5. Test Data (Optional)

You can insert some test data to verify everything works:

```sql
-- Insert test message
INSERT INTO messages (sender_id, receiver_id, content) 
VALUES ('user1', 'user2', 'Hello, is your venue available this weekend?');

-- Insert test review
INSERT INTO reviews (booking_id, reviewer_id, reviewee_id, rating, comment, type) 
VALUES (gen_random_uuid(), 'user1', 'user2', 5, 'Great venue and host!', 'guest_to_host');

-- Insert test saved venue
INSERT INTO saved_venues (user_id, venue_id) 
VALUES ('user1', gen_random_uuid());
```

## Environment Variables

Make sure your `.env` file has the correct Supabase configuration:

```env
REACT_APP_SUPABASE_URL=https://fxqoowlruissctsgbljk.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk
```

## Features Overview

### 1. Messages System
- Real-time messaging between users and hosts
- Read receipts and message status
- Conversation threading
- Mobile-responsive chat interface

### 2. Reviews System
- Guest-to-host and host-to-guest reviews
- 5-star rating system
- Review filtering and management
- Average rating calculation

### 3. Saved Venues
- Wishlist functionality
- Venue cards with images and details
- Easy booking from saved venues
- Remove from saved functionality

### 4. Payment Methods
- Secure payment method storage
- Default payment method selection
- Stripe integration ready
- Card management interface

### 5. Notifications
- System notifications
- Email notification preferences
- Real-time updates

### 6. User Preferences
- Email notification settings
- Language and timezone preferences
- Marketing email opt-in/out

## Security Features

- Row Level Security (RLS) enabled on all tables
- User-specific data access policies
- Secure JWT token validation
- Encrypted sensitive data

## Mobile Optimization

- Responsive sidebar navigation
- Touch-friendly interfaces
- Mobile-first design approach
- Optimized for all screen sizes

## Typography & Design

- Inter font family for better readability
- Consistent color scheme matching your brand
- Enhanced button reactivity
- Smooth animations and transitions
- Accessibility features included

## Next Steps

1. Run the database schema
2. Test the My Account page functionality
3. Configure Stripe for payment methods (optional)
4. Set up email notifications with Brevo
5. Test on mobile devices

## Troubleshooting

If you encounter issues:

1. Check Supabase logs for errors
2. Verify RLS policies are correctly applied
3. Ensure user authentication is working
4. Check browser console for JavaScript errors

## Support

The new My Account system includes:
- ✅ Full backend integration
- ✅ Mobile responsive design
- ✅ Real-time messaging
- ✅ Review management
- ✅ Saved venues wishlist
- ✅ Payment methods (Stripe ready)
- ✅ Enhanced typography (Inter font)
- ✅ Reactive buttons and animations
- ✅ Accessibility features
- ✅ Security with RLS policies

All features are production-ready and follow industry best practices from platforms like Booking.com and Airbnb.
