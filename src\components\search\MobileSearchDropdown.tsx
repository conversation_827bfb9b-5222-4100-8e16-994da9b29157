import React, { useState } from 'react';
import { Search, MapPin, Calendar, Users, ChevronDown, ChevronUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import LocationSearch from './LocationSearch';
import DateRangePicker from './DateRangePicker';
import GuestPicker from './GuestPicker';

interface LocationData {
  lat: number;
  lng: number;
  displayName: string;
}

interface MobileSearchDropdownProps {
  className?: string;
}

export default function MobileSearchDropdown({ className = '' }: MobileSearchDropdownProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [location, setLocation] = useState<LocationData | null>(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [guests, setGuests] = useState(1);
  const navigate = useNavigate();

  const handleSearch = () => {
    const searchParams = new URLSearchParams();
    
    if (location) {
      searchParams.set('location', location.displayName);
      searchParams.set('lat', location.lat.toString());
      searchParams.set('lng', location.lng.toString());
    }
    
    if (startDate) searchParams.set('startDate', startDate);
    if (endDate) searchParams.set('endDate', endDate);
    if (guests > 1) searchParams.set('guests', guests.toString());

    console.log('🔍 MOBILE SEARCH: Navigating with params:', Object.fromEntries(searchParams));
    navigate(`/find-venues?${searchParams.toString()}`);
  };

  const getLocationDisplay = () => {
    if (location) return location.displayName;
    return 'Where?';
  };

  const getDateDisplay = () => {
    if (startDate && endDate) {
      const start = new Date(startDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
      const end = new Date(endDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
      return `${start} - ${end}`;
    }
    if (startDate) {
      return new Date(startDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
    }
    return 'When?';
  };

  const getGuestDisplay = () => {
    return guests === 1 ? '1 guest' : `${guests} guests`;
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Compact Search Bar - Always Visible */}
      <div 
        className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Search className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {location ? location.displayName : 'Search venues'}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  {startDate || endDate ? getDateDisplay() : 'Any date'} • {getGuestDisplay()}
                </div>
              </div>
            </div>
            <div className="ml-2">
              {isExpanded ? (
                <ChevronUp className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-400" />
              )}
            </div>
          </div>
        </div>

        {/* Expanded Search Form */}
        {isExpanded && (
          <div className="border-t border-gray-100 bg-gray-50">
            <div className="p-4 space-y-4">
              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="h-4 w-4 inline mr-1" />
                  Location
                </label>
                <LocationSearch
                  onLocationSelect={(locationData) => {
                    setLocation(locationData);
                  }}
                  placeholder="Search suburbs, cities..."
                  className="w-full"
                />
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Dates
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <DateRangePicker
                    startDate={startDate}
                    endDate={endDate}
                    onStartDateChange={setStartDate}
                    onEndDateChange={setEndDate}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Guests */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Users className="h-4 w-4 inline mr-1" />
                  Guests
                </label>
                <GuestPicker
                  guests={guests}
                  onGuestsChange={setGuests}
                  className="w-full"
                />
              </div>

              {/* Search Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleSearch();
                }}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2"
              >
                <Search className="h-5 w-5" />
                <span>Search Venues</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Quick Search Button - When Collapsed */}
      {!isExpanded && (location || startDate || guests > 1) && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleSearch();
          }}
          className="w-full mt-3 bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2"
        >
          <Search className="h-5 w-5" />
          <span>Search Now</span>
        </button>
      )}
    </div>
  );
}
