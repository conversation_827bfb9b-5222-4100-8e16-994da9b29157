const testCoords = [
  [-33.77978921185863, 151.03477924833007],
  [-33.827237376562216, 151.01123290230692],
  [-33.82828791299521, 151.00967847217177]
];

async function testCoordinate(lat, lng) {
  try {
    console.log(`Querying zoning for coordinates: ${lat}, ${lng}`);
    const zoningResponse = await fetch('http://localhost:3001/api/wfs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        serviceUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer',
        params: {
          service: 'WFS',
          version: '1.1.0',
          request: 'GetFeature',
          typeNames: 'EPI_Primary_Planning_Layers:2',
          outputFormat: 'application/json',
          filter: `
            <Filter xmlns='http://www.opengis.net/ogc' xmlns:gml='http://www.opengis.net/gml'>
              <Intersects>
                <PropertyName>Shape</PropertyName>
                <gml:Point srsName='EPSG:4326'>
                  <gml:coordinates>${lng},${lat}</gml:coordinates>
                </gml:Point>
              </Intersects>
            </Filter>
          `
        }
      })
    });

    if (!zoningResponse.ok) {
      console.error('Zoning request failed:', zoningResponse.status, zoningResponse.statusText);
      const errorBody = await zoningResponse.text();
      console.error('Error response:', errorBody);
      throw new Error(`Zoning request failed: ${zoningResponse.status}`);
    }

    const zoningData = await zoningResponse.json();
    console.log('Raw zoning response:', JSON.stringify(zoningData, null, 2));

    console.log(`Querying council for coordinates: ${lat}, ${lng}`);
    const lgaResponse = await fetch('http://localhost:3001/api/wfs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        serviceUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer',
        params: {
          service: 'WFS',
          version: '1.1.0',
          request: 'GetFeature',
          typeNames: 'EDP_Administrative_Boundaries:1',
          outputFormat: 'application/json',
          filter: `
            <Filter xmlns='http://www.opengis.net/ogc' xmlns:gml='http://www.opengis.net/gml'>
              <Intersects>
                <PropertyName>Shape</PropertyName>
                <gml:Point srsName='EPSG:4326'>
                  <gml:coordinates>${lng},${lat}</gml:coordinates>
                </gml:Point>
              </Intersects>
            </Filter>
          `
        }
      })
    });

    if (!lgaResponse.ok) {
      console.error('Council request failed:', lgaResponse.status, lgaResponse.statusText);
      const errorBody = await lgaResponse.text();
      console.error('Error response:', errorBody);
      throw new Error(`Council request failed: ${lgaResponse.status}`);
    }

    const lgaData = await lgaResponse.json();
    console.log('Raw council response:', JSON.stringify(lgaData, null, 2));

    return {
      coordinates: { lat, lng },
      zoning: zoningData?.features?.[0]?.properties
        ? {
            code: zoningData.features[0].properties.ZONE_CODE,
            name: zoningData.features[0].properties.ZONE_NAME
          }
        : { code: null, name: null },
      council: lgaData?.features?.[0]?.properties?.LGA_NAME ?? null
    };
  } catch (error) {
    return { coordinates: { lat, lng }, error: error.message };
  }
}

async function runTests() {
  for (const [lat, lng] of testCoords) {
    const result = await testCoordinate(lat, lng);
    console.log('Test Result:', JSON.stringify(result, null, 2));
  }
}

runTests().catch(console.error);
