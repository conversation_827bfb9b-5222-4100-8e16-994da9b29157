/* Mobile-first optimizations for HouseGoing */

/* Hero section mobile optimizations */
@media (max-width: 768px) {
  /* Reduce hero section height on mobile */
  .hero-section {
    min-height: 60vh !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  /* Mobile search dropdown optimizations */
  .mobile-search-dropdown {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
  }

  .mobile-search-dropdown .search-field {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
  }

  /* Booking form mobile optimizations */
  .booking-form-mobile {
    position: sticky;
    top: 80px;
    z-index: 40;
    margin: 0 -1rem;
    border-radius: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

/* Touch target improvements */
  /* Ensure all interactive elements meet 44px minimum touch target */
  button, 
  a, 
  input[type="button"], 
  input[type="submit"], 
  .btn,
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Button improvements with enhanced reactivity */
  .btn {
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
  }

  .btn:active {
    transform: scale(0.98);
    background-color: rgba(0, 0, 0, 0.1);
  }

  .btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
  }

  /* Input improvements */
  input, 
  select, 
  textarea {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    transition: border-color 0.15s ease, box-shadow 0.15s ease;
  }

  input:focus,
  select:focus,
  textarea:focus {
    border-color: #8B5CF6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    outline: none;
  }

  /* Navigation improvements */
  .nav-link {
    padding: 12px 16px;
    display: block;
    transition: background-color 0.15s ease;
  }

  .nav-link:active {
    background-color: rgba(139, 92, 246, 0.1);
  }

  /* Enhanced dropdown positioning */
  .dropdown-content,
  .search-dropdown {
    position: fixed !important;
    left: 8px !important;
    right: 8px !important;
    width: auto !important;
    max-height: 60vh;
    overflow-y: auto;
    z-index: 9999 !important;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Mobile menu enhancements */
  .mobile-menu {
    width: 100vw !important;
    max-width: none !important;
  }

  .mobile-menu-content {
    padding: 20px;
    max-height: 100vh;
    overflow-y: auto;
  }

  /* Search interface mobile optimization */
  .search-container {
    padding: 16px;
    gap: 12px;
  }

  .search-input {
    width: 100%;
    margin-bottom: 12px;
  }

  /* Card spacing improvements */
  .card {
    margin: 8px;
    padding: 16px;
    border-radius: 12px;
  }

  /* Grid improvements */
  .grid-mobile {
    grid-template-columns: 1fr !important;
    gap: 16px;
  }

  /* Typography scaling */
  h1 { font-size: 2rem; line-height: 1.2; }
  h2 { font-size: 1.75rem; line-height: 1.3; }
  h3 { font-size: 1.5rem; line-height: 1.4; }
  
  /* Spacing improvements */
  .section-mobile {
    padding: 24px 16px;
  }

  /* Loading states */
  .loading-mobile {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .btn {
    padding: 10px 14px;
    font-size: 15px;
  }

  input, select, textarea {
    min-height: 40px;
    font-size: 15px;
    padding: 10px 14px;
  }
}

/* Enhanced button reactivity for all screen sizes */
.btn-reactive {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.btn-reactive:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-reactive:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Improved focus states */
.focus-enhanced:focus {
  outline: 2px solid #8B5CF6;
  outline-offset: 2px;
  border-color: transparent;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent horizontal scroll */
body {
  overflow-x: hidden;
}

/* Enhanced mobile viewport handling */
@viewport {
  width: device-width;
  initial-scale: 1;
  maximum-scale: 1;
  user-scalable: no;
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  input, select, textarea {
    font-size: 16px !important; /* Prevents zoom */
  }
  
  .btn {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Android specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select {
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    padding-right: 40px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .btn {
    border-width: 0.5px;
  }
  
  input, select, textarea {
    border-width: 0.5px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .btn:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .nav-link:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .nav-link,
  input,
  select,
  textarea {
    transition: none;
  }
  
  .btn-reactive:hover,
  .btn-reactive:active {
    transform: none;
  }
}

/* Print styles */
@media print {
  .btn,
  .mobile-menu,
  .dropdown-content {
    display: none;
  }
}
