import React from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import GoogleSignInButton from '../../components/auth/GoogleSignInButton';

export default function Login() {
  const { error } = useAuth();
  const [searchParams] = useSearchParams();
  const isHost = searchParams.get('type') === 'host';

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-purple-100">
            <svg className="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {isHost ? 'Sign in to your host account' : 'Sign in to HouseGoing'}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {isHost ? (
              <>
                New to hosting?{' '}
                <Link to="/signup?type=host" className="font-medium text-purple-600 hover:text-purple-500">
                  Create your host account
                </Link>
              </>
            ) : (
              <>
                Don't have an account?{' '}
                <Link to="/signup" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign up for free
                </Link>
              </>
            )}
          </p>
        </div>

        <div className="mt-8 space-y-6">
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Authentication Error
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div>
            <GoogleSignInButton 
              className="w-full"
              size="lg"
            >
              {isHost ? 'Continue with Google as Host' : 'Continue with Google'}
            </GoogleSignInButton>
          </div>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-50 text-gray-500">
                  {isHost ? 'Host Portal' : 'Guest Portal'}
                </span>
              </div>
            </div>
          </div>

          {isHost ? (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Looking to book a venue instead?{' '}
                <Link to="/login" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign in as a guest
                </Link>
              </p>
            </div>
          ) : (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Are you a venue owner?{' '}
                <Link to="/login?type=host" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign in as a host
                </Link>
              </p>
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              By signing in, you agree to our{' '}
              <Link to="/terms" className="text-purple-600 hover:text-purple-500">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link to="/privacy" className="text-purple-600 hover:text-purple-500">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
