// Simple email server
import express from 'express';
import nodemailer from 'nodemailer';
import cors from 'cors';
import bodyParser from 'body-parser';

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(bodyParser.json());

// Create a reusable transporter object using Gmail SMTP transport
const transporter = nodemailer.createTransport({
  service: 'Gmail',
  auth: {
    user: process.env.GMAIL_USER || '<EMAIL>',
    pass: process.env.GMAIL_APP_PASSWORD || 'ztjj vkeu foty iyeg' // App Password for Gmail
  }
});

// Test email endpoint
app.post('/api/test-email', async (req, res) => {
  try {
    const mailOptions = {
      from: '"HouseGoing Platform" <<EMAIL>>',
      to: '<EMAIL>',
      subject: 'Email Service Test',
      text: 'This is a test email to verify that the email service is working correctly.',
      html: '<p>This is a test email to verify that the email service is working correctly.</p>'
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Test email sent successfully:', info.messageId);

    res.status(200).json({
      success: true,
      message: 'Test email sent successfully',
      messageId: info.messageId
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email',
      error: error.message
    });
  }
});

// Property submission email endpoint
app.post('/api/property-submission-email', async (req, res) => {
  try {
    const { name, id, address, type, ownerId } = req.body;

    // Email content
    const mailOptions = {
      from: '"HouseGoing Platform" <<EMAIL>>',
      to: '<EMAIL>', // Admin email
      subject: `New Property Submission: ${name}`,
      text: `
A new property has been submitted for review.

Property Details:
- Name: ${name}
- Address: ${address}
- Type: ${type}
- Owner ID: ${ownerId}
- Property ID: ${id}

Please log in to the admin portal to review this submission.
      `,
      html: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
  <h2 style="color: #7c3aed;">New Property Submission</h2>
  <p>A new property has been submitted for review.</p>

  <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <p><strong>Property Name:</strong> ${name}</p>
    <p><strong>Address:</strong> ${address}</p>
    <p><strong>Type:</strong> ${type}</p>
    <p><strong>Owner ID:</strong> ${ownerId}</p>
    <p><strong>Property ID:</strong> ${id}</p>
  </div>

  <p>Please <a href="http://localhost:5178/admin/properties" style="color: #7c3aed; text-decoration: none; font-weight: bold;">log in to the admin portal</a> to review this submission.</p>

  <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666;">
    <p>This is an automated message from HouseGoing. Please do not reply to this email.</p>
  </div>
</div>
      `
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log('Property submission email sent successfully:', info.messageId);

    res.status(200).json({
      success: true,
      message: 'Property submission email sent successfully',
      messageId: info.messageId
    });
  } catch (error) {
    console.error('Error sending property submission email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send property submission email',
      error: error.message
    });
  }
});

// Notification email endpoint for messages and reviews
app.post('/api/send-notification-email', async (req, res) => {
  try {
    const { to, from, fromName, subject, html, text } = req.body;

    // Validate required fields
    if (!to || !subject || (!html && !text)) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: to, subject, and either html or text'
      });
    }

    // Email options
    const mailOptions = {
      from: `"${fromName || 'HouseGoing'}" <${from || '<EMAIL>'}>`,
      to: to,
      subject: subject,
      text: text || '',
      html: html || text || ''
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log('Notification email sent successfully:', info.messageId);

    res.status(200).json({
      success: true,
      message: 'Notification email sent successfully',
      messageId: info.messageId
    });
  } catch (error) {
    console.error('Error sending notification email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send notification email',
      error: error.message
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Email server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`- POST /api/test-email`);
  console.log(`- POST /api/property-submission-email`);
  console.log(`- POST /api/send-notification-email`);
});
