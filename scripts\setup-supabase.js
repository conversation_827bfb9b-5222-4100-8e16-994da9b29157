// This script sets up the Supabase database schema
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://fxqoowlruissctsgbljk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk0MzI1NCwiZXhwIjoyMDU5NTE5MjU0fQ.9qjOAPoSojpa-91l9jlAI79Rkw3Och4gCjuTWM6KBLI';

// Create Supabase client with service role key for full access
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  console.log('Setting up Supabase database...');

  try {
    // Create user_profiles table
    console.log('Creating user_profiles table...');
    const { error: createTableError } = await supabase.rpc('exec_sql', { query: `
      CREATE TABLE IF NOT EXISTS user_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        clerk_id TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        role TEXT NOT NULL DEFAULT 'guest',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `});

    if (createTableError) {
      console.error('Error creating user_profiles table:', createTableError);
      return;
    }

    // Create indexes
    console.log('Creating indexes...');
    const { error: createIndexError } = await supabase.rpc('exec_sql', { query: `
      CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);
      CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
    `});

    if (createIndexError) {
      console.error('Error creating indexes:', createIndexError);
      return;
    }

    // Create updated_at trigger
    console.log('Creating updated_at trigger...');
    const { error: createTriggerError } = await supabase.rpc('exec_sql', { query: `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;

      CREATE TRIGGER update_user_profiles_updated_at
      BEFORE UPDATE ON user_profiles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `});

    if (createTriggerError) {
      console.error('Error creating trigger:', createTriggerError);
      return;
    }

    // Insert pre-registered hosts
    console.log('Inserting pre-registered hosts...');
    const { error: insertError } = await supabase
      .from('user_profiles')
      .upsert([
        {
          clerk_id: 'pre-registered-host-1',
          email: '<EMAIL>',
          role: 'host',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ], { onConflict: 'email' });

    if (insertError) {
      console.error('Error inserting pre-registered hosts:', insertError);
      return;
    }

    // Set up RLS policies
    console.log('Setting up RLS policies...');
    const { error: rlsError } = await supabase.rpc('exec_sql', { query: `
      ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

      -- Policy for users to read their own profile
      CREATE POLICY read_own_profile ON user_profiles
        FOR SELECT
        USING (auth.uid()::text = clerk_id);

      -- Policy for users to update their own profile
      CREATE POLICY update_own_profile ON user_profiles
        FOR UPDATE
        USING (auth.uid()::text = clerk_id);

      -- Policy for service role to read all profiles
      CREATE POLICY service_read_all_profiles ON user_profiles
        FOR SELECT
        TO service_role
        USING (true);

      -- Policy for service role to update all profiles
      CREATE POLICY service_update_all_profiles ON user_profiles
        FOR UPDATE
        TO service_role
        USING (true);

      -- Policy for service role to insert profiles
      CREATE POLICY service_insert_profiles ON user_profiles
        FOR INSERT
        TO service_role
        WITH CHECK (true);
    `});

    if (rlsError) {
      console.error('Error setting up RLS policies:', rlsError);
      return;
    }

    console.log('Database setup completed successfully!');

    // Verify the table was created
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*');

    if (error) {
      console.error('Error verifying table creation:', error);
      return;
    }

    console.log('User profiles in database:', data);

  } catch (error) {
    console.error('Error setting up database:', error);
  }
}

// Run the setup
setupDatabase();
