// Simple script to start the email server
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the email server script
const serverPath = path.join(__dirname, 'server', 'api', 'send-email.js');

console.log(`Starting email server from: ${serverPath}`);

// Spawn the server process
const server = spawn('node', [serverPath], {
  stdio: 'inherit'
});

server.on('error', (err) => {
  console.error('Failed to start email server:', err);
});

console.log('Email server process started');
