const { v4: uuidv4 } = require('uuid')
const { Webhook } = require('svix')

exports.handler = async (event, context) => {
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    }
  }

  // Verify webhook signature for security
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET
  if (!WEBHOOK_SECRET) {
    console.error('Missing CLERK_WEBHOOK_SECRET')
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Missing webhook secret' })
    }
  }

  const svix_id = event.headers['svix-id']
  const svix_timestamp = event.headers['svix-timestamp']
  const svix_signature = event.headers['svix-signature']

  if (!svix_id || !svix_timestamp || !svix_signature) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Missing svix headers' })
    }
  }

  const body = event.body
  const wh = new Webhook(WEBHOOK_SECRET)

  let evt

  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    })
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Invalid signature' })
    }
  }

  // Handle user.created event
  if (evt.type === 'user.created') {
    const userId = evt.data.id
    const uuid = uuidv4()

    console.log(`Setting external_id for user ${userId}: ${uuid}`)

    try {
      const response = await fetch(`https://api.clerk.dev/v1/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${process.env.CLERK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          external_id: uuid
        })
      })

      if (response.ok) {
        console.log(`✅ Successfully set external_id for user ${userId}`)
        return {
          statusCode: 200,
          body: JSON.stringify({ message: 'external_id set', uuid })
        }
      } else {
        const errorText = await response.text()
        console.error(`❌ Failed to set external_id:`, errorText)
        return {
          statusCode: 500,
          body: JSON.stringify({ error: errorText })
        }
      }
    } catch (error) {
      console.error('Error updating user:', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Failed to update user' })
      }
    }
  }

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'webhook received' })
  }
}
