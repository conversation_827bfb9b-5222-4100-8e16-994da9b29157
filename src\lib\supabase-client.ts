/**
 * Centralized Supabase client with Clerk integration
 *
 * This file provides a single instance of the Supabase client to be used throughout the application.
 * This client is configured to handle both authentication and database operations with Clerk JWT tokens.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// Supabase configuration - use environment variables for security
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_URL : '') ||
  'https://fxqoowlruissctsgbljk.supabase.co';

const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY : '') ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk';

// Create a singleton instance of the Supabase client
let supabaseInstance: SupabaseClient<Database>;

// Flag to track if we've already warned about multiple instances
let hasWarnedAboutMultipleInstances = false;

/**
 * Get the Supabase client instance
 * This ensures only one instance is created
 */
export function getSupabaseClient(): SupabaseClient<Database> {
  // Check if we already have an instance in the global scope
  // @ts-ignore
  if (typeof window !== 'undefined' && window.__SUPABASE_CLIENT_INSTANCE) {
    // @ts-ignore
    return window.__SUPABASE_CLIENT_INSTANCE;
  }

  if (!supabaseInstance) {
    // Create a new client with optimized settings
    supabaseInstance = createClient<Database>(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false, // Disable Supabase session persistence - Clerk handles this
        autoRefreshToken: false, // Disable auto-refresh - Clerk manages tokens
        detectSessionInUrl: false, // Disable URL detection to prevent conflicts with Clerk
        storageKey: 'housegoing-supabase-auth', // Unique storage key to avoid conflicts
        flowType: 'pkce', // More secure flow for auth
        debug: false // Disable debug mode to reduce console noise
      },
      global: {
        headers: {
          // Always include the API key in headers to prevent 401 errors
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json'
        }
      },
      db: {
        schema: 'public'
      },
      // Disable realtime subscriptions to reduce client instances
      realtime: {
        params: {
          eventsPerSecond: 0
        }
      }
    });

    // Store the instance in the global scope for reuse
    if (typeof window !== 'undefined') {
      // @ts-ignore
      window.__SUPABASE_CLIENT_INSTANCE = supabaseInstance;
    }

    console.log('Centralized Supabase client initialized');
  }

  return supabaseInstance;
}

// Patch the createClient function to prevent multiple instances
if (typeof window !== 'undefined') {
  try {
    // @ts-ignore
    const originalCreateClient = window.__ORIGINAL_SUPABASE_CREATE_CLIENT ||
                               // @ts-ignore
                               (window.__ORIGINAL_SUPABASE_CREATE_CLIENT = createClient);

    // @ts-ignore
    window.Supabase = window.Supabase || {};
    // @ts-ignore
    window.Supabase.createClient = function(...args: any[]) {
      if (!hasWarnedAboutMultipleInstances) {
        console.warn('WARNING: Attempted to create a new Supabase client. Using centralized client instead.');
        hasWarnedAboutMultipleInstances = true;
      }
      return getSupabaseClient();
    };
  } catch (error) {
    console.error('Error patching Supabase createClient:', error);
  }
}

/**
 * Create a Supabase client with Clerk session token (React/Vite version)
 * This follows the official Clerk-Supabase NATIVE integration pattern
 *
 * Key changes for native integration:
 * - No JWT template needed in Clerk Dashboard
 * - Uses accessToken function to automatically inject Clerk tokens
 * - Clerk must be set up as third-party auth provider in Supabase
 */
export function createClerkSupabaseClient(getToken: () => Promise<string | null>): SupabaseClient<Database> {
  return createClient<Database>(supabaseUrl, supabaseKey, {
    global: {
      // Use the accessToken function to inject Clerk session token
      // This is the native integration pattern - no template parameter needed
      async accessToken() {
        return await getToken();
      },
    },
    auth: {
      persistSession: false, // Disable Supabase session persistence - Clerk handles this
      autoRefreshToken: false, // Disable auto-refresh - Clerk manages tokens
      detectSessionInUrl: false, // Disable URL detection to prevent conflicts with Clerk
    },
  });
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use createClerkSupabaseClient instead
 */
export function createSupabaseClientWithClerkToken(clerkToken: string): SupabaseClient<Database> {
  return createClient<Database>(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
      storageKey: 'housegoing-clerk-supabase-auth',
      flowType: 'pkce',
      debug: false
    },
    global: {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${clerkToken}`, // Use Clerk token instead of Supabase key
        'Content-Type': 'application/json'
      }
    },
    db: {
      schema: 'public'
    },
    realtime: {
      params: {
        eventsPerSecond: 0
      }
    }
  });
}

// Export the supabase client
export const supabase = getSupabaseClient();

// Export default for compatibility with JavaScript imports
export default supabase;

// Add a warning if someone tries to create another client
export const warnAboutMultipleClients = () => {
  if (!hasWarnedAboutMultipleInstances) {
    console.warn('WARNING: You are creating a new Supabase client. This can cause the "Multiple GoTrueClient instances" warning. Use the centralized client from src/lib/supabase-client.ts instead.');
    hasWarnedAboutMultipleInstances = true;
  }
};
