import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Users, Music2, Utensils, Clock, Moon, Volume2 } from "lucide-react";

interface VenueCardProps {
  image: string;
  title: string;
  price: number;
  location: string;
  capacity: number;
  rating?: number;
  reviews?: number;
  amenities?: string[];
  tags?: string[];
  noiseRestrictions?: {
    curfewTime?: string; // e.g. "22:00"
    councilArea?: string;
    allowsOvernight?: boolean;
    notes?: string;
    windowsClosedAfter?: string; // e.g. "21:00"
    zoning?: 'residential' | 'commercial' | 'industrial' | 'mixed';
    residentialProximity?: 'adjacent' | 'nearby' | 'distant';
    soundproofing?: boolean;
    outdoorMusic?: {
      allowed: boolean;
      until?: string; // e.g. "22:00"
    };
  };
  partyScore?: {
    score: number; // 1-10 scale
    factors: string[];
  };
}

export function VenueCard({
  image,
  title,
  price,
  location,
  capacity,
  rating,
  reviews,
  amenities,
  tags,
  noiseRestrictions,
  partyScore
}: VenueCardProps) {

  // Format time for display (convert 24h to 12h format)
  const formatTime = (time24h?: string) => {
    if (!time24h) return null;

    try {
      const [hours, minutes] = time24h.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours % 12 || 12;
      return `${hours12}${minutes > 0 ? `:${minutes.toString().padStart(2, '0')}` : ''} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return time24h;
    }
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };
  return (
    <Card className="overflow-hidden card-hover group shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="aspect-video relative overflow-hidden">
        <img
          src={image}
          alt={title}
          className="object-cover w-full h-full transform group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
        <div className="absolute top-2 right-2 flex flex-col gap-2">
          <Badge className="bg-white text-primary shadow-sm">
            ${price}/hour
          </Badge>
          {tags?.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="secondary" className="bg-black/50 text-white shadow-sm">
              {tag}
            </Badge>
          ))}
          {tags && tags.length > 2 && (
            <Badge variant="secondary" className="bg-black/50 text-white shadow-sm">
              +{tags.length - 2} more
            </Badge>
          )}
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-lg mb-1 group-hover:text-primary transition-colors line-clamp-1">
          {title}
        </h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-1">{location}</p>

        <div className="flex flex-wrap items-center justify-between mb-3 gap-y-2">
          <div className="flex items-center text-sm text-gray-500">
            <Users className="w-4 h-4 mr-1 flex-shrink-0" />
            <span>Up to {capacity} guests</span>
          </div>
          {rating && reviews && (
            <div className="flex items-center">
              <Star className="w-4 h-4 text-primary fill-primary mr-1 flex-shrink-0" />
              <span className="text-sm font-medium">{formatRating(rating)}</span>
              <span className="text-sm text-gray-500 ml-1">({reviews})</span>
            </div>
          )}
        </div>

        {amenities && (
          <div className="border-t pt-3">
            <div className="flex flex-wrap gap-2">
              {amenities.slice(0, 3).map((amenity, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs bg-primary/5 border-primary/20"
                >
                  {amenity}
                </Badge>
              ))}
              {amenities.length > 3 && (
                <Badge
                  variant="outline"
                  className="text-xs bg-primary/10 border-primary/30"
                >
                  +{amenities.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Noise Restrictions */}
        {noiseRestrictions && (
          <div className="border-t mt-3 pt-3">
            <div className="flex items-center mb-2">
              <Volume2 className="w-4 h-4 text-amber-500 mr-1 flex-shrink-0" />
              <span className="text-xs font-medium text-amber-700">Noise Restrictions</span>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-1">
              {noiseRestrictions.curfewTime && (
                <div className="flex items-center text-xs text-gray-600">
                  <Clock className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="truncate">Quiet after {formatTime(noiseRestrictions.curfewTime)}</span>
                </div>
              )}

              {noiseRestrictions.outdoorMusic && (
                <div className="flex items-center text-xs text-gray-600">
                  <Music2 className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="truncate">
                    {noiseRestrictions.outdoorMusic.allowed
                      ? `Outdoor music until ${formatTime(noiseRestrictions.outdoorMusic.until)}`
                      : 'No outdoor music'}
                  </span>
                </div>
              )}

              {noiseRestrictions.allowsOvernight !== undefined && (
                <div className="flex items-center text-xs text-gray-600">
                  <Moon className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{noiseRestrictions.allowsOvernight ? 'Overnight stays allowed' : 'No overnight stays'}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Party Score */}
        {partyScore && (
          <div className="border-t mt-3 pt-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Music2 className="w-4 h-4 text-primary mr-1 flex-shrink-0" />
                <span className="text-xs font-medium">Party Score</span>
              </div>
              <Badge className={`text-xs ${
                partyScore.score >= 8 ? 'bg-green-100 text-green-800' :
                partyScore.score >= 5 ? 'bg-amber-100 text-amber-800' :
                'bg-red-100 text-red-800'
              }`}>
                {partyScore.score.toFixed(1)}
              </Badge>
            </div>

            {partyScore.factors && partyScore.factors.length > 0 && (
              <div className="text-xs text-gray-600 mt-1">
                <span className="line-clamp-1">
                  {partyScore.factors[0]}
                  {partyScore.factors.length > 1 && ` + ${partyScore.factors.length - 1} more`}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
}