/**
 * Example: Proper Clerk-Supabase Integration for React/Vite
 * 
 * This file demonstrates the correct way to integrate Clerk with Supabase
 * following the official documentation patterns, adapted for React/Vite
 * instead of Next.js.
 */

import React, { useEffect, useState } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { createClerkSupabaseClient } from '../lib/supabase-client';

// Example component showing how to use Clerk with Supabase
export default function ClerkSupabaseExample() {
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [name, setName] = useState('');
  
  // The `useUser()` hook ensures that <PERSON> has loaded data about the signed in user
  const { user } = useUser();
  
  // The `useAuth()` hook is used to get the Clerk session object
  // The session object is used to get the Clerk session token
  const { getToken } = useAuth();

  // Create a custom Supabase client that injects the Clerk session token
  // This follows the official Clerk-Supabase integration pattern
  const clerkSupabase = createClerkSupabaseClient(async () => {
    try {
      // Get the token with the 'supabase' template
      // This template must be configured in your Clerk Dashboard
      return await getToken({ template: 'supabase' });
    } catch (error) {
      console.error('Error getting Clerk token:', error);
      return null;
    }
  });

  // This `useEffect` will wait for the User object to be loaded before requesting
  // the tasks for the signed in user
  useEffect(() => {
    if (!user) return;

    async function loadTasks() {
      setLoading(true);
      try {
        // Use the Clerk-authenticated Supabase client
        const { data, error } = await clerkSupabase
          .from('user_profiles') // Using our existing table
          .select('*')
          .eq('email', user.primaryEmailAddress?.emailAddress);
        
        if (!error) {
          setTasks(data || []);
        } else {
          console.error('Error loading user profiles:', error);
        }
      } catch (error) {
        console.error('Error in loadTasks:', error);
      }
      setLoading(false);
    }

    loadTasks();
  }, [user, clerkSupabase]);

  async function createTask(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!name.trim()) return;

    try {
      // Insert task into the database using Clerk-authenticated client
      const { error } = await clerkSupabase
        .from('user_profiles')
        .insert({
          id: `${user?.id}-${Date.now()}`, // Simple ID generation
          email: user?.primaryEmailAddress?.emailAddress || '',
          first_name: name,
          last_name: '',
          role: 'customer',
          is_host: false,
        });

      if (error) {
        console.error('Error creating task:', error);
      } else {
        setName('');
        // Reload tasks
        window.location.reload();
      }
    } catch (error) {
      console.error('Error in createTask:', error);
    }
  }

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Clerk-Supabase Integration Example</h1>
        <p>Please sign in to see this example in action.</p>
        <a href="/login" className="text-purple-600 hover:text-purple-800 underline">
          Go to Login
        </a>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Clerk-Supabase Integration Example</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
        <h2 className="font-semibold text-blue-900 mb-2">User Information from Clerk</h2>
        <div className="text-blue-800 text-sm space-y-1">
          <p><strong>ID:</strong> {user.id}</p>
          <p><strong>Email:</strong> {user.primaryEmailAddress?.emailAddress}</p>
          <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>
        </div>
      </div>

      <div className="bg-white border rounded p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">User Profiles from Supabase</h2>
        
        {loading && <p className="text-gray-600">Loading...</p>}

        {!loading && tasks.length > 0 && (
          <div className="space-y-2 mb-4">
            {tasks.map((task: any) => (
              <div key={task.id} className="p-3 bg-gray-50 rounded">
                <p><strong>Email:</strong> {task.email}</p>
                <p><strong>Name:</strong> {task.first_name} {task.last_name}</p>
                <p><strong>Role:</strong> {task.role}</p>
              </div>
            ))}
          </div>
        )}

        {!loading && tasks.length === 0 && (
          <p className="text-gray-600 mb-4">No user profiles found</p>
        )}

        <form onSubmit={createTask} className="flex gap-2">
          <input
            autoFocus
            type="text"
            name="name"
            placeholder="Enter first name for new profile"
            onChange={(e) => setName(e.target.value)}
            value={name}
            className="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-purple-500 focus:border-purple-500"
          />
          <button 
            type="submit"
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Add Profile
          </button>
        </form>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
        <h3 className="font-semibold text-yellow-900 mb-2">Integration Notes</h3>
        <ul className="text-yellow-800 text-sm space-y-1">
          <li>• This uses the official Clerk-Supabase integration pattern</li>
          <li>• The `accessToken` function automatically injects Clerk JWT tokens</li>
          <li>• Requires JWT template named 'supabase' in Clerk Dashboard</li>
          <li>• Requires Supabase to be configured to accept Clerk JWT tokens</li>
          <li>• Works with Row Level Security (RLS) policies based on JWT claims</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Key Differences from Next.js version:
 * 
 * 1. Uses `@clerk/clerk-react` instead of `@clerk/nextjs`
 * 2. Uses `useAuth()` instead of `useSession()`
 * 3. Uses `getToken()` instead of `session?.getToken()`
 * 4. No server-side rendering components needed
 * 5. Environment variables use VITE_ prefix instead of NEXT_PUBLIC_
 */

/**
 * Required Configuration:
 * 
 * 1. Clerk Dashboard:
 *    - Create JWT template named 'supabase'
 *    - Configure template with proper claims for Supabase
 * 
 * 2. Supabase Dashboard:
 *    - Configure JWT settings to accept Clerk tokens
 *    - Set JWT issuer to your Clerk domain
 *    - Set JWT audience to 'authenticated'
 * 
 * 3. Environment Variables:
 *    - VITE_CLERK_PUBLISHABLE_KEY
 *    - VITE_SUPABASE_URL
 *    - VITE_SUPABASE_ANON_KEY
 */
