// Compatibility layer for <PERSON> hooks - replaced with Supabase Auth
// This provides backward compatibility for components that haven't been updated yet

import { useAuth } from '../providers/AuthProvider';

// Mock Clerk user format for backward compatibility
export function useUser() {
  const { user, isLoading, isAuthenticated } = useAuth();
  
  if (isLoading) {
    return { user: undefined, isLoaded: false, isSignedIn: undefined };
  }
  
  if (!isAuthenticated || !user) {
    return { user: null, isLoaded: true, isSignedIn: false };
  }
  
  // Convert Supabase user to Clerk-like format for backward compatibility
  const clerkLikeUser = {
    id: user.id,
    primaryEmailAddress: {
      emailAddress: user.email
    },
    fullName: user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0],
    firstName: user.user_metadata?.first_name,
    lastName: user.user_metadata?.last_name,
    imageUrl: user.user_metadata?.avatar_url || '',
    primaryPhoneNumber: {
      phoneNumber: user.user_metadata?.phone || ''
    },
    email: user.email,
    // Add any other properties that components might expect
    user_metadata: user.user_metadata
  };
  
  return { user: clerkLikeUser, isLoaded: true, isSignedIn: true };
}

// Mock Clerk auth for backward compatibility
export function useClerkAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  
  return {
    isSignedIn: isAuthenticated,
    isLoaded: !isLoading,
    getToken: async () => null // Placeholder - implement if needed
  };
}

// Mock session for backward compatibility
export function useSession() {
  const { session, isLoading, isAuthenticated } = useAuth();
  
  if (isLoading) {
    return { session: undefined, isLoaded: false, isSignedIn: undefined };
  }
  
  return {
    session: isAuthenticated ? session : null,
    isLoaded: true,
    isSignedIn: isAuthenticated
  };
}
