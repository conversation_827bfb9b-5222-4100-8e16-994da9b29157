{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:netlify": "netlify dev", "build": "vite build && node scripts/generate-redirects.js", "build:prod": "node scripts/generate-sitemap.js && npm run validate-sitemap && node scripts/build-production.js", "build:secure": "npm run security:check && npm run build", "deploy:prod": "bash scripts/deploy-production.sh", "security:check": "npm run security:audit && npm run security:scan", "security:audit": "npm audit --audit-level moderate", "security:scan": "grep -r 'console.log\\|TODO\\|FIXME' src/ || true", "security:validate": "node scripts/validate-security.js", "lint": "eslint .", "lint:security": "eslint . --ext .ts,.tsx --config .eslintrc.security.js", "preview": "vite preview", "functions:dev": "netlify functions:serve", "setup-db": "node scripts/setup-supabase.js", "setup-db-direct": "node scripts/setup-supabase-direct.js", "setup-db-complete": "node scripts/setup-supabase-complete.js", "setup-db-api": "node scripts/supabase-api-setup.js", "setup-db-es": "node scripts/setup-supabase-es.js", "setup-database": "npx ts-node src/scripts/setup-database.ts", "generate-redirects": "node scripts/generate-redirects.js", "generate-sitemap": "node scripts/generate-sitemap.js", "validate-sitemap": "node scripts/validate-sitemap.js", "test-sitemap": "node scripts/test-sitemap.js", "check-sitemap-headers": "node scripts/check-sitemap-headers.js"}, "dependencies": {"@clerk/clerk-react": "^5.25.6", "@google/generative-ai": "^0.24.0", "@headlessui/react": "^2.2.1", "@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/openai": "^0.5.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^2.4.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.45.0", "@testing-library/react": "^16.3.0", "@turf/turf": "^7.2.0", "@types/geojson": "^7946.0.16", "@types/nodemailer": "^6.4.17", "@types/react-leaflet": "^2.8.3", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "body-parser": "^2.2.0", "cloudinary": "^2.6.1", "cloudinary-react": "^1.8.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "i18next": "^25.1.1", "langchain": "^0.3.21", "langsmith": "^0.3.15", "leaflet": "^1.9.4", "leaflet-control-geocoder": "^1.13.0", "lucide-react": "^0.344.0", "mapbox-gl": "^2.15.0", "nodemailer": "^7.0.3", "openai": "^4.91.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-map-gl": "^7.1.7", "react-router-dom": "^6.22.3", "stripe": "^18.1.1", "svix": "^1.66.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/leaflet": "^1.9.17", "@types/next": "^8.0.7", "@types/node": "^22.15.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "chalk": "^4.1.2", "dotenv": "^16.5.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "node-fetch": "^3.3.2", "ora": "^5.4.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.40.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "xmldom": "^0.6.0"}}