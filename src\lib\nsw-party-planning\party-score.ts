/**
 * NSW Party Planning Tool - Party Score Calculator
 * 
 * This module provides functions to calculate a party score based on zoning,
 * property type, and other factors.
 */

interface PartyScoreFactors {
  zoneCode: string;
  zoneName: string;
  propertyType: string;
  curfewStart: string;
  outdoorAllowed: boolean;
  specialLocalRule?: string;
  isWeekend: boolean;
  dataConfidence: 'High' | 'Medium' | 'Low';
}

interface ScoreBreakdown {
  zoning: number;
  propertyType: number;
  curfewTime: number;
  outdoorAllowance: number;
  specialLocalRule: number;
  dayFactor: number;
  dataConfidence: number;
  total: number;
}

interface PartyScoreResult {
  score: number;
  band: 'Great for Parties' | 'Plan Carefully' | 'Highly Restricted';
  color: 'green' | 'yellow' | 'red';
  breakdown: ScoreBreakdown;
}

/**
 * Calculate a party score based on various factors
 * @param factors - Factors affecting the party score
 * @returns Party score result with breakdown
 */
export function calculatePartyScore(factors: PartyScoreFactors): PartyScoreResult {
  const breakdown: ScoreBreakdown = {
    zoning: 0,
    propertyType: 0,
    curfewTime: 0,
    outdoorAllowance: 0,
    specialLocalRule: 0,
    dayFactor: 0,
    dataConfidence: 0,
    total: 0
  };

  // 1. Zoning Group (up to 4 points)
  const zoneCode = factors.zoneCode || 'R2';
  
  // Business/Commercial Zones (B1-B8)
  if (zoneCode.startsWith('B')) {
    breakdown.zoning = 4;
  }
  // Industrial Zones (IN1-IN4)
  else if (zoneCode.startsWith('IN')) {
    breakdown.zoning = 4;
  }
  // Recreation Zones (RE1-RE2)
  else if (zoneCode.startsWith('RE')) {
    // RE1 (Public Recreation) is better for events than RE2 (Private Recreation)
    breakdown.zoning = zoneCode === 'RE1' ? 4 : 3.5;
  }
  // Rural Zones (RU1-RU5)
  else if (zoneCode.startsWith('RU')) {
    // RU5 (Village) is better for events than other rural zones
    breakdown.zoning = zoneCode === 'RU5' ? 3 : 2.5;
  }
  // Residential Zones (R1-R5)
  else if (zoneCode.startsWith('R')) {
    // R1 (General Residential) and R4 (High Density) are more restrictive
    if (zoneCode === 'R1' || zoneCode === 'R4') {
      breakdown.zoning = 1.5;
    }
    // R2 (Low Density) and R3 (Medium Density) are moderately restrictive
    else if (zoneCode === 'R2' || zoneCode === 'R3') {
      breakdown.zoning = 2;
    }
    // R5 (Large Lot Residential) is less restrictive
    else if (zoneCode === 'R5') {
      breakdown.zoning = 3;
    }
    else {
      breakdown.zoning = 2;
    }
  }
  // Special Purpose Zones (SP1-SP3)
  else if (zoneCode.startsWith('SP')) {
    // Highly variable, default to middle value
    breakdown.zoning = 2;
  }
  // Environment Protection Zones (E1-E4)
  else if (zoneCode.startsWith('E')) {
    breakdown.zoning = 0.5;
  }
  // Waterway Zones (W1-W3)
  else if (zoneCode.startsWith('W')) {
    breakdown.zoning = 0.5;
  }
  // Default for unknown zones
  else {
    breakdown.zoning = 2;
  }

  // 2. Property Type (up to 1.5 points)
  const propertyType = factors.propertyType.toLowerCase();
  if (propertyType.includes('house')) {
    breakdown.propertyType = 1.5;
  }
  else if (propertyType.includes('duplex') || propertyType.includes('townhouse') || propertyType.includes('semi')) {
    breakdown.propertyType = 1;
  }
  else if (propertyType.includes('apartment') || propertyType.includes('unit')) {
    breakdown.propertyType = 0.5;
  }
  else if (propertyType.includes('heritage')) {
    breakdown.propertyType = 0;
  }
  else if (propertyType.includes('commercial')) {
    breakdown.propertyType = 1.5;
  }
  else if (propertyType.includes('industrial')) {
    breakdown.propertyType = 1.5;
  }
  else {
    breakdown.propertyType = 1;
  }

  // 3. Curfew/Ending Time (up to 2 points)
  const curfewHour = parseInt(factors.curfewStart.split(':')[0], 10);
  if (curfewHour >= 23) {
    breakdown.curfewTime = 2;
  }
  else if (curfewHour >= 22) {
    breakdown.curfewTime = 1.5;
  }
  else if (curfewHour >= 21) {
    breakdown.curfewTime = 1;
  }
  else if (curfewHour >= 20) {
    breakdown.curfewTime = 0.5;
  }
  else {
    breakdown.curfewTime = 0;
  }

  // 4. Outdoor Allowance (up to 1 point)
  breakdown.outdoorAllowance = factors.outdoorAllowed ? 1 : 0;

  // 5. Special Local Rule (up to 1 point)
  const specialRule = factors.specialLocalRule?.toLowerCase() || '';
  if (specialRule.includes('strict') || specialRule.includes('enforcement')) {
    breakdown.specialLocalRule = -1;
  }
  else if (specialRule.includes('lenien') || specialRule.includes('exempt')) {
    breakdown.specialLocalRule = 0.5;
  }
  else {
    breakdown.specialLocalRule = 0;
  }

  // 6. Day/Date Factor (up to 0.5 points)
  breakdown.dayFactor = factors.isWeekend ? 0.5 : 0;

  // 7. Data Confidence (up to 0.5 points)
  if (factors.dataConfidence === 'High') {
    breakdown.dataConfidence = 0.5;
  }
  else if (factors.dataConfidence === 'Medium') {
    breakdown.dataConfidence = 0.25;
  }
  else {
    breakdown.dataConfidence = 0;
  }

  // Calculate total score
  const totalScore = Object.values(breakdown).reduce((sum, value) => sum + value, 0) - breakdown.total;
  
  // Round to nearest integer and clamp between 0 and 10
  const roundedScore = Math.round(totalScore);
  const clampedScore = Math.max(0, Math.min(10, roundedScore));
  
  breakdown.total = clampedScore;

  // Determine score band
  let band: 'Great for Parties' | 'Plan Carefully' | 'Highly Restricted';
  let color: 'green' | 'yellow' | 'red';
  
  if (clampedScore >= 8) {
    band = 'Great for Parties';
    color = 'green';
  }
  else if (clampedScore >= 5) {
    band = 'Plan Carefully';
    color = 'yellow';
  }
  else {
    band = 'Highly Restricted';
    color = 'red';
  }

  return {
    score: clampedScore,
    band,
    color,
    breakdown
  };
}

/**
 * Get recommendations based on party score and factors
 * @param score - Party score
 * @param factors - Factors affecting the party score
 * @returns Array of recommendations
 */
export function getPartyScoreRecommendations(
  score: number, 
  factors: PartyScoreFactors
): string[] {
  const recommendations: string[] = [];

  // Base recommendations by score band
  if (score >= 8) {
    recommendations.push('This location is generally suitable for parties and events.');
  } else if (score >= 5) {
    recommendations.push('This location requires careful planning for parties and events.');
  } else {
    recommendations.push('This location has significant restrictions for parties and events.');
  }

  // Specific recommendations based on factors
  const zoneCode = factors.zoneCode || 'R2';
  const propertyType = factors.propertyType.toLowerCase();
  const curfewHour = parseInt(factors.curfewStart.split(':')[0], 10);

  // Zoning-specific recommendations
  if (zoneCode.startsWith('R')) {
    recommendations.push('In residential zones, notify neighbors in advance and provide contact details.');
  }
  
  if (zoneCode.startsWith('B')) {
    recommendations.push('Commercial zones typically allow higher noise levels, but check for nearby residences.');
  }
  
  if (zoneCode.startsWith('IN')) {
    recommendations.push('Industrial zones have fewer noise restrictions, but may have specific safety requirements.');
  }

  // Property type recommendations
  if (propertyType.includes('apartment') || propertyType.includes('unit')) {
    recommendations.push('In apartments, be mindful of shared walls and strata bylaws that may impose stricter rules.');
  }
  
  if (propertyType.includes('duplex') || propertyType.includes('townhouse')) {
    recommendations.push('With shared walls, consider using sound-absorbing materials and keeping speakers away from walls.');
  }

  // Curfew recommendations
  if (curfewHour <= 21) {
    recommendations.push(`Early noise curfew (${curfewHour}:00) requires transitioning to quiet activities earlier.`);
  }
  
  // Outdoor recommendations
  if (!factors.outdoorAllowed) {
    recommendations.push('Plan to move all activities indoors after the outdoor cutoff time.');
  }

  // Weekend vs weekday
  if (!factors.isWeekend) {
    recommendations.push('Weekday events should be more considerate of neighbors who may need to work the next day.');
  }

  return recommendations;
}

export default {
  calculatePartyScore,
  getPartyScoreRecommendations
};
