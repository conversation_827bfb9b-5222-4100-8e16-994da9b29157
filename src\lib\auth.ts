import { Auth } from '@auth/core'
import Google from '@auth/core/providers/google'
import { supabase } from './supabase-client'

export const authConfig = {
  providers: [
    Google({
      clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      clientSecret: import.meta.env.VITE_GOOGLE_CLIENT_SECRET,
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          // Check if user exists in Supabase
          const { data: existingUser, error: fetchError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('email', user.email)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('Error checking existing user:', fetchError);
            return false;
          }

          // If user doesn't exist, create them
          if (!existingUser) {
            const { error: insertError } = await supabase
              .from('user_profiles')
              .insert({
                email: user.email,
                first_name: user.name?.split(' ')[0] || '',
                last_name: user.name?.split(' ').slice(1).join(' ') || '',
                role: 'guest',
                is_host: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });

            if (insertError) {
              console.error('Error creating user profile:', insertError);
              return false;
            }

            console.log('✅ New user profile created for:', user.email);
          } else {
            console.log('✅ Existing user found:', user.email);
          }

          return true;
        } catch (error) {
          console.error('Error in signIn callback:', error);
          return false;
        }
      }
      return true;
    },
    async session({ session, token }) {
      return session;
    },
    async jwt({ token, user, account }) {
      return token;
    }
  },
  pages: {
    signIn: '/login',
    signUp: '/signup',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt' as const,
  },
  secret: import.meta.env.VITE_AUTH_SECRET,
}

// Helper function to handle Auth.js requests
export async function handleAuthRequest(request: Request): Promise<Response> {
  return await Auth(request, authConfig)
}
