import express from 'express';
import cors from 'cors';
import { ExpressAuth } from '@auth/express';
import Google from '@auth/core/providers/google';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Validate required environment variables
const requiredVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY', 'GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'AUTH_SECRET'];
const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  process.exit(1);
}

const app = express();
const PORT = process.env.AUTH_SERVER_PORT || 3001;

// Initialize Supabase Admin Client (server-side only)
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5175',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie']
}));

app.use(express.json());

// Auth.js configuration
const authConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          console.log('🔍 Checking user in Supabase:', user.email);
          
          // Check if user exists in customers table
          const { data: existingCustomer, error: fetchError } = await supabaseAdmin
            .from('customers')
            .select('*')
            .eq('email', user.email)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('❌ Error checking existing customer:', fetchError);
            return false;
          }

          // If user doesn't exist, create them
          if (!existingCustomer) {
            const { data: newCustomer, error: insertError } = await supabaseAdmin
              .from('customers')
              .insert({
                email: user.email,
                name: user.name || profile?.name || '',
                created_at: new Date().toISOString()
              })
              .select()
              .single();

            if (insertError) {
              console.error('❌ Error creating customer:', insertError);
              return false;
            }

            console.log('✅ New customer created:', newCustomer.email);
          } else {
            console.log('✅ Existing customer found:', existingCustomer.email);
            
            // Update name if it's missing or different
            if (!existingCustomer.name || existingCustomer.name !== user.name) {
              const { error: updateError } = await supabaseAdmin
                .from('customers')
                .update({ 
                  name: user.name || profile?.name || existingCustomer.name 
                })
                .eq('id', existingCustomer.id);

              if (updateError) {
                console.error('⚠️ Error updating customer name:', updateError);
              } else {
                console.log('✅ Customer name updated');
              }
            }
          }

          return true;
        } catch (error) {
          console.error('❌ Error in signIn callback:', error);
          return false;
        }
      }
      return true;
    },
    async session({ session, token }) {
      // Add custom properties to session if needed
      if (session?.user?.email) {
        try {
          const { data: customer } = await supabaseAdmin
            .from('customers')
            .select('id, name, created_at')
            .eq('email', session.user.email)
            .single();
          
          if (customer) {
            session.user.customerId = customer.id;
            session.user.customerCreatedAt = customer.created_at;
          }
        } catch (error) {
          console.error('⚠️ Error fetching customer data for session:', error);
        }
      }
      return session;
    },
    async jwt({ token, user, account }) {
      // Store additional info in JWT if needed
      if (account?.provider === 'google') {
        token.provider = 'google';
      }
      return token;
    }
  },
  pages: {
    signIn: `${process.env.FRONTEND_URL}/login`,
    error: `${process.env.FRONTEND_URL}/auth/error`,
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.AUTH_SECRET,
  trustHost: true,
};

// Mount Auth.js routes
app.use('/auth', ExpressAuth(authConfig));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'auth-server'
  });
});

// Get current session endpoint
app.get('/auth/session', async (req, res) => {
  try {
    // This would typically be handled by Auth.js middleware
    // For now, return a simple response
    res.json({ session: null });
  } catch (error) {
    console.error('Error getting session:', error);
    res.status(500).json({ error: 'Failed to get session' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Auth server running on http://localhost:${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth endpoints: http://localhost:${PORT}/auth/*`);
});

export default app;
