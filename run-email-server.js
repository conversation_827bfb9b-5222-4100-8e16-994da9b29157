// Simple script to run the email server
import { exec } from 'child_process';

console.log('Starting email server...');

// Run the email server
const server = exec('node email-server.js', (error, stdout, stderr) => {
  if (error) {
    console.error(`Error: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`Stderr: ${stderr}`);
    return;
  }
  console.log(`Stdout: ${stdout}`);
});

// Keep the script running
console.log('Email server started. Press Ctrl+C to stop.');
