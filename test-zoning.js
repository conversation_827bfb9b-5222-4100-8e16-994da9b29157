// Import the enhancedAddressUtils module
import { detectZoning, extractAddressComponents } from './src/utils/enhancedAddressUtils.js';

// Test address
const address = '20 South Street, Rydalmere NSW 2116, Australia';
const lat = -33.8148;
const lng = 151.0233;

// Extract components
const components = extractAddressComponents(address);
console.log('Address components:', components);

// Detect zoning
const zoning = detectZoning(address, lat, lng);
console.log('Detected zoning:', zoning);
