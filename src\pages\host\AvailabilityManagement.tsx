import React, { useState, useEffect } from 'react';
import { useAuth } from '../../providers/AuthProvider';
import { Building, Calendar, AlertCircle, Plus } from 'lucide-react';
import AvailabilityManager from '../../components/owner/AvailabilityManager';
import { getOwnerLiveVenues, LiveVenue } from '../../services/propertyDataService';
import { getSupabaseClient } from '../../lib/supabase-client';

export default function AvailabilityManagement() {
  const { user, isAuthenticated } = useAuth();
  const [venues, setVenues] = useState<LiveVenue[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<LiveVenue | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadOwnerVenues();
  }, [user]);

  const loadOwnerVenues = async () => {
    if (!user?.email) {
      setError('User email not found');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const ownerEmail = user.email;

      // Try to get venues from database first
      let venuesData: LiveVenue[] = [];

      try {
        const supabase = getSupabaseClient();
        const { data: dbVenues, error: dbError } = await supabase
          .from('venues')
          .select('*')
          .eq('owner_id', user.id)
          .eq('is_active', true);

        if (dbError) {
          console.warn('Database error, falling back to service:', dbError);
          throw dbError;
        }

        if (dbVenues && dbVenues.length > 0) {
          // Convert database venues to LiveVenue format
          venuesData = dbVenues.map(venue => ({
            id: venue.id,
            name: venue.title,
            address: venue.address || venue.location,
            capacity: venue.capacity,
            price_per_hour: venue.price,
            status: 'live' as const,
            images: venue.images || []
          }));

          console.log(`✅ Loaded ${venuesData.length} venues from database`);
        } else {
          // Fallback to service if no database venues
          console.log('No venues in database, trying service...');
          venuesData = await getOwnerLiveVenues(ownerEmail);
        }
      } catch (dbError) {
        console.warn('Database unavailable, using service:', dbError);
        // Fallback to existing service
        venuesData = await getOwnerLiveVenues(ownerEmail);
      }

      setVenues(venuesData);

      // Auto-select first venue if available
      if (venuesData.length > 0) {
        setSelectedVenue(venuesData[0]);
      }
    } catch (err) {
      console.error('Error loading owner venues:', err);
      setError('Failed to load your venues');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-2 text-gray-600">Loading your venues...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
          <button
            onClick={loadOwnerVenues}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (venues.length === 0) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Venues Found</h3>
          <p className="text-gray-600 mb-6">
            You don't have any approved venues yet. Add a venue to start managing availability.
          </p>
          <button
            onClick={() => window.location.href = '/add-listing'}
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Venue
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Calendar className="h-8 w-8 mr-3 text-purple-600" />
          Availability Management
        </h1>
        <p className="text-gray-600 mt-2">
          Manage when your venues are available for bookings. Set operating hours, block dates, and configure booking rules.
        </p>
      </div>

      {/* Venue Selector */}
      {venues.length > 1 && (
        <div className="mb-8">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Select Venue to Manage
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {venues.map((venue) => (
              <button
                key={venue.id}
                onClick={() => setSelectedVenue(venue)}
                className={`p-4 rounded-lg border-2 text-left transition-colors ${
                  selectedVenue?.id === venue.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div className="flex items-start">
                  <Building className="h-5 w-5 text-gray-400 mt-1 mr-3 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <h3 className="font-medium text-gray-900 truncate">
                      {venue.name}
                    </h3>
                    <p className="text-sm text-gray-600 truncate">
                      {venue.address}
                    </p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        Live
                      </span>
                      <span className="ml-2">
                        {venue.capacity} guests
                      </span>
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Single Venue Display */}
      {venues.length === 1 && (
        <div className="mb-8">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center">
              <Building className="h-6 w-6 text-purple-600 mr-3" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {venues[0].name}
                </h2>
                <p className="text-gray-600">{venues[0].address}</p>
                <div className="flex items-center mt-2 text-sm text-gray-500">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                    Live
                  </span>
                  <span className="ml-3">Capacity: {venues[0].capacity} guests</span>
                  <span className="ml-3">Price: ${venues[0].price_per_hour}/hour</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Availability Manager */}
      {selectedVenue && (
        <AvailabilityManager
          venueId={selectedVenue.id}
          venueName={selectedVenue.name}
        />
      )}

      {/* Help Section */}
      <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          How Availability Management Works
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
          <div>
            <h4 className="font-medium mb-2">🕒 Operating Hours</h4>
            <p>Set different operating hours for each day of the week. Guests can only book during these times.</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">📅 Block Dates</h4>
            <p>Block specific dates or time slots when your venue is not available for bookings.</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">⚙️ Booking Rules</h4>
            <p>Set minimum/maximum booking duration, lead time requirements, and advance booking limits.</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">🔄 Real-time Updates</h4>
            <p>Changes are reflected immediately in search results and booking availability.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
