/**
 * Generate Sitemap Script
 *
 * This script generates a sitemap.xml file for the website.
 * It includes static routes and can be extended to include dynamic routes from the database.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const publicDir = path.join(rootDir, 'public');

// Supabase client for fetching dynamic routes
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fxqoowlruissctsgbljk.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk';
const supabase = createClient(supabaseUrl, supabaseKey);

// Base URL for the website
const baseUrl = 'https://housegoing.com.au';

// Current date in YYYY-MM-DD format
const currentDate = new Date().toISOString().split('T')[0];

// Static routes with their metadata
const staticRoutes = [
  // Core pages
  { path: '/', priority: '1.0', changefreq: 'weekly' },
  { path: '/find-venues', priority: '0.9', changefreq: 'daily' },
  { path: '/list-space', priority: '0.8', changefreq: 'monthly' },
  { path: '/how-it-works', priority: '0.7', changefreq: 'monthly' },

  // Guide and planning pages
  { path: '/venue-guide', priority: '0.9', changefreq: 'weekly' },
  { path: '/party-planning-guide', priority: '0.8', changefreq: 'monthly' },
  { path: '/nsw-party-planning', priority: '0.8', changefreq: 'monthly' },
  { path: '/nsw-noise-guide', priority: '0.7', changefreq: 'monthly' },

  // AI assistants
  { path: '/sales-assistant', priority: '0.7', changefreq: 'monthly' },
  { path: '/venue-assistant', priority: '0.7', changefreq: 'monthly' },

  // Content pages
  { path: '/blog', priority: '0.8', changefreq: 'weekly' },
  { path: '/faq', priority: '0.7', changefreq: 'monthly' },
  { path: '/contact', priority: '0.6', changefreq: 'monthly' },
  { path: '/safety', priority: '0.7', changefreq: 'monthly' },
  { path: '/help', priority: '0.6', changefreq: 'monthly' },

  // Legal pages
  { path: '/terms', priority: '0.4', changefreq: 'yearly' },
  { path: '/privacy', priority: '0.4', changefreq: 'yearly' },
  { path: '/cookies', priority: '0.4', changefreq: 'yearly' },

  // Auth pages (for SEO)
  { path: '/login', priority: '0.6', changefreq: 'monthly' },
  { path: '/signup', priority: '0.6', changefreq: 'monthly' },
  { path: '/sign-in', priority: '0.6', changefreq: 'monthly' },
  { path: '/sign-up', priority: '0.6', changefreq: 'monthly' },

  // Category pages
  { path: '/categories/garden', priority: '0.8', changefreq: 'weekly' },
  { path: '/categories/outdoor', priority: '0.8', changefreq: 'weekly' },
  { path: '/categories/waterfront', priority: '0.8', changefreq: 'weekly' },
  { path: '/categories/beachside', priority: '0.8', changefreq: 'weekly' },

  // Event type pages
  { path: '/event-types/birthday', priority: '0.8', changefreq: 'weekly' },
  { path: '/event-types/corporate', priority: '0.8', changefreq: 'weekly' },
  { path: '/event-types/wedding', priority: '0.8', changefreq: 'weekly' },
  { path: '/event-types/graduation', priority: '0.7', changefreq: 'weekly' },
  { path: '/event-types/engagement', priority: '0.7', changefreq: 'weekly' },
  { path: '/event-types/reunion', priority: '0.7', changefreq: 'weekly' },

  // Venue type pages
  { path: '/venue-types/cocktail-lounges', priority: '0.8', changefreq: 'weekly' },
  { path: '/venue-types/dance-venues', priority: '0.8', changefreq: 'weekly' },
  { path: '/venue-types/wedding-after-parties', priority: '0.7', changefreq: 'weekly' },

  // Gallery pages
  { path: '/gallery', priority: '0.7', changefreq: 'weekly' },
  { path: '/gallery/featured-venues', priority: '0.7', changefreq: 'weekly' },
  { path: '/gallery/sydney-venues', priority: '0.7', changefreq: 'weekly' },
  { path: '/gallery/event-inspiration', priority: '0.6', changefreq: 'weekly' },
  { path: '/gallery/videos', priority: '0.6', changefreq: 'weekly' },

  // Sitemap page
  { path: '/sitemap', priority: '0.5', changefreq: 'monthly' },

  // Location pages
  { path: '/locations/sydney-cbd', priority: '0.9', changefreq: 'weekly' },
  { path: '/locations/bondi-beach', priority: '0.9', changefreq: 'weekly' },
  { path: '/locations/parramatta', priority: '0.9', changefreq: 'weekly' },
  { path: '/locations/manly', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/newcastle', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/newtown', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/surry-hills', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/chatswood', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/cronulla', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/penrith', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/wollongong', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/blue-mountains', priority: '0.7', changefreq: 'weekly' },
  { path: '/locations/central-coast', priority: '0.7', changefreq: 'weekly' },
  { path: '/locations/southern-highlands', priority: '0.7', changefreq: 'weekly' },

  // Sydney sublocation pages
  { path: '/locations/sydney/inner-west', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/sydney/eastern-suburbs', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/sydney/north-shore', priority: '0.8', changefreq: 'weekly' },
  { path: '/locations/sydney/western-sydney', priority: '0.8', changefreq: 'weekly' },
];

// Blog posts for dynamic sitemap generation
const blogPosts = [
  {
    slug: 'ultimate-guide-party-planning-nsw-noise-laws',
    lastmod: '2024-01-15',
    priority: '0.7'
  },
  {
    slug: 'budget-friendly-venue-booking-tips-sydney',
    lastmod: '2024-01-10',
    priority: '0.6'
  },
  {
    slug: 'top-10-party-venues-sydney-2024',
    lastmod: '2024-01-05',
    priority: '0.7'
  },
  {
    slug: 'seasonal-party-planning-best-times-book-venues-nsw',
    lastmod: '2024-01-01',
    priority: '0.6'
  }
];

// Mock venues for sitemap (until database is fully populated)
const mockVenues = [
  'venue-001', 'venue-002', 'venue-003', 'venue-004', 'venue-005',
  'venue-006', 'venue-006b', 'venue-007', 'venue-008', 'venue-009b',
  'venue-010', 'venue-011', 'venue-012'
];

// Function to validate URLs before adding to sitemap
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Function to generate sitemap XML
async function generateSitemap() {
  console.log('Generating sitemap files...');
  let urlCount = 0;

  // Generate main sitemap with all URLs
  let mainSitemapContent = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n';
  mainSitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Add static routes
  for (const route of staticRoutes) {
    const url = `${baseUrl}${route.path}`;
    if (!isValidUrl(url)) {
      console.warn(`Invalid URL skipped: ${url}`);
      continue;
    }
    
    mainSitemapContent += '  <url>\n';
    mainSitemapContent += `    <loc>${url}</loc>\n`;
    mainSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    mainSitemapContent += `    <changefreq>${route.changefreq}</changefreq>\n`;
    mainSitemapContent += `    <priority>${route.priority}</priority>\n`;
    mainSitemapContent += '  </url>\n';
    urlCount++;
  }

  // Add blog post URLs
  console.log(`Adding ${blogPosts.length} blog post pages to sitemap...`);
  for (const post of blogPosts) {
    mainSitemapContent += '  <url>\n';
    mainSitemapContent += `    <loc>${baseUrl}/blog/${post.slug}</loc>\n`;
    mainSitemapContent += `    <lastmod>${post.lastmod}</lastmod>\n`;
    mainSitemapContent += '    <changefreq>monthly</changefreq>\n';
    mainSitemapContent += `    <priority>${post.priority}</priority>\n`;
    mainSitemapContent += '  </url>\n';
  }

  // Add mock venue URLs (until database is fully populated)
  console.log(`Adding ${mockVenues.length} mock venue pages to sitemap...`);
  for (const venueId of mockVenues) {
    mainSitemapContent += '  <url>\n';
    mainSitemapContent += `    <loc>${baseUrl}/venue/${venueId}</loc>\n`;
    mainSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    mainSitemapContent += '    <changefreq>weekly</changefreq>\n';
    mainSitemapContent += '    <priority>0.8</priority>\n';
    mainSitemapContent += '  </url>\n';
  }

  // Add dynamic routes from venues table
  try {
    // Check if venues table exists
    const { error: tableError } = await supabase
      .from('venues')
      .select('count(*)', { count: 'exact', head: true });

    if (tableError && tableError.code === '42P01') {
      console.log('Venues table does not exist yet. Using mock venues only.');
    } else {
      // If table exists, fetch venues
      const { data: venues, error } = await supabase
        .from('venues')
        .select('id, updated_at')
        .eq('is_published', true);

      if (error) {
        console.error('Error fetching venues from database:', error);
        console.log('Continuing with mock venues...');
      } else if (venues && venues.length > 0) {
        console.log(`Adding ${venues.length} database venue pages to sitemap...`);

        for (const venue of venues) {
          // Skip if this venue ID is already in mock venues
          if (mockVenues.includes(venue.id)) {
            continue;
          }

          const lastmod = venue.updated_at
            ? new Date(venue.updated_at).toISOString().split('T')[0]
            : currentDate;

          mainSitemapContent += '  <url>\n';
          mainSitemapContent += `    <loc>${baseUrl}/venue/${venue.id}</loc>\n`;
          mainSitemapContent += `    <lastmod>${lastmod}</lastmod>\n`;
          mainSitemapContent += '    <changefreq>weekly</changefreq>\n';
          mainSitemapContent += '    <priority>0.8</priority>\n';
          mainSitemapContent += '  </url>\n';
        }
      } else {
        console.log('No published venues found in database. Using mock venues only.');
      }
    }
  } catch (error) {
    console.error('Error fetching dynamic routes:', error);
    console.log('Continuing with static and mock content...');
  }

  mainSitemapContent += '</urlset>';

  // Create separate sitemaps for better organization

  // Generate blog sitemap
  let blogSitemapContent = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n';
  blogSitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  for (const post of blogPosts) {
    blogSitemapContent += '  <url>\n';
    blogSitemapContent += `    <loc>${baseUrl}/blog/${post.slug}</loc>\n`;
    blogSitemapContent += `    <lastmod>${post.lastmod}</lastmod>\n`;
    blogSitemapContent += '    <changefreq>monthly</changefreq>\n';
    blogSitemapContent += `    <priority>${post.priority}</priority>\n`;
    blogSitemapContent += '  </url>\n';
  }

  blogSitemapContent += '</urlset>';

  // Generate venues sitemap
  let venuesSitemapContent = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n';
  venuesSitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Add mock venues
  for (const venueId of mockVenues) {
    venuesSitemapContent += '  <url>\n';
    venuesSitemapContent += `    <loc>${baseUrl}/venue/${venueId}</loc>\n`;
    venuesSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    venuesSitemapContent += '    <changefreq>weekly</changefreq>\n';
    venuesSitemapContent += '    <priority>0.8</priority>\n';
    venuesSitemapContent += '  </url>\n';
  }

  venuesSitemapContent += '</urlset>';

  // Create a comprehensive single sitemap (more reliable for Google)
  let comprehensiveSitemapContent = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n';
  comprehensiveSitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Add all static routes
  for (const route of staticRoutes) {
    comprehensiveSitemapContent += '  <url>\n';
    comprehensiveSitemapContent += `    <loc>${baseUrl}${route.path}</loc>\n`;
    comprehensiveSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    comprehensiveSitemapContent += `    <changefreq>${route.changefreq}</changefreq>\n`;
    comprehensiveSitemapContent += `    <priority>${route.priority}</priority>\n`;
    comprehensiveSitemapContent += '  </url>\n';
  }

  // Add all blog posts
  for (const post of blogPosts) {
    comprehensiveSitemapContent += '  <url>\n';
    comprehensiveSitemapContent += `    <loc>${baseUrl}/blog/${post.slug}</loc>\n`;
    comprehensiveSitemapContent += `    <lastmod>${post.lastmod}</lastmod>\n`;
    comprehensiveSitemapContent += '    <changefreq>monthly</changefreq>\n';
    comprehensiveSitemapContent += `    <priority>${post.priority}</priority>\n`;
    comprehensiveSitemapContent += '  </url>\n';
  }

  // Add all mock venues
  for (const venueId of mockVenues) {
    comprehensiveSitemapContent += '  <url>\n';
    comprehensiveSitemapContent += `    <loc>${baseUrl}/venue/${venueId}</loc>\n`;
    comprehensiveSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    comprehensiveSitemapContent += '    <changefreq>weekly</changefreq>\n';
    comprehensiveSitemapContent += '    <priority>0.8</priority>\n';
    comprehensiveSitemapContent += '  </url>\n';
  }

  comprehensiveSitemapContent += '</urlset>';

  // Generate simple sitemap index that points to the comprehensive sitemap
  let indexSitemapContent = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n';
  indexSitemapContent += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  indexSitemapContent += '  <sitemap>\n';
  indexSitemapContent += `    <loc>${baseUrl}/sitemap_comprehensive.xml</loc>\n`;
  indexSitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
  indexSitemapContent += '  </sitemap>\n';
  indexSitemapContent += '</sitemapindex>';

  // Ensure public directory exists
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  // Write all sitemap files
  fs.writeFileSync(path.join(publicDir, 'sitemap_main.xml'), mainSitemapContent);
  fs.writeFileSync(path.join(publicDir, 'sitemap_blog.xml'), blogSitemapContent);
  fs.writeFileSync(path.join(publicDir, 'sitemap_venues.xml'), venuesSitemapContent);
  fs.writeFileSync(path.join(publicDir, 'sitemap_comprehensive.xml'), comprehensiveSitemapContent);
  fs.writeFileSync(path.join(publicDir, 'sitemap_index.xml'), indexSitemapContent);

  // For backward compatibility - use comprehensive sitemap as main sitemap
  fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), comprehensiveSitemapContent);

  console.log('Sitemap files generated successfully:');
  console.log('- public/sitemap_comprehensive.xml (single comprehensive sitemap)');
  console.log('- public/sitemap_index.xml (sitemap index)');
  console.log('- public/sitemap_main.xml (static pages only)');
  console.log('- public/sitemap_blog.xml (blog posts only)');
  console.log('- public/sitemap_venues.xml (venue pages only)');
  console.log('- public/sitemap.xml (comprehensive sitemap for compatibility)');

  // Count total URLs
  const totalUrls = staticRoutes.length + blogPosts.length + mockVenues.length;
  console.log(`\nTotal URLs in comprehensive sitemap: ${totalUrls}`);
  console.log('To submit to Google Search Console:');
  console.log(`1. Submit ${baseUrl}/sitemap.xml as primary sitemap`);
  console.log(`2. Alternative: Submit ${baseUrl}/sitemap_comprehensive.xml`);
  console.log('3. Wait 24-48 hours for Google to crawl');
  console.log('4. Check coverage report in Search Console');
}

// Execute the function
generateSitemap().catch(error => {
  console.error('Failed to generate sitemap:', error);
  process.exit(1);
});
