import NextAuth from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { SupabaseAdapter } from '@next-auth/supabase-adapter'
import { createClient } from '@supabase/supabase-js'
import jwt from 'jsonwebtoken'

// Create Supabase client with service role key for user management
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY // Service role key for admin operations
)

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    })
  ],
  adapter: SupabaseAdapter({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    secret: process.env.SUPABASE_SERVICE_ROLE_KEY,
  }),
  callbacks: {
    async signIn({ user, profile }) {
      try {
        // For Google OAuth, check if user type was stored (for host registration)
        // Note: In server-side callback, we can't access localStorage directly
        // The user type will be handled on the client side after successful auth

        // Sync user to user_profiles table with default guest role
        // The role will be updated on the client side if needed
        const { data, error } = await supabase
          .from('user_profiles')
          .upsert({
            id: user.id, // Use NextAuth user ID
            email: user.email,
            first_name: profile?.given_name || user.name?.split(' ')[0] || null,
            last_name: profile?.family_name || user.name?.split(' ').slice(1).join(' ') || null,
            avatar_url: user.image || null,
            role: 'guest', // Default role (will be updated client-side if host)
            is_host: false, // Default to not a host (will be updated client-side if host)
            clerk_id: null, // No longer using Clerk
            user_id: user.id, // Map to NextAuth user ID
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'email', // Update if email already exists
            ignoreDuplicates: false
          })

        if (error) {
          console.error('Error syncing user to user_profiles:', error)
          // Still allow sign in even if sync fails
        } else {
          console.log('User synced to user_profiles:', data)
        }

        return true
      } catch (error) {
        console.error('Error in signIn callback:', error)
        return true // Still allow sign in
      }
    },
    async session({ session, user }) {
      const signingSecret = process.env.SUPABASE_JWT_SECRET
      if (signingSecret) {
        const payload = {
          aud: 'authenticated',
          exp: Math.floor(new Date(session.expires).getTime() / 1000),
          sub: user.id,
          email: user.email,
          role: 'authenticated',
        }
        session.supabaseAccessToken = jwt.sign(payload, signingSecret)
      }

      // Add user ID to session for easy access
      session.userId = user.id

      return session
    },
  },
})
