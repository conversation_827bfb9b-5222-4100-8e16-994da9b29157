# Setting Up Clerk JWT Template for Supabase

This guide explains how to set up a JWT template in <PERSON> to authenticate with <PERSON><PERSON><PERSON>.

## Step 1: Get Your Supabase JWT Secret

1. Go to the [Supabase Dashboard](https://app.supabase.io/)
2. Select your project (with URL `fxqoowlruissctsgbljk.supabase.co`)
3. Navigate to Project Settings > API
4. Under the JWT Settings section, copy the value in the JWT Secret field

## Step 2: Create a JWT Template in Clerk

1. Go to the [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Navigate to JWT Templates in the left sidebar
3. Click the "New template" button
4. Select "Supabase" from the list of options

## Step 3: Configure the Template

1. Set the Name field to `supabase` (this exact name is required)
2. Signing algorithm should be HS256 (default)
3. Under Signing key, paste the Supabase JWT secret you copied in Step 1
4. Leave all other fields at their default settings
5. Click "Save" to create the template

## Step 4: Verify the Template

1. Go back to the JWT Templates page
2. Find your "supabase" template in the list
3. Click on it to view the details
4. Make sure the signing algorithm is HS256 and the signing key is set correctly

## Step 5: Test the Integration

1. Sign in to your HouseGoing application
2. Open the browser developer console
3. Look for messages related to Clerk-Supabase authentication
4. You should see successful authentication messages without errors

## Troubleshooting

If you encounter issues:

1. Make sure the JWT template name is exactly `supabase`
2. Verify that the signing key matches the Supabase JWT secret
3. Check that the signing algorithm is HS256
4. Look for error messages in the browser console
5. Try signing out and signing back in

## Additional Resources

- [Clerk Documentation: JWT Templates](https://clerk.com/docs/backend-requests/jwt-templates)
- [Supabase Documentation: Authentication](https://supabase.com/docs/guides/auth)
- [Clerk-Supabase Integration Guide](https://clerk.com/docs/integrations/databases/supabase)
