import React, { useState, useRef, useEffect } from 'react';
import { Send, X, MessageSquare, RefreshCw, Maximize2, Minimize2 } from 'lucide-react';

// Loading messages to show while waiting for a response
const loadingMessages = [
  "Finding perfect venues for you...",
  "Checking availability...",
  "Searching for party spots...",
  "Looking for the best match...",
  "Checking BYO options...",
  "Finding venues in your area...",
  "Calculating party potential...",
  "Hunting for hidden gems..."
];

export default function SalesAssistantChat() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [messages, setMessages] = useState([
    { role: 'assistant', content: "Hi there! I'm Homie from HouseGoing. How can I help you find the perfect venue for your event today?" }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [sessionId, setSessionId] = useState(null);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Get session ID from localStorage or create a new one
  useEffect(() => {
    const storedSessionId = localStorage.getItem('salesAssistantSessionId');
    if (storedSessionId) {
      setSessionId(storedSessionId);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const toggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: input }]);
    setIsLoading(true);

    // Show random loading message
    const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
    setLoadingMessage(randomMessage);

    try {
      // Call the unified AI chat API
      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: input,
          agentType: 'sales',
          sessionId: sessionId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();

      // Save session ID
      if (data.sessionId) {
        localStorage.setItem('salesAssistantSessionId', data.sessionId);
        setSessionId(data.sessionId);
      }

      // Add assistant response
      setMessages(prev => [...prev, { role: 'assistant', content: data.response }]);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => [...prev, { role: 'assistant', content: "Sorry, I'm having trouble connecting right now. Can you try again in a moment?" }]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
      setInput('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const resetConversation = () => {
    setMessages([
      { role: 'assistant', content: "Hi there! I'm Homie from HouseGoing. How can I help you find the perfect venue for your event today?" }
    ]);
    localStorage.removeItem('salesAssistantSessionId');
    setSessionId(null);
  };

  return (
    <>
      {/* Chat button */}
      <button
        onClick={toggleChat}
        className="fixed bottom-4 right-4 p-4 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-colors z-50"
        aria-label="Chat with sales assistant"
      >
        {isOpen ? <X size={24} /> : <MessageSquare size={24} />}
      </button>

      {/* Chat window */}
      {isOpen && (
        <div className={`fixed z-50 bg-white rounded-lg shadow-xl flex flex-col overflow-hidden border border-gray-200 transition-all duration-300 ${isMaximized
          ? 'inset-4 md:inset-10'
          : 'bottom-20 right-4 w-80 sm:w-96 h-[32rem]'}`}>
          {/* Header */}
          <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium">Chat with Homie</h3>
              <p className="text-xs text-purple-100">HouseGoing Venue Finder</p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={resetConversation}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Reset conversation"
              >
                <RefreshCw size={16} />
              </button>
              <button
                onClick={toggleMaximize}
                className="text-white hover:text-purple-200 p-1 rounded"
                title={isMaximized ? "Minimize" : "Maximize"}
              >
                {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
              </button>
              <button
                onClick={toggleChat}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Close"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`mb-4 ${message.role === 'user' ? 'text-right' : ''}`}
              >
                <div
                  className={`inline-block max-w-[85%] p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-purple-600 text-white rounded-tr-none'
                      : 'bg-white border border-gray-200 rounded-tl-none'
                  }`}
                >
                  {message.content}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="mb-4">
                <div className="inline-block max-w-[85%] p-3 rounded-lg bg-white border border-gray-200 rounded-tl-none">
                  <p className="text-gray-500 mb-1">{loadingMessage}</p>
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-purple-300 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-100"></div>
                    <div className="w-2 h-2 bg-purple-700 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t p-4 bg-white">
            <div className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                disabled={isLoading}
              />
              <button
                onClick={sendMessage}
                disabled={isLoading || !input.trim()}
                className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
