/**
 * Edit Property Page
 * 
 * Allows owners to edit rejected properties and resubmit for approval
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '../../hooks/useClerkCompat';
import { 
  ArrowLeft, 
  AlertTriangle, 
  CheckCircle, 
  Upload,
  X,
  Save,
  Send
} from 'lucide-react';

interface PropertyData {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  status: string;
  rejection_reason?: string;
  admin_notes?: string;
  images?: string[];
  amenities?: string[];
}

export default function EditProperty() {
  const { propertyId } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [property, setProperty] = useState<PropertyData | null>(null);
  const [formData, setFormData] = useState<Partial<PropertyData>>({});
  const [newImages, setNewImages] = useState<string[]>([]);

  // Load property data
  useEffect(() => {
    const loadProperty = async () => {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - in production this would come from database
      const mockProperty: PropertyData = {
        id: propertyId || '',
        name: 'Urban Loft Space',
        address: '321 King Street, Newtown NSW 2042',
        type: 'Loft',
        description: 'Modern urban loft with contemporary design and city views.',
        maxGuests: 80,
        price: 180,
        status: 'rejected',
        rejection_reason: 'Property photos are unclear and missing required documents',
        admin_notes: 'Please provide clearer photos of all rooms and upload your insurance certificate. The property has great potential!',
        images: [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400',
          'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400'
        ],
        amenities: ['City Views', 'Modern Kitchen', 'Sound System', 'Elevator', 'Parking']
      };
      
      setProperty(mockProperty);
      setFormData(mockProperty);
      setLoading(false);
    };

    loadProperty();
  }, [propertyId]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      // In production, upload to Cloudinary and get URLs
      // For now, simulate with placeholder URLs
      const newImageUrls = Array.from(files).map((file, index) => 
        `https://images.unsplash.com/photo-${Date.now()}-${index}?w=400`
      );
      setNewImages(prev => [...prev, ...newImageUrls]);
    }
  };

  const removeImage = (index: number, isNew: boolean = false) => {
    if (isNew) {
      setNewImages(prev => prev.filter((_, i) => i !== index));
    } else {
      setFormData(prev => ({
        ...prev,
        images: prev.images?.filter((_, i) => i !== index)
      }));
    }
  };

  const handleSaveDraft = async () => {
    setSaving(true);
    
    // Simulate save
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    alert('✅ Changes saved as draft!');
    setSaving(false);
  };

  const handleResubmit = async () => {
    setSaving(true);
    
    try {
      // Simulate resubmission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In production, update the property status to 'pending' and send notification
      alert('✅ Property resubmitted for review! You will receive an email notification once it has been reviewed.');
      
      // Navigate back to dashboard
      navigate('/owner/dashboard');
      
    } catch (error) {
      alert('❌ Error resubmitting property. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            <p className="ml-3 text-gray-600">Loading property details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-4xl mx-auto text-center py-12">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-6">The property you're looking for doesn't exist or you don't have permission to edit it.</p>
          <button
            onClick={() => navigate('/owner/dashboard')}
            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  const allImages = [...(formData.images || []), ...newImages];

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/owner/dashboard')}
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to My Properties
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Edit Property</h1>
          <p className="text-gray-600">Make changes based on admin feedback and resubmit for approval</p>
        </div>

        {/* Admin Feedback */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-red-800 mb-2">Admin Feedback</h3>
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-red-700">Rejection Reason:</p>
                  <p className="text-sm text-red-600">{property.rejection_reason}</p>
                </div>
                {property.admin_notes && (
                  <div>
                    <p className="text-sm font-medium text-red-700">Additional Notes:</p>
                    <p className="text-sm text-red-600">{property.admin_notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Edit Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Property Name</label>
                  <input
                    type="text"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                  <select
                    value={formData.type || ''}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="House">House</option>
                    <option value="Apartment">Apartment</option>
                    <option value="Villa">Villa</option>
                    <option value="Loft">Loft</option>
                    <option value="Warehouse">Warehouse</option>
                    <option value="Rooftop">Rooftop</option>
                    <option value="Backyard">Backyard</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
              <input
                type="text"
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Describe your property in detail..."
              />
            </div>

            {/* Capacity and Price */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Maximum Guests</label>
                <input
                  type="number"
                  value={formData.maxGuests || ''}
                  onChange={(e) => handleInputChange('maxGuests', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price per Hour ($)</label>
                <input
                  type="number"
                  value={formData.price || ''}
                  onChange={(e) => handleInputChange('price', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Images */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Property Images</label>
              <p className="text-sm text-gray-500 mb-4">Upload clear, high-quality photos of your property</p>
              
              {/* Current Images */}
              {allImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
                  {allImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`Property ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        onClick={() => removeImage(index, index >= (formData.images?.length || 0))}
                        className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Upload Button */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">Upload additional images</p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg cursor-pointer transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  Choose Images
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
              <button
                onClick={handleSaveDraft}
                disabled={saving}
                className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                <Save className="w-4 h-4" />
                {saving ? 'Saving...' : 'Save Draft'}
              </button>
              
              <button
                onClick={handleResubmit}
                disabled={saving}
                className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg font-medium transition-colors"
              >
                <Send className="w-4 h-4" />
                {saving ? 'Resubmitting...' : 'Resubmit for Review'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
