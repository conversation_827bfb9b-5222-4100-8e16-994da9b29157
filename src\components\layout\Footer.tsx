import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Instagram, Twitter, Mail } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="text-xl font-bold text-purple-600 mb-2">HouseGoing</div>
            <p className="text-sm font-medium text-purple-600 mb-3">
              "The only house we going, is a party house"
            </p>
            <p className="text-gray-600 text-xs leading-relaxed mb-4">
              Australia's premier party venue rental platform.
            </p>

            {/* Social Links */}
            <div className="flex space-x-3">
              <a href="https://facebook.com/housegoing" className="text-gray-500 hover:text-purple-600 transition-colors">
                <Facebook className="h-4 w-4" />
              </a>
              <a href="https://instagram.com/housegoing" className="text-gray-500 hover:text-purple-600 transition-colors">
                <Instagram className="h-4 w-4" />
              </a>
              <a href="https://twitter.com/housegoing" className="text-gray-500 hover:text-purple-600 transition-colors">
                <Twitter className="h-4 w-4" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Quick Links</h4>
            <ul className="space-y-1">
              <li><Link to="/find-venues" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Find Venues</Link></li>
              <li><Link to="/venue-guide" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Venue Guide</Link></li>
              <li><Link to="/how-it-works" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">How It Works</Link></li>
              <li><Link to="/nsw-party-planning" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">NSW Planning</Link></li>
              <li><Link to="/host/submit-property" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">List Your Venue</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Support</h4>
            <ul className="space-y-1">
              <li><Link to="/help" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Contact Us</Link></li>
              <li><Link to="/safety" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Safety Information</Link></li>
              <li><Link to="/terms" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Terms & Conditions</Link></li>
              <li><Link to="/privacy" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Privacy Policy</Link></li>
            </ul>
          </div>

          {/* Stay Connected */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Stay Connected</h4>
            <div className="flex items-center text-gray-600">
              <Mail className="h-3 w-3 mr-1" />
              <a href="mailto:<EMAIL>" className="text-xs hover:text-purple-600 transition-colors">
                <EMAIL>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 mt-6 pt-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-600 text-xs">
              © 2025 HouseGoing Pty Ltd. All rights reserved. ABN **************
            </div>
            <div className="flex space-x-4 mt-2 md:mt-0">
              <Link to="/terms" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Terms</Link>
              <Link to="/privacy" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Privacy</Link>
              <Link to="/cookies" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Cookies</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
