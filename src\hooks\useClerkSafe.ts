/**
 * Development-Safe Clerk Hooks
 * Provides fallback implementations for Clerk hooks when <PERSON><PERSON>rovider is not available
 */

import { useEffect, useState } from 'react';

// Check if we're in development mode
function isDevelopmentMode(): boolean {
  return typeof window !== 'undefined' &&
         (window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          window.location.hostname.includes('local'));
}

// Mock user object for development
const mockUser = {
  id: 'dev-user-123',
  primaryEmailAddress: {
    emailAddress: '<EMAIL>'
  },
  firstName: 'Dev',
  lastName: 'Admin',
  fullName: 'Dev Admin',
  emailAddresses: [{
    emailAddress: '<EMAIL>'
  }]
};

// Mock auth object for development
const mockAuth = {
  isSignedIn: true,
  isLoaded: true,
  userId: 'dev-user-123',
  getToken: async () => 'mock-token'
};

// Safe useUser hook
export function useUserSafe() {
  const [user, setUser] = useState(isDevelopmentMode() ? mockUser : null);
  const [isLoaded, setIsLoaded] = useState(isDevelopmentMode());

  useEffect(() => {
    if (isDevelopmentMode()) {
      console.log('🔧 useUserSafe: Development mode - using mock user');
      setUser(mockUser);
      setIsLoaded(true);
    } else {
      // In production, try to use real Clerk
      try {
        // Dynamic import to avoid errors when Clerk is not available
        // Use Supabase auth instead
        console.log('Production mode: Using Supabase auth');
        setIsLoaded(true);
      } catch (error) {
        console.warn('Clerk hooks not available:', error);
        setIsLoaded(true);
      }
    }
  }, []);

  return { user, isLoaded, isSignedIn: !!user };
}

// Safe useAuth hook
export function useAuthSafe() {
  const [auth, setAuth] = useState(isDevelopmentMode() ? mockAuth : {
    isSignedIn: false,
    isLoaded: false,
    userId: null,
    getToken: async () => null
  });

  useEffect(() => {
    if (isDevelopmentMode()) {
      console.log('🔧 useAuthSafe: Development mode - using mock auth');
      setAuth(mockAuth);
    } else {
      // In production, try to use real Clerk
      try {
        // Use Supabase auth instead
        console.log('Production mode: Using Supabase auth');
        setAuth({ ...auth, isLoaded: true });
      } catch (error) {
        console.warn('Clerk hooks not available:', error);
        setAuth({ ...auth, isLoaded: true });
      }
    }
  }, []);

  return auth;
}

// Safe useClerk hook
export function useClerkSafe() {
  const [clerk, setClerk] = useState(isDevelopmentMode() ? {
    signOut: () => {
      console.log('🔧 Development mode: Mock sign out');
      window.location.href = '/';
    },
    loaded: true
  } : null);

  useEffect(() => {
    if (isDevelopmentMode()) {
      console.log('🔧 useClerkSafe: Development mode - using mock clerk');
    } else {
      try {
        // Use Supabase auth instead
        console.log('Production mode: Using Supabase auth');
      } catch (error) {
        console.warn('Clerk hooks not available:', error);
      }
    }
  }, []);

  return clerk;
}
