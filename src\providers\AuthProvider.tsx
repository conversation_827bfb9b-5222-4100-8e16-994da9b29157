import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut as nextAuthSignOut } from 'next-auth/react';
import { supabase } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null; // NextAuth user object
  session: any | null; // NextAuth session object
  isLoading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  userProfile: any | null; // User profile from Supabase
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);

  const isLoading = status === 'loading';
  const isAuthenticated = !!session?.user;
  const user = session?.user || null;

  // Fetch user profile from Supabase when session changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (session?.user?.email) {
        try {
          const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('email', session.user.email!)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('Error fetching user profile:', error);
            setError(error.message);
          } else {
            setUserProfile(data);
            console.log('✅ User profile loaded:', data?.email);
          }
        } catch (error) {
          console.error('Error in fetchUserProfile:', error);
          setError(error instanceof Error ? error.message : 'Profile fetch failed');
        }
      } else {
        setUserProfile(null);
      }
    };

    fetchUserProfile();
  }, [session?.user?.email]);

  const signInWithGoogle = async () => {
    try {
      setError(null);
      await signIn('google', {
        callbackUrl: window.location.origin,
        redirect: true
      });
    } catch (error) {
      console.error('Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);
      await nextAuthSignOut({
        callbackUrl: window.location.origin,
        redirect: true
      });
      console.log('✅ Successfully signed out');
    } catch (error) {
      console.error('Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);
      // NextAuth.js handles session refresh automatically
      // We can trigger a re-fetch of the user profile
      if (session?.user?.email) {
        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('email', session.user.email!)
          .single();

        if (!error) {
          setUserProfile(data);
        }
      }
      console.log('✅ Session refreshed');
    } catch (error) {
      console.error('Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    signInWithGoogle,
    signOut,
    refreshAuth,
    userProfile
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
