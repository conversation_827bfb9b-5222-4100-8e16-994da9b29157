import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { supabase } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  isLoading: boolean;
  error: string | null;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = status === 'authenticated';
  const user = session?.user || null;

  // Set up Supabase session when NextAuth session changes
  useEffect(() => {
    const setupSupabaseSession = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (isAuthenticated && session?.supabaseAccessToken) {
          console.log('✅ NextAuth session found, setting up Supabase session...');
          await supabase.auth.setSession({
            access_token: session.supabaseAccessToken,
            refresh_token: ''
          });
          console.log('✅ Supabase session set with NextAuth token');
        } else {
          console.log('🔄 No active session, clearing Supabase session...');
          await supabase.auth.signOut();
        }
      } catch (error) {
        console.error('❌ Error setting up Supabase session:', error);
        setError(error instanceof Error ? error.message : 'Authentication error');
      } finally {
        setIsLoading(false);
      }
    };

    setupSupabaseSession();
  }, [isAuthenticated, session]);

  const signOut = async () => {
    await useSession().signOut({ redirect: false });
    await supabase.auth.signOut();
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    isLoading,
    error,
    signOut
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export for backward compatibility
export { default as AuthProvider } from './AuthProvider';
