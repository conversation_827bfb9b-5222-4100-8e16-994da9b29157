import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react';
import { supabase, createSupabaseClientWithClerkToken } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  session: any | null;
  isLoading: boolean;
  error: string | null;
  authProvider: 'clerk';
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  customerData: any | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut, getToken } = useClerkAuth();

  const [error, setError] = useState<string | null>(null);
  const [customerData, setCustomerData] = useState<any | null>(null);

  const isLoading = !clerkLoaded;
  const isAuthenticated = !!isSignedIn;
  const authProvider = 'clerk' as const;

  // Use Clerk user data
  const user = clerkUser;
  const session = clerkUser;

  // Helper function to get Supabase client with Clerk token
  const getSupabaseWithClerkToken = async () => {
    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        return createSupabaseClientWithClerkToken(token);
      }
    } catch (error) {
      console.warn('Could not get Clerk token, using default Supabase client:', error);
    }
    return supabase; // Fallback to default client
  };

  // Sync Clerk user to Supabase when user changes
  useEffect(() => {
    const syncUserToSupabase = async () => {
      if (clerkUser && clerkUser.primaryEmailAddress?.emailAddress) {
        try {
          const supabaseClient = await getSupabaseWithClerkToken();

          // Check if user exists in user_profiles table
          const { data: existingUser, error: fetchError } = await supabaseClient
            .from('user_profiles')
            .select('*')
            .eq('email', clerkUser.primaryEmailAddress.emailAddress)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('❌ Error checking existing user:', fetchError);
            return;
          }

          if (!existingUser) {
            // Create new user profile record
            const { data: newUser, error: insertError } = await supabaseClient
              .from('user_profiles')
              .insert({
                id: clerkUser.id,
                email: clerkUser.primaryEmailAddress.emailAddress,
                first_name: clerkUser.firstName || '',
                last_name: clerkUser.lastName || '',
                role: 'customer',
                is_host: false,
              })
              .select()
              .single();

            if (insertError) {
              console.error('❌ Error creating user profile:', insertError);
              setError(insertError.message);
            } else {
              setCustomerData(newUser);
              console.log('✅ New user profile created:', newUser.email);
            }
          } else {
            setCustomerData(existingUser);
            console.log('✅ Existing user profile loaded:', existingUser.email);
          }
        } catch (error) {
          console.error('❌ Error syncing user to Supabase:', error);
          setError(error instanceof Error ? error.message : 'User sync failed');
        }
      } else {
        setCustomerData(null);
      }
    };

    if (clerkLoaded) {
      syncUserToSupabase();
    }
  }, [clerkUser, clerkLoaded]);

  // Clerk auth methods - these will redirect to Clerk's hosted pages
  const signInWithGoogle = async () => {
    try {
      setError(null);
      // Redirect to Clerk's Google OAuth flow
      window.location.href = '/sign-in';
    } catch (error) {
      console.error('❌ Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Google sign-in failed');
    }
  };

  const signInWithEmail = async (email: string) => {
    try {
      setError(null);
      // Redirect to Clerk's sign-in page with email pre-filled
      window.location.href = `/sign-in?email=${encodeURIComponent(email)}`;
    } catch (error) {
      console.error('❌ Error in signInWithEmail:', error);
      setError(error instanceof Error ? error.message : 'Email sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);

      // Use Clerk's sign out
      await clerkSignOut();

      // Clear customer data
      setCustomerData(null);
      setError(null);

      console.log('✅ Signed out from Clerk');
      window.location.href = '/';
    } catch (error) {
      console.error('❌ Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);

      // Refresh customer data from Supabase
      if (clerkUser?.primaryEmailAddress?.emailAddress) {
        const supabaseClient = await getSupabaseWithClerkToken();
        const { data, error } = await supabaseClient
          .from('user_profiles')
          .select('*')
          .eq('email', clerkUser.primaryEmailAddress.emailAddress)
          .single();

        if (!error) {
          setCustomerData(data);
        }
      }
      console.log('✅ Auth refreshed');
    } catch (error) {
      console.error('❌ Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    authProvider,
    signInWithGoogle,
    signInWithEmail,
    signOut,
    refreshAuth,
    customerData
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
