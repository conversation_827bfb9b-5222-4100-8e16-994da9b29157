import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase-client';
import type { User, Session } from '@supabase/supabase-js';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user && !!session;

  // Initialize auth state and listen for changes
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          console.log('✅ Initial session loaded:', !!session);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (mounted) {
          setError(error instanceof Error ? error.message : 'Authentication error');
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, !!session);

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          setError(null);

          if (event === 'SIGNED_OUT') {
            console.log('👋 User signed out');
          } else if (event === 'SIGNED_IN') {
            console.log('👋 User signed in:', session?.user?.email);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signInWithGoogle = async () => {
    try {
      setError(null);
      setIsLoading(true);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('Google sign-in error:', error);
        setError(error.message);
      }
    } catch (error) {
      console.error('Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Sign-in failed');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setError(null);
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Sign-out error:', error);
        setError(error.message);
      } else {
        console.log('✅ Successfully signed out');
      }
    } catch (error) {
      console.error('Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);
      const { data: { session }, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Refresh session error:', error);
        setError(error.message);
      } else {
        console.log('✅ Session refreshed');
        setSession(session);
        setUser(session?.user ?? null);
      }
    } catch (error) {
      console.error('Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    signInWithGoogle,
    signOut,
    refreshAuth
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
