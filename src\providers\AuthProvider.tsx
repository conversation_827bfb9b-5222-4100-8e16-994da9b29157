import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react';
import { createClerkSupabaseClient } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  session: any | null;
  isLoading: boolean;
  error: string | null;
  authProvider: 'clerk';
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  customerData: any | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

e'use server'

import { createServerSupabaseClient } from './client'

export async function addTask(name: string) {
  const client = createServerSupabaseClient()

  try {
    const response = await client.from('tasks').insert({
      name,
    })

    console.log('Task successfully added!', response)
  } catch (error: any) {
    console.error('Error adding task:', error.message)
    throw new Error('Failed to add task')
  }
}Integrations
Supabase
Integrate Supabase with Clerk
Before you start
Set up a Clerk application
Integrate the appropriate Clerk SDK in your local project
Example repository
Supabase, Next.js, and Clerk Demo
Integrating Supabase with Clerk gives you the benefits of using a Supabase database while leveraging Clerk's authentication, prebuilt components, and webhooks. To get the most out of Supabase with Clerk, you must implement custom Row Level Security (RLS) policies.

RLS works by validating database queries according to the restrictions defined in the RLS policies applied to the table. This guide will show you how to create RLS policies that restrict access to data based on the user's Clerk ID. This way, users can only access data that belongs to them. To set this up, you will:

Create a user_id column that defaults to the Clerk user's ID when new records are created.
Create policies to restrict what data can be read and inserted.
Use the Clerk Supabase integration helper in your code to authenticate with Supabase and execute queries.
This guide will have you create a new table in your Supabase project, but you can apply these concepts to your existing tables as well.

Tip

This integration restricts what data authenticated users can access in the database, but does not synchronize user records between Clerk and Supabase. To send additional data from Clerk to your Supabase database, use webhooks.

Set up Clerk as a Supabase third-party auth provider
For your Clerk session token to work with Supabase, you need to set up Clerk as a third-party auth provider in Supabase.

In the Clerk Dashboard, navigate to the Supabase integration setup.
Select your configuration options, and then select Activate Supabase integration. This will reveal the Clerk domain for your Clerk instance.
Save the Clerk domain.
In the Supabase Dashboard, navigate to Authentication > Sign In / Up.
Select Add provider and select Clerk from the list of providers.
Paste the Clerk domain you copied from the Clerk Dashboard.
Set up RLS policies using Clerk session token data
You can access Clerk session token data in Supabase using the built-in auth.jwt() function. This is necessary to create custom RLS policies to restrict database access based on the requesting user.

Create a table to enable RLS on. Open Supabase's SQL editor and run the following queries. This example creates a tasks table with a user_id column that maps to a Clerk user ID.

-- Create a "tasks" table with a user_id column that maps to a Clerk user ID
create table tasks(
  id serial primary key,
  name text not null,
  user_id text not null default auth.jwt()->>'sub'
);

-- Enable RLS on the table
alter table "tasks" enable row level security;
Create two policies that restrict access to the tasks table based on the requesting user's Clerk ID. These policies allow users to create tasks for themselves and view their own tasks.

create policy "User can view their own tasks"
on "public"."tasks"
for select
to authenticated
using (
((select auth.jwt()->>'sub') = (user_id)::text)
);

create policy "Users must insert their own tasks"
on "public"."tasks"
as permissive
for insert
to authenticated
with check (
((select auth.jwt()->>'sub') = (user_id)::text)
);
Install the Supabase client library
Run the following command to add the Supabase client library to your application.

npm
yarn
pnpm
bun
terminal

npm i @supabase/supabase-js
Set up your environment variables
In the Supabase dashboard, navigate to Project Settings > Data API.
Add the Project URL to your .env file as SUPABASE_URL.
In the Project API keys section, add the value beside anon public to your .env file as SUPABASE_KEY.
Important

The NEXT_PUBLIC_ prefix is required for environment variables that are used in the client-side code, so add this prefix to these variables.

Fetch Supabase data in your code
In your app's page.tsx, paste the following code. This example shows the list of tasks for the user and allows the user to add new tasks. The createClerkSupabaseClient() function uses Supabase's createClient() method to initialize a new Supabase client with access to Clerk's session token.

Client-side rendering
Server-side rendering
The following example uses the Next.js SDK to demonstrate how to integrate Supabase with Clerk in a server-side rendered application.

The createServerSupabaseClient() function is stored in a separate file so that it can be re-used in multiple places, such as within page.tsx or a Server Action file. This function uses the auth().getToken() method to pass the Clerk session token to the Supabase client.

app/ssr/client.ts

import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'

export function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_KEY!,
    {
      async accessToken() {
        return (await auth()).getToken()
      },
    },
  )
}
The following files render the /ssr page and handle the "Add task" form submission. Use the following tabs to view the code for each page.

page.tsx
actions.ts
AddTaskForm.tsx
app/ssr/AddTaskForm.tsx

'use client'
import React, { useState } from 'react'
import { addTask } from './actions'
import { useRouter } from 'next/navigation'

function AddTaskForm() {
  const [taskName, setTaskName] = useState('')
  const router = useRouter()

  async function onSubmit() {
    await addTask(taskName)
    setTaskName('')
    router.refresh()
  }

  return (
    <form action={onSubmit}>
      <input
        autoFocus
        type="text"
        name="name"
        placeholder="Enter new task"
        onChange={(e) => setTaskName(e.target.value)}
        value={taskName}
      />
      <button type="submit">Add</button>
    </form>
  )
}
export default AddTaskForm
Test your integration
Run your project and sign in. Test creating and viewing tasks. Sign out and sign in as a different user, and repeat.

If you have the same tasks across multiple accounts, double check that RLS is enabled, or that the RLS policies were properly created. Check the table in the Supabase dashboard. You should see all the tasks between both users, but with differing values in the user_id column.

What does the Clerk Supabase integration do?
Requests to Supabase's APIs require that authenticated users have a "role": "authenticated" JWT claim. When enabled, the Clerk Supabase integration adds this claim to your instance's generated session tokens.

Supabase JWT template deprecation
As of April 1st, 2025, the Clerk Supabase JWT template is considered deprecated. Going forward, the native Supabase integration is the recommended way to integrate Clerk with Supabase. The native integration has a number of benefits over the JWT template:

No need to fetch a new token for each Supabase request
No need to share your Supabase JWT secret key with Clerk
For more information on the benefits of the native integration, see Supabase's documentation on third-party auth providers.

Feedback
What did you think of this content?xport function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut, getToken } = useClerkAuth();

  const [error, setError] = useState<string | null>(null);
  const [customerData, setCustomerData] = useState<any | null>(null);

  const isLoading = !clerkLoaded;
  const isAuthenticated = !!isSignedIn;
  const authProvider = 'clerk' as const;

  // Use Clerk user data
  const user = clerkUser;
  const session = clerkUser;

  // Create Supabase client with Clerk session token (following official pattern)
  const clerkSupabase = createClerkSupabaseClient(async () => {
    try {
      return await getToken();
    } catch (error) {
      console.warn('Could not get Clerk token:', error);
      return null;
    }
  });

  // Sync Clerk user to Supabase when user changes
  useEffect(() => {
    const syncUserToSupabase = async () => {
      if (clerkUser && clerkUser.primaryEmailAddress?.emailAddress) {
        try {
          // Check if user exists in user_profiles table using Clerk-authenticated Supabase client
          const { data: existingUser, error: fetchError } = await clerkSupabase
            .from('user_profiles')
            .select('*')
            .eq('email', clerkUser.primaryEmailAddress.emailAddress)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('❌ Error checking existing user:', fetchError);
            return;
          }

          if (!existingUser) {
            // Create new user profile record
            const { data: newUser, error: insertError } = await clerkSupabase
              .from('user_profiles')
              .insert({
                id: clerkUser.id,
                email: clerkUser.primaryEmailAddress.emailAddress,
                first_name: clerkUser.firstName || '',
                last_name: clerkUser.lastName || '',
                role: 'customer',
                is_host: false,
              })
              .select()
              .single();

            if (insertError) {
              console.error('❌ Error creating user profile:', insertError);
              setError(insertError.message);
            } else {
              setCustomerData(newUser);
              console.log('✅ New user profile created:', newUser.email);
            }
          } else {
            setCustomerData(existingUser);
            console.log('✅ Existing user profile loaded:', existingUser.email);
          }
        } catch (error) {
          console.error('❌ Error syncing user to Supabase:', error);
          setError(error instanceof Error ? error.message : 'User sync failed');
        }
      } else {
        setCustomerData(null);
      }
    };

    if (clerkLoaded) {
      syncUserToSupabase();
    }
  }, [clerkUser, clerkLoaded]);

  // Clerk auth methods - these will redirect to Clerk's hosted pages
  const signInWithGoogle = async () => {
    try {
      setError(null);
      // Redirect to Clerk's Google OAuth flow
      window.location.href = '/sign-in';
    } catch (error) {
      console.error('❌ Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Google sign-in failed');
    }
  };

  const signInWithEmail = async (email: string) => {
    try {
      setError(null);
      // Redirect to Clerk's sign-in page with email pre-filled
      window.location.href = `/sign-in?email=${encodeURIComponent(email)}`;
    } catch (error) {
      console.error('❌ Error in signInWithEmail:', error);
      setError(error instanceof Error ? error.message : 'Email sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);

      // Use Clerk's sign out
      await clerkSignOut();

      // Clear customer data
      setCustomerData(null);
      setError(null);

      console.log('✅ Signed out from Clerk');
      window.location.href = '/';
    } catch (error) {
      console.error('❌ Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);

      // Refresh customer data from Supabase
      if (clerkUser?.primaryEmailAddress?.emailAddress) {
        const { data, error } = await clerkSupabase
          .from('user_profiles')
          .select('*')
          .eq('email', clerkUser.primaryEmailAddress.emailAddress)
          .single();

        if (!error) {
          setCustomerData(data);
        }
      }
      console.log('✅ Auth refreshed');
    } catch (error) {
      console.error('❌ Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    authProvider,
    signInWithGoogle,
    signInWithEmail,
    signOut,
    refreshAuth,
    customerData
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
