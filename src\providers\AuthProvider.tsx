import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  userProfile: any | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!session;

  // Listen for Supabase auth changes
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting Supabase session:', error);
        } else if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          console.log('✅ Supabase session loaded:', !!session);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for Supabase auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Supabase auth state changed:', event, !!session);

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          setError(null);

          if (event === 'SIGNED_OUT') {
            console.log('👋 User signed out');
          } else if (event === 'SIGNED_IN') {
            console.log('👋 User signed in:', session?.user?.email);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Fetch user profile from Supabase when session changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      const userEmail = user?.email;
      if (userEmail) {
        try {
          const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('email', userEmail)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('Error fetching user profile:', error);
            setError(error.message);
          } else {
            setUserProfile(data);
            console.log('✅ User profile loaded:', data?.email);
          }
        } catch (error) {
          console.error('Error in fetchUserProfile:', error);
          setError(error instanceof Error ? error.message : 'Profile fetch failed');
        }
      } else {
        setUserProfile(null);
      }
    };

    fetchUserProfile();
  }, [user?.email]);

  const signInWithGoogle = async () => {
    try {
      setError(null);
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setError(error.message);
        console.error('Error in Google sign-in:', error);
      }
    } catch (error) {
      console.error('Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Google sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);
      await supabase.auth.signOut();
      console.log('✅ Successfully signed out');
      window.location.href = '/';
    } catch (error) {
      console.error('Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);
      // NextAuth.js handles session refresh automatically
      // We can trigger a re-fetch of the user profile
      if (session?.user?.email) {
        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('email', session.user.email!)
          .single();

        if (!error) {
          setUserProfile(data);
        }
      }
      console.log('✅ Session refreshed');
    } catch (error) {
      console.error('Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    signInWithGoogle,
    signOut,
    refreshAuth,
    userProfile
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
