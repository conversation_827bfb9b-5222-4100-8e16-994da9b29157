import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut as nextAuthSignOut } from 'next-auth/react';
import { supabase } from '../lib/supabase-client';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null; // NextAuth user object
  session: any | null; // NextAuth session object
  isLoading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  userProfile: any | null; // User profile from Supabase
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: nextAuthSession, status } = useSession();
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<any | null>(null);
  const [supabaseSession, setSupabaseSession] = useState<any | null>(null);

  const isNextAuthLoading = status === 'loading';
  const [isSupabaseLoading, setIsSupabaseLoading] = useState(true);

  // Determine which auth system is active
  const isNextAuthActive = !!nextAuthSession?.user;
  const isSupabaseActive = !!supabaseUser && !!supabaseSession;

  const isLoading = isNextAuthLoading || isSupabaseLoading;
  const isAuthenticated = isNextAuthActive || isSupabaseActive;

  // Use NextAuth user if available, otherwise Supabase user
  const user = nextAuthSession?.user || supabaseUser;
  const session = nextAuthSession || supabaseSession;

  // Listen for Supabase auth changes (for email sign-ins)
  useEffect(() => {
    let mounted = true;

    // Get initial Supabase session
    const getInitialSupabaseSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting Supabase session:', error);
        } else if (mounted) {
          setSupabaseSession(session);
          setSupabaseUser(session?.user ?? null);
          console.log('✅ Supabase session loaded:', !!session);
        }
      } catch (error) {
        console.error('Error in getInitialSupabaseSession:', error);
      } finally {
        if (mounted) {
          setIsSupabaseLoading(false);
        }
      }
    };

    getInitialSupabaseSession();

    // Listen for Supabase auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Supabase auth state changed:', event, !!session);

        if (mounted) {
          setSupabaseSession(session);
          setSupabaseUser(session?.user ?? null);
          setError(null);

          if (event === 'SIGNED_OUT') {
            console.log('👋 User signed out from Supabase');
          } else if (event === 'SIGNED_IN') {
            console.log('👋 User signed in via Supabase:', session?.user?.email);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Fetch user profile from Supabase when session changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      const userEmail = user?.email;
      if (userEmail) {
        try {
          // Check if there's a pending user type update (for Google OAuth host registration)
          const pendingUserType = localStorage.getItem('pending_user_type');

          if (pendingUserType && isNextAuthActive) {
            // Update user role if they signed up as a host via Google
            const { error: updateError } = await supabase
              .from('user_profiles')
              .update({
                role: pendingUserType,
                is_host: pendingUserType === 'host',
                updated_at: new Date().toISOString()
              })
              .eq('email', userEmail);

            if (updateError) {
              console.error('Error updating user role:', updateError);
            } else {
              console.log(`✅ User role updated to ${pendingUserType}`);
            }

            // Clear the pending user type
            localStorage.removeItem('pending_user_type');
          }

          // Fetch the user profile
          const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('email', userEmail)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('Error fetching user profile:', error);
            setError(error.message);
          } else {
            setUserProfile(data);
            console.log('✅ User profile loaded:', data?.email, 'Role:', data?.role);
          }
        } catch (error) {
          console.error('Error in fetchUserProfile:', error);
          setError(error instanceof Error ? error.message : 'Profile fetch failed');
        }
      } else {
        setUserProfile(null);
      }
    };

    fetchUserProfile();
  }, [user?.email, isNextAuthActive]);

  const signInWithGoogle = async () => {
    try {
      setError(null);
      await signIn('google', {
        callbackUrl: window.location.origin,
        redirect: true
      });
    } catch (error) {
      console.error('Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);

      // Sign out from both auth systems
      if (isNextAuthActive) {
        await nextAuthSignOut({
          callbackUrl: window.location.origin,
          redirect: false // Don't redirect immediately
        });
      }

      if (isSupabaseActive) {
        await supabase.auth.signOut();
      }

      console.log('✅ Successfully signed out');

      // Redirect after sign out
      window.location.href = '/';
    } catch (error) {
      console.error('Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);
      // NextAuth.js handles session refresh automatically
      // We can trigger a re-fetch of the user profile
      if (session?.user?.email) {
        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('email', session.user.email!)
          .single();

        if (!error) {
          setUserProfile(data);
        }
      }
      console.log('✅ Session refreshed');
    } catch (error) {
      console.error('Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    signInWithGoogle,
    signOut,
    refreshAuth,
    userProfile
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
