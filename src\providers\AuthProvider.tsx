import React, { createContext, useContext, useEffect, useState } from 'react';
import { User as SupabaseUser, Session as SupabaseSession } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase-client';

// Auth.js session type
interface AuthJsSession {
  user: {
    id?: string;
    name?: string;
    email?: string;
    image?: string;
    customerId?: string;
    customerCreatedAt?: string;
  };
  expires: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: (SupabaseUser | AuthJsSession['user']) | null;
  session: (SupabaseSession | AuthJsSession) | null;
  isLoading: boolean;
  error: string | null;
  authProvider: 'supabase' | 'authjs' | null;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  customerData: any | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const AUTH_SERVER_URL = import.meta.env.VITE_AUTH_SERVER_URL || 'http://localhost:3001';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [error, setError] = useState<string | null>(null);
  const [customerData, setCustomerData] = useState<any | null>(null);

  // Supabase auth state
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [supabaseSession, setSupabaseSession] = useState<SupabaseSession | null>(null);

  // Auth.js auth state
  const [authJsSession, setAuthJsSession] = useState<AuthJsSession | null>(null);

  const [isLoading, setIsLoading] = useState(true);

  // Determine which auth provider is active
  const authProvider: 'supabase' | 'authjs' | null =
    supabaseSession ? 'supabase' :
    authJsSession ? 'authjs' : null;

  const isAuthenticated = !!(supabaseSession || authJsSession);

  // Use the appropriate user and session based on active provider
  const user = supabaseUser || authJsSession?.user || null;
  const session = supabaseSession || authJsSession || null;

  // Listen for Supabase auth changes (email/magic link)
  useEffect(() => {
    let mounted = true;

    const getInitialSupabaseSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ Error getting Supabase session:', error);
        } else if (mounted) {
          setSupabaseSession(session);
          setSupabaseUser(session?.user ?? null);
          console.log('✅ Supabase session loaded:', !!session);
        }
      } catch (error) {
        console.error('❌ Error in getInitialSupabaseSession:', error);
      }
    };

    getInitialSupabaseSession();

    // Listen for Supabase auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Supabase auth state changed:', event, !!session);

        if (mounted) {
          setSupabaseSession(session);
          setSupabaseUser(session?.user ?? null);
          setError(null);

          if (event === 'SIGNED_OUT') {
            console.log('👋 User signed out from Supabase');
          } else if (event === 'SIGNED_IN') {
            console.log('👋 User signed in via Supabase:', session?.user?.email);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Check for Auth.js session
  useEffect(() => {
    let mounted = true;

    const checkAuthJsSession = async () => {
      try {
        const response = await fetch(`${AUTH_SERVER_URL}/auth/session`, {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          if (mounted && data.session) {
            setAuthJsSession(data.session);
            console.log('✅ Auth.js session loaded:', data.session.user?.email);
          }
        }
      } catch (error) {
        console.error('❌ Error checking Auth.js session:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    checkAuthJsSession();

    // Poll for Auth.js session changes every 30 seconds
    const interval = setInterval(checkAuthJsSession, 30000);

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, []);

  // Fetch customer data from Supabase when session changes
  useEffect(() => {
    const fetchCustomerData = async () => {
      const userEmail = user?.email;
      if (userEmail) {
        try {
          const { data, error } = await (supabase as any)
            .from('customers')
            .select('*')
            .eq('email', userEmail)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('❌ Error fetching customer data:', error);
            setError(error.message);
          } else {
            setCustomerData(data);
            console.log('✅ Customer data loaded:', data?.email);
          }
        } catch (error) {
          console.error('❌ Error in fetchCustomerData:', error);
          setError(error instanceof Error ? error.message : 'Customer fetch failed');
        }
      } else {
        setCustomerData(null);
      }
    };

    fetchCustomerData();
  }, [user?.email]);

  // Google sign-in via Supabase Auth
  const signInWithGoogle = async () => {
    try {
      setError(null);
      setIsLoading(true);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        }
      });

      if (error) {
        console.error('❌ Google sign-in error:', error);
        setError(error.message);
      }
    } catch (error) {
      console.error('❌ Error in signInWithGoogle:', error);
      setError(error instanceof Error ? error.message : 'Google sign-in failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Email sign-in via Supabase magic link
  const signInWithEmail = async (email: string) => {
    try {
      setError(null);
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setError(error.message);
        console.error('❌ Error in email sign-in:', error);
      } else {
        console.log('✅ Magic link sent to:', email);
      }
    } catch (error) {
      console.error('❌ Error in signInWithEmail:', error);
      setError(error instanceof Error ? error.message : 'Email sign-in failed');
    }
  };

  const signOut = async () => {
    try {
      setError(null);

      // Sign out from both auth systems
      if (authProvider === 'supabase') {
        await supabase.auth.signOut();
        console.log('✅ Signed out from Supabase');
      } else if (authProvider === 'authjs') {
        // Sign out from Auth.js
        await fetch(`${AUTH_SERVER_URL}/auth/signout`, {
          method: 'POST',
          credentials: 'include',
        });
        setAuthJsSession(null);
        console.log('✅ Signed out from Auth.js');
      }

      // Clear all state
      setCustomerData(null);
      setError(null);

      window.location.href = '/';
    } catch (error) {
      console.error('❌ Error in signOut:', error);
      setError(error instanceof Error ? error.message : 'Sign-out failed');
    }
  };

  const refreshAuth = async () => {
    try {
      setError(null);

      // Refresh customer data
      if (user?.email) {
        const { data, error } = await (supabase as any)
          .from('customers')
          .select('*')
          .eq('email', user.email)
          .single();

        if (!error) {
          setCustomerData(data);
        }
      }
      console.log('✅ Auth refreshed');
    } catch (error) {
      console.error('❌ Error in refreshAuth:', error);
      setError(error instanceof Error ? error.message : 'Refresh failed');
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error,
    authProvider,
    signInWithGoogle,
    signInWithEmail,
    signOut,
    refreshAuth,
    customerData
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
