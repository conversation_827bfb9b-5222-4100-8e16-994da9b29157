import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import {
  AlertTriangle,
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  XCircle,
  ChevronDown,
  ChevronUp,
  Clock
} from 'lucide-react';
import { getRecentErrors, markErrorResolved } from '../../lib/error-logging/error-logger';

// DIRECT OVERRIDE: List of admin emails
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

export default function ErrorLogs() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [errors, setErrors] = useState([]);
  const [filteredErrors, setFilteredErrors] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [contextFilter, setContextFilter] = useState('all');
  const [severityFilter, setSeverityFilter] = useState('all');
  const [expandedErrors, setExpandedErrors] = useState({});
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');

  // Check if user is admin
  useEffect(() => {
    if (user) {
      // Get user email
      const userEmail = user.primaryEmailAddress?.emailAddress || '';

      // Simple check: user is admin if email is in the list or we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';
      const userIsAdmin = isDevelopment || ADMIN_EMAILS.includes(userEmail.toLowerCase());

      // Log for debugging
      console.log('ErrorLogs admin check:', { email: userEmail, isDevelopment, isAdmin: userIsAdmin });

      setIsAdmin(userIsAdmin);

      if (!userIsAdmin) {
        navigate('/unauthorized');
      } else {
        fetchErrors();
      }
    }
  }, [user, navigate]);

  // Fetch errors
  const fetchErrors = async () => {
    setIsLoading(true);
    try {
      const errorData = await getRecentErrors(100);
      setErrors(errorData);
      applyFilters(errorData);
    } catch (error) {
      console.error('Failed to fetch errors:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filters and search
  const applyFilters = (errorData = errors) => {
    let filtered = [...errorData];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(error => error.status === statusFilter);
    }

    // Apply context filter
    if (contextFilter !== 'all') {
      filtered = filtered.filter(error => error.context === contextFilter);
    }

    // Apply severity filter
    if (severityFilter !== 'all') {
      filtered = filtered.filter(error => error.severity === severityFilter);
    }

    // Apply search
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(error =>
        error.message.toLowerCase().includes(term) ||
        error.stack?.toLowerCase().includes(term) ||
        error.context.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      if (sortField === 'created_at') {
        return sortDirection === 'asc'
          ? new Date(a.created_at) - new Date(b.created_at)
          : new Date(b.created_at) - new Date(a.created_at);
      }

      if (sortField === 'severity') {
        const severityOrder = { critical: 3, error: 2, warning: 1, info: 0 };
        return sortDirection === 'asc'
          ? severityOrder[a.severity] - severityOrder[b.severity]
          : severityOrder[b.severity] - severityOrder[a.severity];
      }

      // Default string comparison
      const aValue = a[sortField]?.toString().toLowerCase() || '';
      const bValue = b[sortField]?.toString().toLowerCase() || '';

      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });

    setFilteredErrors(filtered);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [searchTerm, statusFilter, contextFilter, severityFilter, sortField, sortDirection]);

  // Toggle error expansion
  const toggleErrorExpansion = (errorId) => {
    setExpandedErrors(prev => ({
      ...prev,
      [errorId]: !prev[errorId]
    }));
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Mark error as resolved
  const handleResolveError = async (errorId) => {
    try {
      const success = await markErrorResolved(errorId);
      if (success) {
        // Update local state
        setErrors(prev => prev.map(error =>
          error.id === errorId
            ? { ...error, status: 'resolved', resolved_at: new Date().toISOString() }
            : error
        ));
        applyFilters();
      }
    } catch (error) {
      console.error('Failed to mark error as resolved:', error);
    }
  };

  // Get unique contexts for filter
  const contexts = ['all', ...new Set(errors.map(error => error.context))];

  // Get severity badge color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'error':
        return 'bg-orange-100 text-orange-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return null; // Navigate will redirect, this prevents flash of content
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Error Logs</h1>
          <button
            onClick={fetchErrors}
            className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-4 md:space-y-0">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearch}
                  placeholder="Search errors..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <div className="relative inline-block text-left">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
                >
                  <option value="all">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="resolved">Resolved</option>
                </select>
              </div>

              <div className="relative inline-block text-left">
                <select
                  value={contextFilter}
                  onChange={(e) => setContextFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
                >
                  <option value="all">All Contexts</option>
                  {contexts.filter(c => c !== 'all').map(context => (
                    <option key={context} value={context}>{context}</option>
                  ))}
                </select>
              </div>

              <div className="relative inline-block text-left">
                <select
                  value={severityFilter}
                  onChange={(e) => setSeverityFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="error">Error</option>
                  <option value="warning">Warning</option>
                  <option value="info">Info</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Error List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('message')}
                  >
                    <div className="flex items-center">
                      Error
                      {sortField === 'message' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('context')}
                  >
                    <div className="flex items-center">
                      Context
                      {sortField === 'context' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('severity')}
                  >
                    <div className="flex items-center">
                      Severity
                      {sortField === 'severity' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('created_at')}
                  >
                    <div className="flex items-center">
                      Time
                      {sortField === 'created_at' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center">
                      Status
                      {sortField === 'status' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredErrors.length > 0 ? (
                  filteredErrors.map((error) => (
                    <React.Fragment key={error.id}>
                      <tr className="hover:bg-gray-50 cursor-pointer" onClick={() => toggleErrorExpansion(error.id)}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <AlertTriangle className={`flex-shrink-0 mr-2 h-5 w-5 ${
                              error.severity === 'critical' ? 'text-red-500' :
                              error.severity === 'error' ? 'text-orange-500' :
                              error.severity === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                            }`} />
                            <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                              {error.message}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{error.context}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityColor(error.severity)}`}>
                            {error.severity}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-gray-400" />
                            {new Date(error.created_at).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            error.status === 'open' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {error.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          {error.status === 'open' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleResolveError(error.id);
                              }}
                              className="text-purple-600 hover:text-purple-900"
                            >
                              Resolve
                            </button>
                          )}
                        </td>
                      </tr>
                      {expandedErrors[error.id] && (
                        <tr>
                          <td colSpan={6} className="px-6 py-4 bg-gray-50">
                            <div className="text-sm text-gray-900">
                              <h4 className="font-medium mb-2">Stack Trace</h4>
                              <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-xs font-mono">
                                {error.stack || 'No stack trace available'}
                              </pre>

                              {error.metadata && (
                                <>
                                  <h4 className="font-medium mt-4 mb-2">Metadata</h4>
                                  <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-xs font-mono">
                                    {JSON.stringify(JSON.parse(error.metadata), null, 2)}
                                  </pre>
                                </>
                              )}

                              {error.url && (
                                <div className="mt-4">
                                  <span className="font-medium">URL:</span> {error.url}
                                </div>
                              )}

                              {error.user_id && (
                                <div className="mt-2">
                                  <span className="font-medium">User ID:</span> {error.user_id}
                                </div>
                              )}

                              {error.resolved_at && (
                                <div className="mt-2">
                                  <span className="font-medium">Resolved at:</span> {new Date(error.resolved_at).toLocaleString()}
                                </div>
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      {isLoading ? (
                        <div className="flex justify-center items-center">
                          <RefreshCw className="w-5 h-5 text-purple-600 animate-spin mr-2" />
                          Loading errors...
                        </div>
                      ) : (
                        'No errors found matching the current filters.'
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
