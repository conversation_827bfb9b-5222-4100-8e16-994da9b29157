import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Calendar, Clock, Users, DollarSign, ChevronRight, AlertCircle } from 'lucide-react';
import { getUserBookings, updateBookingStatus } from '../api/bookings';
import { useAuth } from '../providers/AuthProvider';
import { useUser } from '../hooks/useClerkCompat';

export default function MyBookings() {
  const { user: authUser } = useAuth();
  const { user: clerkUser, isLoaded: clerkLoaded } = useUser();
  const navigate = useNavigate();
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!authUser && clerkLoaded && !clerkUser) {
      navigate('/login?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    async function fetchBookings() {
      try {
        setLoading(true);

        // Get user ID from Clerk or localStorage
        const userId = clerkUser?.id || localStorage.getItem('clerk_user_id');
        const userEmail = clerkUser?.primaryEmailAddress?.emailAddress ||
                         localStorage.getItem('clerk_user_email') ||
                         localStorage.getItem('email');

        if (!userId) {
          throw new Error('User ID not found');
        }

        console.log('Fetching bookings for user:', userId, 'with email:', userEmail);
        const data = await getUserBookings(userId, userEmail);
        console.log('Bookings data:', data);
        setBookings(data);
      } catch (err) {
        console.error('Error fetching bookings:', err);
        setError('Failed to load your bookings');
      } finally {
        setLoading(false);
      }
    }

    if (clerkLoaded) {
      fetchBookings();
    }
  }, [authUser, clerkUser, clerkLoaded, navigate]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle booking cancellation
  const handleCancelBooking = async (bookingId: string) => {
    if (!confirm('Are you sure you want to cancel this booking?')) {
      return;
    }

    try {
      await updateBookingStatus(bookingId, 'cancelled');

      // Update the local state
      setBookings(bookings.map(booking =>
        booking.id === bookingId
          ? { ...booking, status: 'cancelled' }
          : booking
      ));
    } catch (err) {
      console.error('Error cancelling booking:', err);
      alert('Failed to cancel booking. Please try again.');
    }
  };

  // Group bookings by status
  const upcomingBookings = bookings.filter(b =>
    b.status !== 'cancelled' && new Date(b.start_date) > new Date()
  );

  const pastBookings = bookings.filter(b =>
    b.status !== 'cancelled' && new Date(b.start_date) <= new Date()
  );

  const cancelledBookings = bookings.filter(b => b.status === 'cancelled');

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">My Bookings</h1>

        {error && error !== 'Failed to load your bookings' && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            {error}
          </div>
        )}

        {bookings.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">No Bookings Yet</h2>
            <p className="text-gray-600 mb-6">You haven't made any bookings yet. Start exploring venues to book your next event!</p>
            <Link to="/" className="px-4 py-2 bg-purple-600 text-white rounded-md inline-block hover:bg-purple-700 transition-colors">
              Explore Venues
            </Link>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Upcoming Bookings */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Upcoming Bookings</h2>
              {upcomingBookings.length === 0 ? (
                <p className="text-gray-600">No upcoming bookings</p>
              ) : (
                <div className="space-y-4">
                  {upcomingBookings.map(booking => (
                    <div key={booking.id} className="bg-white rounded-lg shadow overflow-hidden">
                      <div className="p-4 sm:p-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold">{booking.venue.title}</h3>
                          <span className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-start">
                            <Calendar className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Date</p>
                              <p>{formatDate(booking.start_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Clock className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Time</p>
                              <p>{formatTime(booking.start_date)} - {formatTime(booking.end_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Users className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Guests</p>
                              <p>{booking.guests_count} people</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <DollarSign className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Total</p>
                              <p>${booking.total_price.toFixed(2)}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-3 mt-4">
                          <Link
                            to={`/booking-confirmation/${booking.id}`}
                            className="px-4 py-2 bg-purple-600 text-white rounded-md text-center hover:bg-purple-700 transition-colors"
                          >
                            View Details
                          </Link>

                          <button
                            onClick={() => handleCancelBooking(booking.id)}
                            className="px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50 transition-colors"
                          >
                            Cancel Booking
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Past Bookings */}
            {pastBookings.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Past Bookings</h2>
                <div className="space-y-4">
                  {pastBookings.map(booking => (
                    <div key={booking.id} className="bg-white rounded-lg shadow overflow-hidden">
                      <div className="p-4 sm:p-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold">{booking.venue.title}</h3>
                          <span className="inline-block px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                            {booking.status === 'completed' ? 'Completed' : 'Past'}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-start">
                            <Calendar className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Date</p>
                              <p>{formatDate(booking.start_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Clock className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Time</p>
                              <p>{formatTime(booking.start_date)} - {formatTime(booking.end_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Users className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Guests</p>
                              <p>{booking.guests_count} people</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <DollarSign className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Total</p>
                              <p>${booking.total_price.toFixed(2)}</p>
                            </div>
                          </div>
                        </div>

                        <Link
                          to={`/booking-confirmation/${booking.id}`}
                          className="flex items-center text-purple-600 hover:text-purple-800 transition-colors"
                        >
                          View Details
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Cancelled Bookings */}
            {cancelledBookings.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Cancelled Bookings</h2>
                <div className="space-y-4">
                  {cancelledBookings.map(booking => (
                    <div key={booking.id} className="bg-white rounded-lg shadow overflow-hidden opacity-75">
                      <div className="p-4 sm:p-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold">{booking.venue.title}</h3>
                          <span className="inline-block px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
                            Cancelled
                          </span>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-start">
                            <Calendar className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Date</p>
                              <p>{formatDate(booking.start_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Clock className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Time</p>
                              <p>{formatTime(booking.start_date)} - {formatTime(booking.end_date)}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Users className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Guests</p>
                              <p>{booking.guests_count} people</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <DollarSign className="w-5 h-5 text-gray-400 mr-2" />
                            <div>
                              <p className="text-xs text-gray-500">Total</p>
                              <p>${booking.total_price.toFixed(2)}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
