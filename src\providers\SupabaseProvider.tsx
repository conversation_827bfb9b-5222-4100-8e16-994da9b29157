import React, { createContext, useContext, useEffect, useState } from 'react';
import { initializeDatabase } from '../utils/run-migration';
import { supabase } from '../lib/supabase-client';
import { UserRole } from '../services/auth';
import { getClerkToken } from '../lib/clerk-supabase';

// User profile interface
interface UserProfile {
  id: string;
  email: string;
  role: UserRole;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  bio?: string;
  phone?: string;
  is_host: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseContextType {
  userProfile: UserProfile | null;
  isLoading: boolean;
  isHost: boolean;
  isAuthenticated: boolean;
  setUserRole: (role: UserRole) => Promise<boolean>;
  refreshUserProfile: () => Promise<void>;
  signOut: () => Promise<void>;
}

const SupabaseContext = createContext<SupabaseContextType>({
  userProfile: null,
  isLoading: true,
  isHost: false,
  isAuthenticated: false,
  setUserRole: async () => false,
  refreshUserProfile: async () => {},
  signOut: async () => {},
});

export const useSupabase = () => useContext(SupabaseContext);

interface SupabaseProviderProps {
  children: React.ReactNode;
}

export function SupabaseProvider({ children }: SupabaseProviderProps) {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize the database
  useEffect(() => {
    const initialize = async () => {
      try {
        // Set a timeout to prevent blocking the UI
        setTimeout(async () => {
          try {
            await initializeDatabase();
            console.log('Database initialized successfully');
          } catch (error) {
            console.error('Error initializing database:', error);
          }
        }, 100);
      } catch (error) {
        console.error('Error in initialize function:', error);
      }
    };
    initialize();
  }, []);

  // Set up auth state listener for Supabase
  useEffect(() => {
    console.log('Setting up Supabase auth state listener...');

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Supabase auth state changed:', event, session?.user?.id);

        if (session?.user) {
          console.log('Supabase user is authenticated:', session.user.email);
          setIsAuthenticated(true);

          // Get user profile from database
          await fetchUserProfile(session.user.id);
        } else {
          console.log('No authenticated Supabase user');
          setUserProfile(null);
          setIsHost(false);
          setIsAuthenticated(false);
          setIsLoading(false);
        }
      }
    );

    // Check current session on mount (with Clerk integration)
    const checkCurrentSession = async () => {
      // First check for Clerk token
      const clerkToken = await getClerkToken();

      if (clerkToken) {
        console.log('Found Clerk token, setting up Supabase session');
        try {
          // Set the Clerk token on Supabase
          await supabase.auth.setSession({
            access_token: clerkToken,
            refresh_token: ''
          });

          // Get the current user from Supabase
          const { data: { user }, error } = await supabase.auth.getUser();

          if (user) {
            console.log('User authenticated via Clerk-Supabase:', user.email);
            setIsAuthenticated(true);
            await fetchUserProfile(user.id);
            return;
          } else if (error) {
            console.error('Error getting user from Supabase:', error);
          }
        } catch (sessionError) {
          console.error('Error setting Supabase session with Clerk token:', sessionError);
        }
      }

      // Fallback to regular Supabase session check
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        console.log('Current Supabase session found:', session.user.email);
        setIsAuthenticated(true);
        await fetchUserProfile(session.user.id);
      } else {
        console.log('No current Supabase session or Clerk token');
        setIsLoading(false);
      }
    };

    checkCurrentSession();

    // Clean up subscription
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Also listen for auth_complete events
  useEffect(() => {
    const handleAuthComplete = (event: CustomEvent) => {
      try {
        console.log('Auth complete event detected in SupabaseProvider:', event.detail);

        // Set auth_success flag
        localStorage.setItem('auth_success', 'true');
        localStorage.setItem('auth_success_time', new Date().toISOString());

        // Check if the event has a user
        if (event.detail?.user?.id) {
          console.log('Auth complete event has user ID:', event.detail.user.id);

          // Store user info in localStorage for persistence
          if (event.detail.user.email) {
            localStorage.setItem('email', event.detail.user.email);
          }

          if (event.detail.user.first_name) {
            localStorage.setItem('first_name', event.detail.user.first_name);
          }

          if (event.detail.user.last_name) {
            localStorage.setItem('last_name', event.detail.user.last_name);
          }

          // Store Clerk ID if available
          if (event.detail.user.clerk_id) {
            localStorage.setItem('clerk_user_id', event.detail.user.clerk_id);
          }

          // Check if we have a Clerk ID
          const clerkId = event.detail.user.clerk_id || localStorage.getItem('clerk_user_id');

          if (clerkId) {
            // Fetch user profile by Clerk ID
            fetchUserProfileByClerkId(clerkId);
          } else {
            // Fallback to Supabase ID
            fetchUserProfile(event.detail.user.id);
          }
          return;
        }

        // Check for current Supabase session
        const checkCurrentSession = async () => {
          const { data: { session } } = await supabase.auth.getSession();

          if (session?.user) {
            console.log('Current Supabase session found in auth_complete handler:', session.user.email);
            setIsAuthenticated(true);
            await fetchUserProfile(session.user.id);
          } else {
            console.log('No current Supabase session in auth_complete handler');
            setIsAuthenticated(false);
          }
        };

        checkCurrentSession();
      } catch (error) {
        console.error('Error in handleAuthComplete:', error);
        setIsAuthenticated(false);
      }
    };

    window.addEventListener('auth_complete', handleAuthComplete as EventListener);

    // Also check for auth_success flag on mount
    const authSuccess = localStorage.getItem('auth_success') === 'true';
    if (authSuccess && !userProfile) {
      console.log('Auth success flag found in localStorage');

      // Check for current Supabase session
      const checkCurrentSession = async () => {
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          console.log('Current Supabase session found on mount:', session.user.email);
          setIsAuthenticated(true);
          await fetchUserProfile(session.user.id);
        }
      };

      checkCurrentSession();
    }

    return () => {
      window.removeEventListener('auth_complete', handleAuthComplete as EventListener);
    };
  }, [userProfile]);

  // Fetch user profile by Clerk ID
  const fetchUserProfileByClerkId = async (clerkId: string) => {
    console.log('Fetching user profile by Clerk ID:', clerkId);
    setIsLoading(true);

    try {
      // Try to get the profile by Clerk ID
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('clerk_id', clerkId)
        .single();

      if (error) {
        console.log('Error fetching profile by Clerk ID:', error.message);

        // If profile doesn't exist, create one
        const email = localStorage.getItem('email') || '';
        const firstName = localStorage.getItem('first_name') || '';
        const lastName = localStorage.getItem('last_name') || '';
        const userType = localStorage.getItem('auth_user_type') || 'guest';
        const role = userType as UserRole;

        // Create a new profile
        const newProfile = {
          id: `temp-${Date.now()}`, // Temporary ID for the profile
          clerk_id: clerkId,
          email: email,
          role: role,
          first_name: firstName,
          last_name: lastName,
          is_host: role === 'host',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Try to create the profile
        const { data, error: insertError } = await supabase
          .from('user_profiles')
          .insert(newProfile)
          .select();

        if (insertError) {
          console.error('Error creating user profile:', insertError);

          // Even if insert fails, set the profile in state
          setUserProfile(newProfile as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        } else {
          console.log('User profile created successfully:', data);
          setUserProfile((data?.[0] || newProfile) as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        }
      } else if (profile) {
        console.log('User profile found by Clerk ID:', profile);
        setUserProfile(profile as UserProfile);
        setIsHost(profile.role === 'host' || profile.is_host);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error fetching user profile by Clerk ID:', error);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user profile by Supabase user ID
  const fetchUserProfile = async (userId: string) => {
    console.log('Fetching user profile by Supabase user ID:', userId);
    setIsLoading(true);

    try {
      // Try to get the profile by user ID
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.log('Error fetching profile:', error.message);

        // If profile doesn't exist, create one
        const { data: userData, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error getting user data:', userError);
          setIsLoading(false);
          return;
        }

        if (userData.user) {
          await createUserProfile(userData.user);
        } else {
          console.warn('No user data available');
          setIsLoading(false);
        }
      } else if (profile) {
        console.log('User profile found:', profile);
        setUserProfile(profile as UserProfile);
        setIsHost(profile.role === 'host' || profile.is_host);
        setIsAuthenticated(true);
      }

      // If no profile was found, create a minimal profile
      if (!profile) {
        console.log('No profile found, creating minimal profile');

        // Get user info from Supabase auth
        const { data: userData, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error getting user data:', userError);
          setIsLoading(false);
          return;
        }

        if (userData.user) {
          // Create a minimal profile
          const email = userData.user.email || '';
          const userType = localStorage.getItem('auth_user_type') || 'guest';
          const role = userType as UserRole;

          // Try to extract first and last name from email
          const nameParts = email.split('@')[0].split('.');
          const firstName = localStorage.getItem('first_name') || nameParts[0] || '';
          const lastName = localStorage.getItem('last_name') || (nameParts.length > 1 ? nameParts[1] : '') || '';

          const minimalProfile = {
            id: userId,
            email: email,
            role: role,
            first_name: firstName,
            last_name: lastName,
            is_host: role === 'host'
          };

          // Set the profile in state
          setUserProfile(minimalProfile as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);

          console.log('Minimal profile created and set in state:', minimalProfile);

          // Try to create the profile in Supabase
          try {
            const { data, error } = await supabase
              .from('user_profiles')
              .insert(minimalProfile)
              .select();

            if (error) {
              console.error('Error creating user profile:', error);
            } else {
              console.log('User profile created successfully:', data);
              setUserProfile((data?.[0] || minimalProfile) as UserProfile);
            }
          } catch (e) {
            console.error('Error creating profile in Supabase:', e);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile by Supabase user ID:', error);

      // Try to get current user as fallback
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          console.log('Creating minimal profile from current user after error');
          const email = user.email || '';
          const userType = localStorage.getItem('auth_user_type') || 'guest';
          const role = userType as UserRole;

          const minimalProfile = {
            id: userId,
            email: email,
            role: role as UserRole,
            is_host: role === 'host'
          };

          setUserProfile(minimalProfile as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        }
      } catch (e) {
        console.error('Error creating minimal profile after fetch error:', e);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Create a user profile from Supabase user
  const createUserProfile = async (user: any) => {
    try {
      const email = user.email || '';
      const userType = localStorage.getItem('auth_user_type') || 'guest';
      const role = userType as UserRole;

      // Try to extract first and last name from email
      const nameParts = email.split('@')[0].split('.');
      const firstName = localStorage.getItem('first_name') || nameParts[0] || '';
      const lastName = localStorage.getItem('last_name') || (nameParts.length > 1 ? nameParts[1] : '') || '';

      console.log('Creating user profile with role:', role);

      const newProfile = {
        id: user.id,
        email: email,
        role: role,
        first_name: firstName,
        last_name: lastName,
        is_host: role === 'host',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // First check if the profile already exists
      const { data: existingProfile, error: checkError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();

      if (checkError) {
        console.log('Error checking for existing profile:', checkError.message);
        // Continue with upsert even if check fails
      }

      if (existingProfile) {
        console.log('Profile already exists, updating:', existingProfile);

        // Update the existing profile
        const { data, error } = await supabase
          .from('user_profiles')
          .update({
            email: email,
            role: role,
            first_name: firstName,
            last_name: lastName,
            is_host: role === 'host',
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)
          .select();

        if (error) {
          console.error('Error updating user profile:', error);

          // Even if update fails, set the profile in state
          setUserProfile(existingProfile as UserProfile);
          setIsHost(existingProfile.role === 'host' || existingProfile.is_host);
          setIsAuthenticated(true);
        } else {
          console.log('User profile updated successfully:', data);
          setUserProfile((data?.[0] || existingProfile) as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        }
      } else {
        // Create a new profile
        const { data, error } = await supabase
          .from('user_profiles')
          .insert(newProfile)
          .select();

        if (error) {
          console.error('Error creating user profile:', error);

          // Even if insert fails, set the profile in state
          setUserProfile(newProfile as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        } else {
          console.log('User profile created successfully:', data);
          setUserProfile((data?.[0] || newProfile) as UserProfile);
          setIsHost(role === 'host');
          setIsAuthenticated(true);
        }
      }
    } catch (error) {
      console.error('Error in createUserProfile:', error);

      // Even if there's an error, try to set a minimal profile
      const email = user.email || '';
      const userType = localStorage.getItem('auth_user_type') || 'guest';
      const role = userType as UserRole;

      const minimalProfile = {
        id: user.id,
        email: email,
        role: role as UserRole,
        is_host: role === 'host'
      };

      setUserProfile(minimalProfile as UserProfile);
      setIsHost(role === 'host');
      setIsAuthenticated(true);
    }
  };



  // Update user role
  const updateUserRole = async (role: UserRole): Promise<boolean> => {
    if (!userProfile) return false;

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({
          role,
          is_host: role === 'host',
          updated_at: new Date().toISOString()
        })
        .eq('id', userProfile.id);

      if (error) {
        console.error('Error updating user role:', error);
        return false;
      }

      setUserProfile({
        ...userProfile,
        role,
        is_host: role === 'host'
      });

      setIsHost(role === 'host');
      return true;
    } catch (error) {
      console.error('Error in updateUserRole:', error);
      return false;
    }
  };

  // Refresh user profile
  const refreshUserProfile = async (): Promise<void> => {
    try {
      // Get current session
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        console.log('Current Supabase session found in refreshUserProfile:', session.user.email);
        setIsAuthenticated(true);
        await fetchUserProfile(session.user.id);
      } else {
        console.log('No current Supabase session in refreshUserProfile');
        setIsAuthenticated(false);
        setUserProfile(null);
        setIsHost(false);
      }
    } catch (error) {
      console.error('Error in refreshUserProfile:', error);
      setIsAuthenticated(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<void> => {
    // Clear all auth-related localStorage items
    localStorage.removeItem('auth_success');
    localStorage.removeItem('auth_success_time');
    localStorage.removeItem('auth_processing');
    localStorage.removeItem('auth_user_type');
    localStorage.removeItem('auth_redirect_to');
    localStorage.removeItem('first_name');
    localStorage.removeItem('last_name');

    // Reset state
    setUserProfile(null);
    setIsHost(false);
    setIsAuthenticated(false);

    // Sign out from Supabase
    await supabase.auth.signOut();
  };

  const value = {
    userProfile,
    isLoading,
    isHost,
    isAuthenticated,
    setUserRole: updateUserRole,
    refreshUserProfile,
    signOut,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
}
