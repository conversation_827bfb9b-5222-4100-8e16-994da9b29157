export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          email: string
          role: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          bio: string | null
          phone: string | null
          is_host: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          role?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          phone?: string | null
          is_host?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          phone?: string | null
          is_host?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      venues: {
        Row: {
          id: string
          title: string
          description: string | null
          location: string
          coordinates: [number, number] | null
          price: number
          capacity: number
          amenities: string[] | null
          images: string[] | null
          host_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          location: string
          coordinates?: [number, number] | null
          price: number
          capacity: number
          amenities?: string[] | null
          images?: string[] | null
          host_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          location?: string
          coordinates?: [number, number] | null
          price?: number
          capacity?: number
          amenities?: string[] | null
          images?: string[] | null
          host_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      bookings: {
        Row: {
          id: string
          venue_id: string
          guest_id: string
          start_date: string
          end_date: string
          guests_count: number
          total_price: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          venue_id: string
          guest_id: string
          start_date: string
          end_date: string
          guests_count: number
          total_price: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          venue_id?: string
          guest_id?: string
          start_date?: string
          end_date?: string
          guests_count?: number
          total_price?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_venue_rating: {
        Args: { venue_uuid: string }
        Returns: number
      }
    }
  }
}