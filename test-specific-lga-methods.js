/**
 * Test Specific Addresses with Different LGA Detection Methods
 * 
 * This script tests specific addresses with different LGA detection methods
 * to determine which method provides the most accurate results.
 */

// Test addresses
const testAddresses = [
  '62 Cameron St, Doonside NSW 2767',
  '62 Linden Way, Bella Vista NSW 2153',
  '213 Pennant Hills Rd, Thornleigh NSW 2120'
];

// Method 1: Layer 8 of NSW Spatial Services API
async function testLayer8Method(address) {
  try {
    console.log(`Testing Layer 8 for address: ${address}`);
    
    // First, geocode the address
    const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    if (geocodeData.features && geocodeData.features.length > 0) {
      const [lng, lat] = geocodeData.features[0].center;
      console.log(`Geocoded coordinates: ${lat}, ${lng}`);
      
      // NSW Spatial Services API endpoint for LGA (Layer 8)
      const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
      
      const lgaParams = new URLSearchParams({
        geometry: `${lng},${lat}`, // longitude first, then latitude
        geometryType: 'esriGeometryPoint',
        inSR: '4326', // WGS84 coordinate system
        outFields: 'lganame', // We know the field name is 'lganame'
        f: 'json'
      });
      
      const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
      
      if (lgaResponse.ok) {
        const lgaData = await lgaResponse.json();
        
        if (lgaData.features && lgaData.features.length > 0) {
          const lgaName = lgaData.features[0].attributes.lganame;
          console.log('LGA from Layer 8:', lgaName);
          
          // Format the LGA name to title case for consistency
          if (lgaName) {
            // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
            const formattedLgaName = lgaName.toLowerCase().split(' ').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
            
            console.log('Formatted LGA name:', formattedLgaName);
            return { method: 'Layer 8', lga: formattedLgaName, coordinates: { lat, lng } };
          }
        } else {
          console.log('No LGA information found from Layer 8 API');
        }
      } else {
        console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
      }
    } else {
      console.error('Geocoding failed:', geocodeData);
    }
  } catch (error) {
    console.error('Error testing Layer 8 LGA detection:', error);
  }
  
  return { method: 'Layer 8', lga: null, coordinates: null };
}

// Method 2: Layer 1 of NSW Administrative Boundaries Theme
async function testLayer1Method(address, lat, lng) {
  try {
    console.log(`Testing Layer 1 for address: ${address}`);
    
    // NSW Spatial Services API endpoint for LGA (Layer 1)
    const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/1/query`;
    
    const lgaParams = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
    
    if (lgaResponse.ok) {
      const lgaData = await lgaResponse.json();
      
      if (lgaData.features && lgaData.features.length > 0) {
        console.log('Layer 1 attributes:', lgaData.features[0].attributes);
        
        // Check for different possible field names
        const attributes = lgaData.features[0].attributes;
        let lgaName = null;
        
        if (attributes.NAME) {
          lgaName = attributes.NAME;
          console.log('LGA from Layer 1 (NAME):', lgaName);
        } else if (attributes.name) {
          lgaName = attributes.name;
          console.log('LGA from Layer 1 (name):', lgaName);
        } else if (attributes.LGA_NAME) {
          lgaName = attributes.LGA_NAME;
          console.log('LGA from Layer 1 (LGA_NAME):', lgaName);
        } else if (attributes.lga_name) {
          lgaName = attributes.lga_name;
          console.log('LGA from Layer 1 (lga_name):', lgaName);
        } else {
          console.log('LGA field not found in Layer 1 attributes');
        }
        
        return { method: 'Layer 1', lga: lgaName };
      } else {
        console.log('No LGA information found from Layer 1 API');
      }
    } else {
      console.error('Layer 1 API response not OK:', lgaResponse.statusText);
    }
  } catch (error) {
    console.error('Error testing Layer 1 LGA detection:', error);
  }
  
  return { method: 'Layer 1', lga: null };
}

// Method 3: NSW Land Parcel Property Theme (Layer 9)
async function testLandParcelMethod(address, lat, lng) {
  try {
    console.log(`Testing Land Parcel for address: ${address}`);
    
    // NSW Spatial Services API endpoint for LGA (Land Parcel Property Theme)
    const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Land_Parcel_Property_Theme/MapServer/9/query`;
    
    const lgaParams = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
    
    if (lgaResponse.ok) {
      const lgaData = await lgaResponse.json();
      
      if (lgaData.features && lgaData.features.length > 0) {
        console.log('Land Parcel attributes:', lgaData.features[0].attributes);
        
        // Check for different possible field names
        const attributes = lgaData.features[0].attributes;
        let lgaName = null;
        
        if (attributes.LGANAME) {
          lgaName = attributes.LGANAME;
          console.log('LGA from Land Parcel (LGANAME):', lgaName);
        } else if (attributes.lganame) {
          lgaName = attributes.lganame;
          console.log('LGA from Land Parcel (lganame):', lgaName);
        } else if (attributes.LGA_NAME) {
          lgaName = attributes.LGA_NAME;
          console.log('LGA from Land Parcel (LGA_NAME):', lgaName);
        } else {
          console.log('LGA field not found in Land Parcel attributes');
        }
        
        return { method: 'Land Parcel', lga: lgaName };
      } else {
        console.log('No LGA information found from Land Parcel API');
      }
    } else {
      console.error('Land Parcel API response not OK:', lgaResponse.statusText);
    }
  } catch (error) {
    console.error('Error testing Land Parcel LGA detection:', error);
  }
  
  return { method: 'Land Parcel', lga: null };
}

// Method 4: WFS endpoint
async function testWFSMethod(address, lat, lng) {
  try {
    console.log(`Testing WFS for address: ${address}`);
    
    // WFS endpoint for LGA
    const restUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/EDP/Administrative_Boundaries/MapServer/1/query`;
    
    const restParams = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const restResponse = await fetch(`${restUrl}?${restParams.toString()}`);
    
    if (restResponse.ok) {
      const restData = await restResponse.json();
      
      if (restData.features && restData.features.length > 0) {
        console.log('WFS attributes:', restData.features[0].attributes);
        
        // Check for different possible field names
        const attributes = restData.features[0].attributes;
        let lgaName = null;
        
        if (attributes.LGA_NAME) {
          lgaName = attributes.LGA_NAME;
          console.log('LGA from WFS (LGA_NAME):', lgaName);
        } else if (attributes.lga_name) {
          lgaName = attributes.lga_name;
          console.log('LGA from WFS (lga_name):', lgaName);
        } else if (attributes.NAME) {
          lgaName = attributes.NAME;
          console.log('LGA from WFS (NAME):', lgaName);
        } else {
          console.log('LGA field not found in WFS attributes');
        }
        
        return { method: 'WFS', lga: lgaName };
      } else {
        console.log('No LGA information found from WFS API');
      }
    } else {
      console.error('WFS API response not OK:', restResponse.statusText);
    }
  } catch (error) {
    console.error('Error testing WFS LGA detection:', error);
  }
  
  return { method: 'WFS', lga: null };
}

// Method 5: Text extraction
function testTextExtractionMethod(address) {
  console.log(`Testing text extraction for address: ${address}`);
  
  // Common LGA patterns in addresses
  const lgaPatterns = [
    /City of ([A-Za-z\s]+)(?:,|$)/i,       // City of Sydney
    /([A-Za-z\s]+) Council(?:,|$)/i,       // Randwick Council
    /([A-Za-z\s]+) Shire(?:,|$)/i,         // Sutherland Shire
    /([A-Za-z\s]+) Municipal(?:,|$)/i      // Woollahra Municipal
  ];
  
  // Check if the address contains any LGA patterns
  for (const pattern of lgaPatterns) {
    const match = address.match(pattern);
    if (match && match[1]) {
      const lgaName = match[1].trim();
      console.log('LGA from text extraction (pattern):', lgaName);
      return { method: 'Text Extraction (Pattern)', lga: lgaName };
    }
  }
  
  // Suburb to LGA mapping
  const suburbToLGA = {
    'doonside': 'Blacktown City Council',
    'bella vista': 'The Hills Shire Council',
    'thornleigh': 'Hornsby Shire Council'
  };
  
  // Extract suburb from address
  const addressLower = address.toLowerCase();
  for (const [suburb, lga] of Object.entries(suburbToLGA)) {
    if (addressLower.includes(suburb)) {
      console.log(`LGA from text extraction (suburb mapping - ${suburb}):`, lga);
      return { method: 'Text Extraction (Suburb Mapping)', lga };
    }
  }
  
  console.log('No LGA found from text extraction');
  return { method: 'Text Extraction', lga: null };
}

// Run the tests
async function runTests() {
  console.log('Testing specific addresses with different LGA detection methods...\n');
  
  const results = [];
  
  for (const address of testAddresses) {
    console.log(`\n=== Testing address: ${address} ===`);
    
    // Test Layer 8
    const layer8Result = await testLayer8Method(address);
    
    // Only proceed with other methods if we have coordinates
    if (layer8Result.coordinates) {
      // Test Layer 1
      const layer1Result = await testLayer1Method(
        address,
        layer8Result.coordinates.lat,
        layer8Result.coordinates.lng
      );
      
      // Test Land Parcel
      const landParcelResult = await testLandParcelMethod(
        address,
        layer8Result.coordinates.lat,
        layer8Result.coordinates.lng
      );
      
      // Test WFS
      const wfsResult = await testWFSMethod(
        address,
        layer8Result.coordinates.lat,
        layer8Result.coordinates.lng
      );
      
      // Test text extraction
      const textResult = testTextExtractionMethod(address);
      
      // Store results
      results.push({
        address,
        layer8: layer8Result.lga,
        layer1: layer1Result.lga,
        landParcel: landParcelResult.lga,
        wfs: wfsResult.lga,
        text: textResult.lga
      });
    } else {
      console.error('Could not geocode address:', address);
      results.push({
        address,
        layer8: null,
        layer1: null,
        landParcel: null,
        wfs: null,
        text: null
      });
    }
    
    console.log('='.repeat(50));
  }
  
  // Summary
  console.log('\n=== Summary ===');
  console.log('Address | Layer 8 | Layer 1 | Land Parcel | WFS | Text Extraction');
  console.log('-'.repeat(100));
  
  for (const result of results) {
    console.log(`${result.address} | ${result.layer8 || 'Not found'} | ${result.layer1 || 'Not found'} | ${result.landParcel || 'Not found'} | ${result.wfs || 'Not found'} | ${result.text || 'Not found'}`);
  }
}

// Run the tests
runTests().catch(console.error);
