import express from 'express';
import { handleAuthRequest } from '../lib/auth';

const router = express.Router();

// Handle all Auth.js routes
router.all('/api/auth/*', async (req, res) => {
  try {
    // Convert Express request to Web API Request
    const url = new URL(req.url, `http://${req.headers.host}`);
    const request = new Request(url, {
      method: req.method,
      headers: req.headers as any,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined,
    });

    // Handle the auth request
    const response = await handleAuthRequest(request);
    
    // Convert Web API Response back to Express response
    const body = await response.text();
    
    // Set headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    
    res.status(response.status).send(body);
  } catch (error) {
    console.error('Auth route error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
