import { StrictMode } from 'react';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import App from './App.tsx';
import SimpleApp from './SimpleApp.tsx';
import './index.css';
import { useSimpleApp } from './config/appConfig';
import { HelmetProvider } from 'react-helmet-async';

// Import NextAuth SessionProvider
import { SessionProvider } from 'next-auth/react';

// Import custom HMR client for better WebSocket handling
import './hmr-client.js';

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Caught global error:', event.error);
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by error boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
          <p className="mb-4">We're sorry, but there was an error loading the application.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Reload the page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

function renderWithErrorHandling() {
  try {
    const root = createRoot(document.getElementById('root')!);
    
    root.render(
      <StrictMode>
        <ErrorBoundary>
          <SessionProvider>
            <HelmetProvider>
              <I18nextProvider i18n={i18n}>
                {useSimpleApp ? <SimpleApp /> : <App />}
              </I18nextProvider>
            </HelmetProvider>
          </SessionProvider>
        </ErrorBoundary>
      </StrictMode>
    );

    console.log('React app rendered successfully with Supabase Auth');
  } catch (error: any) {
    console.error('Failed to render React app:', error, error.stack);
    // Show fallback content
    const fallbackElement = document.getElementById('fallback');
    if (fallbackElement) {
      fallbackElement.style.display = 'block';
    }
    // Hide loading indicator
    const loadingIndicator = document.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }
}

// Execute with a small delay to ensure DOM is ready
setTimeout(renderWithErrorHandling, 0);
