import { StrictMode } from 'react';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
// Import both apps - we'll conditionally render based on URL parameter
import App from './App';
import SimpleApp from './SimpleApp';
import './index.css';
import { useSimpleApp } from './config/appConfig';

// Import Clerk
import { ClerkProvider } from '@clerk/clerk-react';

// Import custom HMR client for better WebSocket handling
import './hmr-client.js';

// Get Clerk publishable key with local development support
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY || 
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    ? 'pk_test_Y2xlcmsuaG91c2Vnb2luZy5jb20uYXUk' // Test key for local development
    : 'pk_live_Y2xlcmsuaG91c2Vnb2luZy5jb20uYXUk'); // Production key

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Caught global error:', event.error);
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Error boundary component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by error boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
          <p className="mb-4">We're sorry, but there was an error loading the application.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Reload the page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Error boundary function to catch rendering errors
function renderWithErrorHandling() {
  try {
    const root = createRoot(document.getElementById('root')!);
    root.render(
      <StrictMode>
        <ErrorBoundary>
          <ClerkProvider publishableKey={clerkPubKey}>
            <I18nextProvider i18n={i18n}>
              {useSimpleApp ? <SimpleApp /> : <App />}
            </I18nextProvider>
          </ClerkProvider>
        </ErrorBoundary>
      </StrictMode>
    );
    console.log('React app rendered successfully with Clerk');
    
    // Dispatch app initialization event
    document.dispatchEvent(new CustomEvent('app-initialized', {
      detail: { success: true, provider: 'clerk' }
    }));
  } catch (error: any) {
    console.error('Failed to render React app:', error, error.stack);
    // Show fallback content
    const fallbackElement = document.getElementById('fallback');
    if (fallbackElement) {
      fallbackElement.style.display = 'block';
    }
    // Hide loading indicator
    const loadingIndicator = document.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }
}

// Execute with a small delay to ensure DOM is ready
setTimeout(renderWithErrorHandling, 0);
