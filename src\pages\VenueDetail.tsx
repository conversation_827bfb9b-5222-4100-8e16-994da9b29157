import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { Star, MapPin, Users, Wifi, UtensilsCrossed, Car, Clock, Volume2, Music2, <PERSON>, AlertTriangle } from 'lucide-react';
import BookingForm from '../components/booking/BookingForm';
import EnhancedBookingFlow from '../components/booking/EnhancedBookingFlow';
import BookingMessagingIntegration from '../components/messaging/BookingMessagingIntegration';
import type { BookingData } from '../components/booking/BookingForm';
import { createBooking } from '../api/bookings';
import { getVenueById } from '../api/venues';
import { mockVenues } from '../data/mockVenues';
import SEO from '../components/seo/SEO';
import { VenueSchema } from '../components/seo/JsonLd';
import { useRecentlyViewed } from '../hooks/useRecentlyViewed';

const amenityIcons = {
  WiFi: Wifi,
  Kitchen: UtensilsCrossed,
  Parking: Car
};

export default function VenueDetail() {
  const { id } = useParams();
  const [selectedImage, setSelectedImage] = useState(0);
  const [venue, setVenue] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format time for display (convert 24h to 12h format)
  const formatTime = (time24h?: string) => {
    if (!time24h) return 'N/A';

    try {
      const [hours, minutes] = time24h.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours % 12 || 12;
      return `${hours12}${minutes > 0 ? `:${minutes.toString().padStart(2, '0')}` : ''} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return time24h;
    }
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  // Fetch venue data
  useEffect(() => {
    const fetchVenue = async () => {
      if (!id) {
        setError('No venue ID provided');
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching venue with ID:', id);
        setLoading(true);

        // Try to get venue from API first
        let venueData = await getVenueById(id);

        // If not found in API, try to find in mock data
        if (!venueData) {
          console.log('🔍 Venue not found in API, searching mock data...');
          const mockVenue = mockVenues.find(v => v.id === id);
          if (mockVenue) {
            // Convert mock venue to the expected format
            venueData = {
              id: mockVenue.id,
              title: mockVenue.title || mockVenue.name, // Handle both title and name fields
              description: mockVenue.description,
              location: `${mockVenue.location.suburb}, ${mockVenue.location.state}`,
              price: mockVenue.pricing.hourlyRate,
              capacity: mockVenue.capacity.recommended,
              rating: mockVenue.host.rating,
              reviews: mockVenue.host.reviewCount,
              images: mockVenue.images,
              amenities: mockVenue.amenities,
              eventTypes: mockVenue.eventTypes || [mockVenue.venueType], // Use eventTypes if available
              host: {
                id: mockVenue.host.id || mockVenue.id + '_host',
                name: mockVenue.host.name,
                image: mockVenue.host.image || "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200",
                rating: mockVenue.host.rating
              },
              noiseRestrictions: {
                curfewTime: "22:00",
                councilArea: `${mockVenue.location.suburb} Council`,
                allowsOvernight: true,
                notes: "Please be mindful of neighbors after 10 PM. All loud music must move indoors after curfew.",
                windowsClosedAfter: "21:00",
                zoning: "residential",
                residentialProximity: "nearby",
                soundproofing: true,
                outdoorMusic: {
                  allowed: true,
                  until: "21:00"
                }
              },
              partyScore: {
                score: Math.round(mockVenue.partyScore / 10) || 8,
                factors: [
                  "Residential area with noise restrictions",
                  "Good soundproofing",
                  "Outdoor space available",
                  "Council permits in place"
                ]
              }
            };
            console.log('✅ Found venue in mock data:', venueData.title);
          }
        } else {
          console.log('✅ Found venue in API:', venueData.title);
        }

        if (venueData) {
          setVenue(venueData);

          // Add to recently viewed
          addToRecentlyViewed({
            id: venueData.id,
            title: venueData.title,
            location: venueData.location,
            price: venueData.price,
            image: venueData.images[0] || ''
          });
        } else {
          setError('Venue not found');
        }
      } catch (err) {
        console.error('❌ Error fetching venue:', err);
        setError('Failed to load venue details');
      } finally {
        setLoading(false);
      }
    };

    fetchVenue();
  }, [id]);

  const { user } = useUser();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [useEnhancedFlow, setUseEnhancedFlow] = useState(true); // Toggle for enhanced booking flow
  const { addToRecentlyViewed } = useRecentlyViewed();

  // Booking API function is imported at the top of the file
  // Email service is not needed at this stage since we're just redirecting to the booking review page

  const handleBooking = async (bookingData: BookingData) => {
    // Check if user is logged in
    if (!user) {
      // Redirect to login with return URL
      navigate(`/login?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setIsSubmitting(true);
    setBookingError(null);

    try {
      // Redirect to booking review page with booking data and venue info
      navigate('/booking-review', {
        state: {
          bookingData,
          venue: venue
        }
      });
    } catch (err) {
      console.error('Booking failed:', err);
      setBookingError('Failed to process booking. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle enhanced booking flow completion
  const handleEnhancedBookingComplete = (bookingId: string) => {
    navigate(`/booking-confirmation/${bookingId}`);
  };

  // Handle enhanced booking flow cancellation
  const handleEnhancedBookingCancel = () => {
    setUseEnhancedFlow(false); // Fall back to regular flow
  };

  // Show loading state
  if (loading) {
    return (
      <div className="pt-32 px-4 sm:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="pt-32 px-4 sm:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Venue Not Found</h1>
            <p className="text-gray-600 mb-8">{error}</p>
            <button
              onClick={() => navigate('/find-venues')}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Browse Other Venues
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show venue not found state
  if (!venue) {
    return (
      <div className="pt-32 px-4 sm:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Venue Not Found</h1>
            <p className="text-gray-600 mb-8">The venue you're looking for doesn't exist or has been removed.</p>
            <button
              onClick={() => navigate('/find-venues')}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Browse Other Venues
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 px-4 sm:px-6">
      <SEO
        title={`${venue.title} - Party Venue in ${venue.location} | HouseGoing`}
        description={`${venue.description.substring(0, 150)}... Book this venue for up to ${venue.capacity} guests. ${venue.amenities.join(', ')}.`}
        image={venue.images[0]}
        url={`https://housegoing.com.au/venue/${id}`}
        type="product"
      />

      <VenueSchema
        name={venue.title}
        description={venue.description}
        image={venue.images[0]}
        pricePerHour={venue.price}
        address={venue.location}
        maxGuests={venue.capacity}
        amenities={venue.amenities}
        url={`https://housegoing.com.au/venue/${id}`}
      />

      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Image Gallery */}
          <div className="space-y-4">
            <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
              <img
                src={venue.images[selectedImage]}
                alt={venue.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              {venue.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-w-16 aspect-h-9 rounded-lg overflow-hidden ${
                    selectedImage === index ? 'ring-2 ring-purple-600' : ''
                  }`}
                >
                  <img
                    src={image}
                    alt={`${venue.title} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Venue Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{venue.title}</h1>
              <div className="flex items-center gap-4 text-gray-600">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-1" />
                  {venue.location}
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-1" />
                  Up to {venue.capacity} guests
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-400 fill-current" />
              <span className="font-semibold">{formatRating(venue.rating)}</span>
              <span className="text-gray-600">({venue.reviews} reviews)</span>
            </div>

            <p className="text-gray-600">{venue.description}</p>

            <div>
              <h2 className="text-xl font-semibold mb-4">Amenities</h2>
              <div className="grid grid-cols-2 gap-4">
                {venue.amenities.map((amenity) => {
                  const Icon = amenityIcons[amenity as keyof typeof amenityIcons];
                  return (
                    <div key={amenity} className="flex items-center gap-2">
                      {Icon && <Icon className="h-5 w-5 text-gray-600" />}
                      <span>{amenity}</span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Party Score Section */}
            {venue.partyScore && (
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-purple-800">Party Score</h3>
                  <div className="bg-white rounded-full h-12 w-12 flex items-center justify-center border-2 border-purple-200">
                    <span className="text-lg font-bold text-purple-700">{venue.partyScore.score.toFixed(1)}</span>
                  </div>
                </div>
                <ul className="space-y-1">
                  {venue.partyScore.factors.map((factor, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      {factor}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Noise Restrictions Section */}
            {venue.noiseRestrictions && (
              <div className="bg-amber-50 rounded-lg p-4 border border-amber-100">
                <h3 className="font-semibold text-amber-800 mb-3 flex items-center">
                  <Volume2 className="h-5 w-5 mr-2 text-amber-600" />
                  Noise Restrictions
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {venue.noiseRestrictions.curfewTime && (
                    <div className="flex items-start">
                      <Clock className="h-4 w-4 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">Noise Curfew</p>
                        <p className="text-sm text-amber-700">
                          {formatTime(venue.noiseRestrictions.curfewTime)}
                        </p>
                      </div>
                    </div>
                  )}

                  {venue.noiseRestrictions.outdoorMusic && (
                    <div className="flex items-start">
                      <Music2 className="h-4 w-4 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">Outdoor Music</p>
                        <p className="text-sm text-amber-700">
                          {venue.noiseRestrictions.outdoorMusic.allowed
                            ? `Allowed until ${formatTime(venue.noiseRestrictions.outdoorMusic.until)}`
                            : 'Not allowed outdoors'}
                        </p>
                      </div>
                    </div>
                  )}

                  {venue.noiseRestrictions.allowsOvernight !== undefined && (
                    <div className="flex items-start">
                      <Moon className="h-4 w-4 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">Overnight Stays</p>
                        <p className="text-sm text-amber-700">
                          {venue.noiseRestrictions.allowsOvernight ? 'Guests may stay overnight' : 'All guests must leave by the end time'}
                        </p>
                      </div>
                    </div>
                  )}

                  {venue.noiseRestrictions.soundproofing !== undefined && (
                    <div className="flex items-start">
                      <Volume2 className="h-4 w-4 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">Soundproofing</p>
                        <p className="text-sm text-amber-700">
                          {venue.noiseRestrictions.soundproofing ? 'Venue has soundproofing' : 'No soundproofing'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {venue.noiseRestrictions.notes && (
                  <div className="mt-4 pt-3 border-t border-amber-200">
                    <div className="flex items-start">
                      <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800">Additional Information</p>
                        <p className="text-sm text-amber-700 mt-1">{venue.noiseRestrictions.notes}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Messaging Section */}
            <div className="border-t pt-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Contact Host</h2>
              <BookingMessagingIntegration
                hostId={venue.host?.id || venue.id + '_host'}
                hostName={venue.host?.name || 'Host'}
                venueName={venue.title}
                venueId={venue.id}
                mode="inquiry"
                className="mb-6"
              />
            </div>

            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Book this venue</h2>
                <div className="flex items-center space-x-4">
                  <div className="text-sm text-gray-600">
                    💳 Secure payment • 📧 Instant confirmation
                  </div>
                  <button
                    onClick={() => setUseEnhancedFlow(!useEnhancedFlow)}
                    className="text-sm text-purple-600 hover:text-purple-700"
                  >
                    {useEnhancedFlow ? 'Use Simple Flow' : 'Use Enhanced Flow'}
                  </button>
                </div>
              </div>

              {bookingError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                  {bookingError}
                </div>
              )}

              {useEnhancedFlow ? (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-sm text-blue-800 mb-3">
                    🚀 <strong>Enhanced Booking Flow:</strong> Complete your booking with integrated payment processing and instant confirmation.
                  </p>
                  <button
                    onClick={() => navigate(`/enhanced-booking/${venue.id}`)}
                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium"
                  >
                    Book Now
                  </button>
                </div>
              ) : (
                <>
                  <BookingForm venue={venue} onSubmit={handleBooking} />
                  {isSubmitting && (
                    <div className="mt-4 flex justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
