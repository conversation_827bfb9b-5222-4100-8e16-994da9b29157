import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useAuth } from '../../providers/AuthProvider';
import { useClerkSupabase } from '../../hooks/useClerkSupabase';
import MessageNotifications from '../messaging/MessageNotifications';

interface HeaderAuthButtonsProps {
  className?: string;
  variant?: 'default' | 'owner';
}

export default function HeaderAuthButtons({ className = '', variant = 'default' }: HeaderAuthButtonsProps) {
  const { isAuthenticated, isLoading, user, signOut, refreshAuth } = useAuth();
  const { isReady: clerkSupabaseReady, isAuthenticated: clerkAuth } = useClerkSupabase();
  const [forceRerender, setForceRerender] = useState(0);

  // Enhanced auth state management with new Clerk-Supabase integration
  useEffect(() => {
    console.log('Header<PERSON>uth<PERSON>uttons - Auth state:', {
      isAuthenticated,
      isLoading,
      user: !!user,
      clerk<PERSON>upaba<PERSON><PERSON>eady,
      clerkAuth,
      userDetails: user ? {
        id: user.id,
        email: user.email,
        firstName: user.user_metadata?.first_name || user.firstName,
        lastName: user.user_metadata?.last_name || user.lastName
      } : null,
      // Add Clerk debugging info
      clerkUser: typeof window !== 'undefined' && window.Clerk?.user ? {
        id: window.Clerk.user.id,
        email: window.Clerk.user.primaryEmailAddress?.emailAddress,
        loaded: window.Clerk.loaded
      } : 'No Clerk user'
    });
  }, [isAuthenticated, isLoading, user, clerkSupabaseReady, clerkAuth]);

  // Check for Clerk user periodically if not authenticated
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      const checkClerkUser = () => {
        if (typeof window !== 'undefined' && window.Clerk?.user) {
          console.log('🔄 Found Clerk user, refreshing auth state...');
          refreshAuth();
        }
      };

      const interval = setInterval(checkClerkUser, 2000);
      return () => clearInterval(interval);
    }
  }, [isAuthenticated, isLoading, refreshAuth]);

  // Show a loading state instead of nothing
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="h-9 w-20 bg-gray-100 rounded-md animate-pulse"></div>
        <div className="h-9 w-20 bg-purple-100 rounded-md animate-pulse"></div>
      </div>
    );
  }

  // Determine the login/signup URLs based on the variant
  const loginUrl = variant === 'owner' ? '/host/login?userType=host' : '/login?userType=guest';
  const signupUrl = variant === 'owner' ? '/host/signup?userType=host' : '/signup?userType=guest';
  const accountUrl = variant === 'owner' ? '/host/dashboard' : '/my-account';

  const handleSignOut = async () => {
    try {
      // Our unified auth manager handles all sign out logic
      await signOut();

      if (variant === 'owner') {
        // Redirect to home page after signing out from owner portal
        window.location.href = '/';
      } else {
        // Force page reload to update UI
        window.location.reload();
      }
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Show authenticated state - our auth manager provides clean state
  if (isAuthenticated && user) {
    // Get user name from the authenticated user object
    let userName = 'User';
    let userFullNameDisplay = '';

    // Extract user information from the user object (handle both Clerk and Supabase formats)
    const firstName = user.user_metadata?.first_name ||
                     user.user_metadata?.firstName ||
                     user.firstName;
    const lastName = user.user_metadata?.last_name ||
                    user.user_metadata?.lastName ||
                    user.lastName;
    const email = user.email || user.primaryEmailAddress?.emailAddress;

    // Set the full name for display
    if (firstName && lastName) {
      userFullNameDisplay = `${firstName} ${lastName}`;
    } else if (firstName) {
      userFullNameDisplay = firstName;
    } else if (email) {
      userFullNameDisplay = email.split('@')[0];
    }

    // Set the short name for the button
    if (firstName) {
      userName = firstName;
    } else if (email) {
      userName = email.split('@')[0];
    }

    console.log('✅ HeaderAuthButtons: Showing authenticated state for user:', userName);

    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {/* Message Notifications - only show for regular users, not owners */}
        {variant === 'default' && <MessageNotifications />}

        <Link to={accountUrl}>
          <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm flex items-center">
            <User className="w-4 h-4 mr-1.5 text-purple-600" />
            <span>{variant === 'owner' ? 'Dashboard' : 'My Account'}</span>
          </button>
        </Link>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Enhanced debug info - remove after fixing */}
      {typeof window !== 'undefined' && window.Clerk?.user && (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => {
              console.log('🔄 Manual auth refresh triggered');
              console.log('Clerk user:', window.Clerk?.user);
              console.log('Clerk loaded:', window.Clerk?.loaded);
              refreshAuth();
            }}
            className="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded"
            title="Refresh Auth (Debug)"
          >
            🔄 Refresh
          </button>
          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
            Clerk user found but not authenticated!
          </span>
        </div>
      )}
      <Link to={loginUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors flex items-center shadow-sm">
          <User className="w-4 h-4 mr-1.5" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to={signupUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 transition-colors shadow-sm">
          Sign Up
        </button>
      </Link>
    </div>
  );
}
