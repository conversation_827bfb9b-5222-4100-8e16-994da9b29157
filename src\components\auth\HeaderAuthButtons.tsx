import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useAuth } from '../../providers/AuthProvider';
import MessageNotifications from '../messaging/MessageNotifications';

interface HeaderAuthButtonsProps {
  className?: string;
  variant?: 'default' | 'owner';
}

export default function HeaderAuthButtons({ className = '', variant = 'default' }: HeaderAuthButtonsProps) {
  const { isAuthenticated, isLoading, user, signOut } = useAuth();

  // Debug auth state
  useEffect(() => {
    console.log('HeaderAuthButtons - Auth state:', {
      isAuthenticated,
      isLoading,
      user: !!user,
      userDetails: user ? {
        id: user.id,
        email: user.email,
        user_metadata: user.user_metadata
      } : null
    });
  }, [isAuthenticated, isLoading, user]);

  // Show a loading state instead of nothing
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="h-9 w-20 bg-gray-100 rounded-md animate-pulse"></div>
        <div className="h-9 w-20 bg-purple-100 rounded-md animate-pulse"></div>
      </div>
    );
  }

  // Determine the login/signup URLs based on the variant
  const loginUrl = variant === 'owner' ? '/login?type=host' : '/login';
  const signupUrl = variant === 'owner' ? '/signup?type=host' : '/signup';
  const accountUrl = variant === 'owner' ? '/host/dashboard' : '/my-account';

  const handleSignOut = async () => {
    try {
      await signOut();
      console.log('✅ Successfully signed out');

      // Redirect based on variant
      if (variant === 'owner') {
        window.location.href = '/';
      } else {
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Show authenticated state
  if (isAuthenticated && user) {
    // Extract user information from Supabase user object
    const firstName = user.user_metadata?.first_name || user.user_metadata?.full_name?.split(' ')[0];
    const lastName = user.user_metadata?.last_name || user.user_metadata?.full_name?.split(' ')[1];
    const email = user.email;

    // Determine display name
    let displayName = 'User';
    if (firstName) {
      displayName = firstName;
    } else if (email) {
      displayName = email.split('@')[0];
    }

    console.log('✅ HeaderAuthButtons: Showing authenticated state for user:', displayName);

    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {/* Message Notifications - only show for regular users, not owners */}
        {variant === 'default' && <MessageNotifications />}

        {/* User Account Button */}
        <Link to={accountUrl}>
          <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm flex items-center">
            <User className="w-4 h-4 mr-1.5 text-purple-600" />
            <span>{variant === 'owner' ? 'Dashboard' : 'My Account'}</span>
          </button>
        </Link>

        {/* Sign Out Button */}
        <button
          onClick={handleSignOut}
          className="text-sm font-medium px-5 py-2.5 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors shadow-sm flex items-center"
        >
          <LogOut className="w-4 h-4 mr-1.5" />
          <span>Sign Out</span>
        </button>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Link to={loginUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors flex items-center shadow-sm">
          <User className="w-4 h-4 mr-1.5" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to={signupUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 transition-colors shadow-sm">
          Sign Up
        </button>
      </Link>
    </div>
  );
}
