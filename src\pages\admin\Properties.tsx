import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@clerk/clerk-react';
import {
  Building,
  Search,
  Filter,
  RefreshCw,
  Edit,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Calendar,
  MapPin,
  User,
  ChevronDown,
  ChevronUp,
  MessageSquare
} from 'lucide-react';
import { supabase } from '../../lib/supabase';

// DIRECT OVERRIDE: List of admin emails
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

export default function AdminProperties() {
  const { user, isLoaded } = useAuth();
  const navigate = useNavigate();

  // State for properties
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');

  // State for property details
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [showPropertyDetails, setShowPropertyDetails] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');

  // Check if user is admin
  const userEmail = user?.primaryEmailAddress?.emailAddress || '';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAdmin = isDevelopment || ADMIN_EMAILS.includes(userEmail.toLowerCase());

  // Fetch properties
  useEffect(() => {
    if (!isLoaded) return;

    if (!isAdmin) {
      navigate('/unauthorized');
      return;
    }

    fetchProperties();
  }, [isLoaded, isAdmin, navigate, statusFilter, sortBy, sortDirection]);

  // Fetch properties from Supabase
  const fetchProperties = async () => {
    setLoading(true);

    try {
      let query = supabase
        .from('property_submissions')
        .select(`
          *,
          profiles:ownerId (
            email,
            full_name
          )
        `)
        .order(sortBy, { ascending: sortDirection === 'asc' });

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching properties:', error);
        return;
      }

      // Filter by search term if provided
      const filteredData = searchTerm
        ? data.filter(property =>
            property.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            property.address?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            property.profiles?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            property.type?.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : data;

      setProperties(filteredData);
    } catch (error) {
      console.error('Exception fetching properties:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to transform noise restrictions from property submission to venue format
  const transformNoiseRestrictions = (propertyData) => {
    // If there are no noise restrictions, return null
    if (!propertyData.noiseRestrictions && !propertyData.curfew && !propertyData.endTime) {
      return null;
    }

    // Create the noise restrictions object in the format expected by the venue card
    const noiseRestrictions = {
      notes: propertyData.noiseRestrictions || '',
      allowsOvernight: false // Default value
    };

    // Extract council area if available
    const councilAreaMatch = propertyData.noiseRestrictions?.match(/zone \((.*?)\)/i);
    if (councilAreaMatch && councilAreaMatch[1]) {
      noiseRestrictions.councilArea = councilAreaMatch[1].trim();
    }

    // Add curfew time (use weekday curfew by default)
    if (propertyData.curfew && propertyData.curfew.weekday && propertyData.curfew.weekday.start) {
      noiseRestrictions.curfewTime = propertyData.curfew.weekday.start;
    } else if (propertyData.endTime && propertyData.endTime.weekday) {
      noiseRestrictions.curfewTime = propertyData.endTime.weekday;
    }

    // Add outdoor music restrictions
    if (propertyData.outdoorCutoff && (propertyData.outdoorCutoff.weekday || propertyData.outdoorCutoff.weekend)) {
      noiseRestrictions.outdoorMusic = {
        allowed: true,
        until: propertyData.outdoorCutoff.weekday || propertyData.outdoorCutoff.weekend
      };
    } else {
      // Default to no outdoor music if not specified
      noiseRestrictions.outdoorMusic = {
        allowed: false
      };
    }

    // Add windows closed after time if bass restriction is available
    if (propertyData.bassRestriction && propertyData.bassRestriction.weekday) {
      noiseRestrictions.windowsClosedAfter = propertyData.bassRestriction.weekday;
    }

    // Add zoning information if available
    if (propertyData.noiseRestrictions) {
      if (propertyData.noiseRestrictions.toLowerCase().includes('residential')) {
        noiseRestrictions.zoning = 'residential';
        noiseRestrictions.residentialProximity = 'adjacent';
      } else if (propertyData.noiseRestrictions.toLowerCase().includes('commercial')) {
        noiseRestrictions.zoning = 'commercial';
        noiseRestrictions.residentialProximity = 'distant';
      } else if (propertyData.noiseRestrictions.toLowerCase().includes('industrial')) {
        noiseRestrictions.zoning = 'industrial';
        noiseRestrictions.residentialProximity = 'distant';
      } else if (propertyData.noiseRestrictions.toLowerCase().includes('mixed')) {
        noiseRestrictions.zoning = 'mixed';
        noiseRestrictions.residentialProximity = 'nearby';
      }
    }

    return noiseRestrictions;
  };

  // Helper function to transform party score from property submission to venue format
  const transformPartyScore = (propertyData) => {
    // If there's no noise restrictions information, try to generate a party score based on other data
    if (!propertyData.noiseRestrictions) {
      // Generate a basic party score based on available data
      let score = 5; // Default middle score
      const factors = [];

      // Adjust score based on property type
      if (propertyData.type === 'house') {
        score += 1;
        factors.push('Standalone house with more privacy');
      } else if (propertyData.type === 'apartment') {
        score -= 1;
        factors.push('Apartment with shared walls');
      } else if (propertyData.type === 'commercial') {
        score += 2;
        factors.push('Commercial space with fewer residential neighbors');
      }

      // Adjust score based on curfew times
      if (propertyData.curfew && propertyData.curfew.weekday && propertyData.curfew.weekday.start) {
        const curfewHour = parseInt(propertyData.curfew.weekday.start.split(':')[0], 10);
        if (curfewHour < 22) {
          score -= 2;
          factors.push('Early noise curfew');
        } else if (curfewHour >= 23) {
          score += 1;
          factors.push('Late noise curfew');
        }
      }

      // Adjust score based on outdoor space
      if (propertyData.outdoorCutoff) {
        score += 1;
        factors.push('Outdoor space available');
      }

      // Ensure score is within 1-10 range
      score = Math.max(1, Math.min(10, score));

      return {
        score,
        factors
      };
    }

    // Try to extract party score from the noise restrictions text
    const scoreMatch = propertyData.noiseRestrictions.match(/Party Score:?\s*(\d+)\/10/i);
    if (scoreMatch) {
      const score = parseInt(scoreMatch[1], 10);

      // Create factors based on the noise restrictions text
      const factors = [];

      if (propertyData.noiseRestrictions.toLowerCase().includes('residential')) {
        factors.push('Residential area with noise restrictions');
      }

      if (propertyData.noiseRestrictions.toLowerCase().includes('commercial')) {
        factors.push('Commercial area with more flexible noise rules');
      }

      if (propertyData.noiseRestrictions.toLowerCase().includes('industrial')) {
        factors.push('Industrial area with minimal noise restrictions');
      }

      if (propertyData.outdoorCutoff) {
        factors.push('Outdoor space available with time restrictions');
      }

      // Add curfew factor if available
      if (propertyData.curfew && propertyData.curfew.weekday && propertyData.curfew.weekday.start) {
        const curfewHour = parseInt(propertyData.curfew.weekday.start.split(':')[0], 10);
        if (curfewHour < 22) {
          factors.push('Early noise curfew at ' + propertyData.curfew.weekday.start);
        } else if (curfewHour >= 23) {
          factors.push('Late noise curfew at ' + propertyData.curfew.weekday.start);
        } else {
          factors.push('Standard noise curfew at ' + propertyData.curfew.weekday.start);
        }
      }

      return {
        score,
        factors
      };
    }

    // If no explicit party score, generate one based on the noise restrictions
    let score = 5; // Default middle score
    const factors = [];

    // Adjust score based on zoning mentioned in noise restrictions
    if (propertyData.noiseRestrictions.toLowerCase().includes('residential')) {
      score -= 1;
      factors.push('Residential area with noise restrictions');
    }

    if (propertyData.noiseRestrictions.toLowerCase().includes('commercial')) {
      score += 2;
      factors.push('Commercial area with more flexible noise rules');
    }

    if (propertyData.noiseRestrictions.toLowerCase().includes('industrial')) {
      score += 3;
      factors.push('Industrial area with minimal noise restrictions');
    }

    // Adjust score based on curfew times
    if (propertyData.curfew && propertyData.curfew.weekday && propertyData.curfew.weekday.start) {
      const curfewHour = parseInt(propertyData.curfew.weekday.start.split(':')[0], 10);
      if (curfewHour < 22) {
        score -= 2;
        factors.push('Early noise curfew at ' + propertyData.curfew.weekday.start);
      } else if (curfewHour >= 23) {
        score += 1;
        factors.push('Late noise curfew at ' + propertyData.curfew.weekday.start);
      } else {
        factors.push('Standard noise curfew at ' + propertyData.curfew.weekday.start);
      }
    }

    // Adjust score based on outdoor space
    if (propertyData.outdoorCutoff) {
      score += 1;
      factors.push('Outdoor space available with time restrictions');
    }

    // Ensure score is within 1-10 range
    score = Math.max(1, Math.min(10, score));

    return {
      score,
      factors
    };
  };

  // Handle property approval
  const handleApproveProperty = async (propertyId) => {
    try {
      // First, get the property details
      const { data: propertyData, error: fetchError } = await supabase
        .from('property_submissions')
        .select('*')
        .eq('id', propertyId)
        .single();

      if (fetchError) {
        console.error('Error fetching property details:', fetchError);
        return;
      }

      // Transform noise restrictions and party score
      const noiseRestrictions = transformNoiseRestrictions(propertyData);
      const partyScore = transformPartyScore(propertyData);

      // Create a new venue entry in the venues table
      const { data: venueData, error: venueError } = await supabase
        .from('venues')
        .insert({
          title: propertyData.name,
          description: propertyData.description || `A beautiful ${propertyData.type} venue`,
          location: propertyData.address,
          address: propertyData.address,
          host_id: propertyData.ownerId,
          price_per_hour: propertyData.price,
          capacity: propertyData.maxGuests,
          images: propertyData.images || [],
          amenities: propertyData.amenities || [],
          noiseRestrictions: noiseRestrictions, // Add noise restrictions (camelCase for JSON column)
          partyScore: partyScore, // Add party score (camelCase for JSON column)
          approval_status: 'approved',
          is_published: true,
          admin_notes: adminNotes,
          approved_by: userEmail,
          approved_at: new Date().toISOString(),
          created_at: propertyData.created_at,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (venueError) {
        console.error('Error creating venue:', venueError);
        return;
      }

      // Update the property submission status
      const { error } = await supabase
        .from('property_submissions')
        .update({
          status: 'approved',
          is_published: true,
          admin_notes: adminNotes,
          approved_by: userEmail,
          approved_at: new Date().toISOString(),
          venue_id: venueData.id // Link to the created venue
        })
        .eq('id', propertyId);

      if (error) {
        console.error('Error updating property submission:', error);
        return;
      }

      // Refresh properties
      fetchProperties();
      setShowPropertyDetails(false);
    } catch (error) {
      console.error('Exception approving property:', error);
    }
  };

  // Handle property rejection
  const handleRejectProperty = async (propertyId) => {
    try {
      const { error } = await supabase
        .from('property_submissions')
        .update({
          status: 'rejected',
          is_published: false,
          admin_notes: adminNotes,
          approved_by: userEmail,
          approved_at: new Date().toISOString()
        })
        .eq('id', propertyId);

      if (error) {
        console.error('Error rejecting property:', error);
        return;
      }

      // Refresh properties
      fetchProperties();
      setShowPropertyDetails(false);
    } catch (error) {
      console.error('Exception rejecting property:', error);
    }
  };

  // Handle request for revisions
  const handleRequestRevisions = async (propertyId) => {
    try {
      const { error } = await supabase
        .from('property_submissions')
        .update({
          status: 'revision_requested',
          is_published: false,
          admin_notes: adminNotes,
          approved_by: userEmail,
          approved_at: new Date().toISOString()
        })
        .eq('id', propertyId);

      if (error) {
        console.error('Error requesting revisions:', error);
        return;
      }

      // Refresh properties
      fetchProperties();
      setShowPropertyDetails(false);
    } catch (error) {
      console.error('Exception requesting revisions:', error);
    }
  };

  // Handle scheduling inspection
  const handleScheduleInspection = async (propertyId, inspectionDate) => {
    try {
      const { error } = await supabase
        .from('property_submissions')
        .update({
          inspection_scheduled: true,
          inspection_date: inspectionDate
        })
        .eq('id', propertyId);

      if (error) {
        console.error('Error scheduling inspection:', error);
        return;
      }

      // Refresh properties
      fetchProperties();
    } catch (error) {
      console.error('Exception scheduling inspection:', error);
    }
  };

  // Handle property selection
  const handleSelectProperty = (property) => {
    setSelectedProperty(property);
    setAdminNotes(property.admin_notes || '');
    setShowPropertyDetails(true);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';

    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'revision_requested':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  if (!isLoaded) {
    return <div className="pt-32 flex justify-center">Loading...</div>;
  }

  if (!isAdmin) {
    return <div className="pt-32 flex justify-center">Unauthorized</div>;
  }

  return (
    <div className="pt-32 px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Property Listings</h1>
          <p className="mt-2 text-sm text-gray-700">
            Review and approve property listings submitted by hosts.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            type="button"
            onClick={fetchProperties}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-purple-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 sm:w-auto"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6 flex flex-col sm:flex-row gap-4">
        <div className="relative rounded-md shadow-sm flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="focus:ring-purple-500 focus:border-purple-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
            placeholder="Search properties..."
          />
        </div>

        <div className="w-full sm:w-64">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="revision_requested">Revision Requested</option>
          </select>
        </div>
      </div>

      {/* Properties Table */}
      <div className="mt-8 flex flex-col">
        <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                    >
                      <button
                        className="group inline-flex"
                        onClick={() => handleSort('title')}
                      >
                        Property
                        <span className="ml-2 flex-none rounded text-gray-400">
                          {sortBy === 'title' ? (
                            sortDirection === 'asc' ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )
                          ) : (
                            <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-100" />
                          )}
                        </span>
                      </button>
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                    >
                      <button
                        className="group inline-flex"
                        onClick={() => handleSort('location')}
                      >
                        Location
                        <span className="ml-2 flex-none rounded text-gray-400">
                          {sortBy === 'location' ? (
                            sortDirection === 'asc' ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )
                          ) : (
                            <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-100" />
                          )}
                        </span>
                      </button>
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                    >
                      Host
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                    >
                      <button
                        className="group inline-flex"
                        onClick={() => handleSort('status')}
                      >
                        Status
                        <span className="ml-2 flex-none rounded text-gray-400">
                          {sortBy === 'status' ? (
                            sortDirection === 'asc' ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )
                          ) : (
                            <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-100" />
                          )}
                        </span>
                      </button>
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                    >
                      <button
                        className="group inline-flex"
                        onClick={() => handleSort('created_at')}
                      >
                        Submitted
                        <span className="ml-2 flex-none rounded text-gray-400">
                          {sortBy === 'created_at' ? (
                            sortDirection === 'asc' ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )
                          ) : (
                            <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-100" />
                          )}
                        </span>
                      </button>
                    </th>
                    <th
                      scope="col"
                      className="relative py-3.5 pl-3 pr-4 sm:pr-6"
                    >
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="py-10 text-center text-gray-500">
                        <div className="flex justify-center">
                          <RefreshCw className="h-8 w-8 animate-spin text-purple-500" />
                        </div>
                        <div className="mt-2">Loading properties...</div>
                      </td>
                    </tr>
                  ) : properties.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="py-10 text-center text-gray-500">
                        No properties found
                      </td>
                    </tr>
                  ) : (
                    properties.map((property) => (
                      <tr key={property.id} className="hover:bg-gray-50">
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                          {property.name}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                            {property.address}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1 text-gray-400" />
                            {property.profiles?.email || 'Unknown'}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(property.status)}`}>
                            {property.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                            {property.status === 'approved' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {property.status === 'rejected' && <XCircle className="h-3 w-3 mr-1" />}
                            {property.status === 'revision_requested' && <AlertTriangle className="h-3 w-3 mr-1" />}
                            {property.status.replace('_', ' ')}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                            {formatDate(property.created_at)}
                          </div>
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <button
                            onClick={() => handleSelectProperty(property)}
                            className="text-purple-600 hover:text-purple-900"
                          >
                            Review<span className="sr-only">, {property.title}</span>
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Property Details Modal */}
      {showPropertyDetails && selectedProperty && (
        <div className="fixed inset-0 overflow-y-auto z-50">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Property Review: {selectedProperty.title}
                  </h3>

                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Property Details */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Property Details
                      </h4>

                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Name</p>
                          <p className="mt-1">{selectedProperty.name}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Description</p>
                          <p className="mt-1">{selectedProperty.description || 'No description provided'}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Address</p>
                          <p className="mt-1">{selectedProperty.address}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Type</p>
                          <p className="mt-1">{selectedProperty.type}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Price</p>
                          <p className="mt-1">${selectedProperty.price} per hour</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Max Guests</p>
                          <p className="mt-1">{selectedProperty.maxGuests} guests</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Size</p>
                          <p className="mt-1">{selectedProperty.size} m²</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Function Rooms</p>
                          <p className="mt-1">{selectedProperty.functionRooms || 0}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Event Spaces</p>
                          <p className="mt-1">{selectedProperty.eventSpaces || 0}</p>
                        </div>
                      </div>
                    </div>

                    {/* Host & Images */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Host Information
                      </h4>

                      <div className="bg-gray-50 p-4 rounded-md mb-6">
                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Owner ID</p>
                          <p className="mt-1">{selectedProperty.ownerId || 'Unknown'}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Email</p>
                          <p className="mt-1">{selectedProperty.profiles?.email || 'Unknown'}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Name</p>
                          <p className="mt-1">{selectedProperty.profiles?.full_name || 'Unknown'}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Submitted</p>
                          <p className="mt-1">{formatDate(selectedProperty.created_at)}</p>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-500">Status</p>
                          <p className="mt-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(selectedProperty.status)}`}>
                              {selectedProperty.status?.replace('_', ' ') || 'Unknown'}
                            </span>
                          </p>
                        </div>
                      </div>

                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Images
                      </h4>

                      <div className="bg-gray-50 p-4 rounded-md mb-6">
                        <div className="grid grid-cols-2 gap-2">
                          {selectedProperty.images && selectedProperty.images.length > 0 ? (
                            selectedProperty.images.map((image, index) => (
                              <div key={index} className="aspect-w-16 aspect-h-9">
                                <img
                                  src={image}
                                  alt={`Property ${index + 1}`}
                                  className="object-cover rounded-md w-full h-full"
                                  onClick={() => window.open(image, '_blank')}
                                  style={{ cursor: 'pointer' }}
                                />
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-gray-500">No images provided</p>
                          )}
                        </div>
                      </div>

                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Bank Details
                      </h4>

                      <div className="bg-gray-50 p-4 rounded-md">
                        {selectedProperty.bankDetails ? (
                          <>
                            <div className="mb-4">
                              <p className="text-sm font-medium text-gray-500">Account Name</p>
                              <p className="mt-1">{selectedProperty.bankDetails.accountName}</p>
                            </div>

                            <div className="mb-4">
                              <p className="text-sm font-medium text-gray-500">BSB</p>
                              <p className="mt-1">{selectedProperty.bankDetails.bsb}</p>
                            </div>

                            <div className="mb-4">
                              <p className="text-sm font-medium text-gray-500">Account Number</p>
                              <p className="mt-1">{selectedProperty.bankDetails.accountNumber}</p>
                            </div>

                            <div className="mb-4">
                              <p className="text-sm font-medium text-gray-500">Bank Name</p>
                              <p className="mt-1">{selectedProperty.bankDetails.bankName}</p>
                            </div>
                          </>
                        ) : (
                          <p className="text-sm text-gray-500">No bank details provided</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Admin Notes */}
                  <div className="mt-6">
                    <label htmlFor="admin-notes" className="block text-sm font-medium text-gray-700">
                      Admin Notes
                    </label>
                    <div className="mt-1">
                      <textarea
                        id="admin-notes"
                        name="admin-notes"
                        rows={3}
                        className="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="Add notes about this property..."
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => handleApproveProperty(selectedProperty.id)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </button>
                <button
                  type="button"
                  onClick={() => handleRequestRevisions(selectedProperty.id)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Request Revisions
                </button>
                <button
                  type="button"
                  onClick={() => handleRejectProperty(selectedProperty.id)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </button>
                <button
                  type="button"
                  onClick={() => setShowPropertyDetails(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
