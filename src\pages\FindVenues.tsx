import React, { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import SearchBar from '../components/SearchBar';
import VenueFilters from '../components/venues/VenueFilters';
import VenueSort from '../components/venues/VenueSort';
import VenueGrid from '../components/venues/VenueGrid';
import SmartVenueSearch from '../components/search/SmartVenueSearch';
import VenueAssistant from '../components/chat/VenueAssistant';

import { getVenues, VenueSearchParams } from '../api/venues';

// Mock data - replace with API call
const mockVenues = [
  {
    id: "1",
    title: "Beachfront Party Paradise",
    description: "Stunning beachfront venue perfect for weddings and corporate events with panoramic ocean views.",
    location: "Bondi Beach, NSW",
    price: 1200,
    capacity: 50,
    rating: 4.9,
    reviews: 128,
    images: ["https://images.unsplash.com/photo-1533929736458-ca588d08c8be?auto=format&fit=crop&w=800"],
    amenities: ["WiFi", "Kitchen", "Parking"],
    eventTypes: ["Wedding", "Corporate"],
    host: {
      id: "h1",
      name: "Sarah <PERSON>",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200",
      rating: 4.8
    }
  },
  {
    id: "2",
    title: "Modern Rooftop Lounge",
    description: "Elegant rooftop venue with city skyline views, perfect for sophisticated events.",
    location: "Sydney, NSW",
    price: 1500,
    capacity: 80,
    rating: 4.8,
    reviews: 96,
    images: ["https://images.unsplash.com/photo-1566737236500-c8ac43014a67?auto=format&fit=crop&w=800"],
    amenities: ["WiFi", "Sound System", "Catering"],
    eventTypes: ["Corporate", "Social"],
    host: {
      id: "h2",
      name: "Michael Chen",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=200",
      rating: 4.9
    }
  }
];

export default function FindVenues() {
  const location = useLocation();
  const [venues, setVenues] = useState([]);
  const [sortBy, setSortBy] = useState('recommended');
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [searchParams, setSearchParams] = useState({
    location: '',
    lat: null,
    lng: null,
    startDate: '',
    endDate: '',
    guests: 0,
    budget: 0
  });
  const [currentFilters, setCurrentFilters] = useState<any>({});
  const [smartSearchResults, setSmartSearchResults] = useState<any[]>([]);
  const [showSmartResults, setShowSmartResults] = useState(false);
  const [initialSmartQuery, setInitialSmartQuery] = useState<string>('');

  // Store selected location for radius filtering
  const [selectedLocation, setSelectedLocation] = useState<{ lat: number; lng: number; displayName: string } | null>(null);

  // Handle smart search results
  const handleSmartVenuesFound = useCallback((smartVenues: any[]) => {
    console.log('🔍 FIND VENUES: Received smart search results:', smartVenues.length, 'venues');
    setSmartSearchResults(smartVenues);
    setShowSmartResults(true);
    // Optionally hide the regular search results when smart search is used
    setVenues([]);
  }, []);

  const handleFilterChange = async (filters: any) => {
    console.log('Filters changed:', filters);
    setCurrentFilters(filters);

    // Apply filters and refetch venues
    setIsLoading(true);
    try {
      // Convert search params to API params
      const apiParams: VenueSearchParams = {
        location: searchParams.location,
        lat: searchParams.lat,
        lng: searchParams.lng,
        startDate: searchParams.startDate,
        endDate: searchParams.endDate,
        guests: searchParams.guests,
        budget: searchParams.budget,
        // Add filter parameters
        eventTypes: filters.eventTypes?.length > 0 ? filters.eventTypes : undefined,
        amenities: filters.amenities?.length > 0 ? filters.amenities : undefined
      };

      // Parse price range filter
      if (filters.priceRange) {
        const [min, max] = filters.priceRange.split('-').map(Number);
        if (!isNaN(min)) apiParams.minPrice = min;
        if (!isNaN(max)) apiParams.maxPrice = max;
        else if (filters.priceRange.includes('+')) {
          apiParams.minPrice = min;
        }
      }

      // Parse guest range filter
      if (filters.guestRange) {
        const [min, max] = filters.guestRange.split('-').map(Number);
        if (!isNaN(min)) apiParams.minCapacity = min;
        if (!isNaN(max)) apiParams.maxCapacity = max;
        else if (filters.guestRange.includes('+')) {
          apiParams.minCapacity = min;
        }
      }

      // Only include parameters that have values
      Object.keys(apiParams).forEach(key => {
        if (apiParams[key] === '' || apiParams[key] === 0 || apiParams[key] === null || apiParams[key] === undefined) {
          delete apiParams[key];
        }
      });

      console.log('🔍 FILTER DEBUG: Applying filters with params:', apiParams);

      // Fetch venues with filters
      const venueResults = await getVenues(apiParams);
      console.log('🔍 FILTER DEBUG: Received filtered venues:', venueResults.length, 'venues');
      setVenues(venueResults);
    } catch (error) {
      console.error('Error applying filters:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy);
    // Implement sort logic here
    console.log('Sort changed:', newSortBy);
  };

  // Parse search parameters from URL and fetch venues
  useEffect(() => {
    const params = new URLSearchParams(location.search);

    const newSearchParams = {
      location: params.get('location') || '',
      lat: params.get('lat') ? parseFloat(params.get('lat')) : null,
      lng: params.get('lng') ? parseFloat(params.get('lng')) : null,
      startDate: params.get('startDate') || '',
      endDate: params.get('endDate') || '',
      guests: params.get('guests') ? parseInt(params.get('guests')) : 0,
      budget: params.get('budget') ? parseInt(params.get('budget')) : 0
    };

    setSearchParams(newSearchParams);

    // Handle smart search query from homepage
    if (location.state?.smartSearch) {
      console.log('🏠 HOMEPAGE SMART SEARCH: Received query from homepage:', location.state.smartSearch);
      setInitialSmartQuery(location.state.smartSearch);
    }

    // Update selected location for radius filtering
    if (newSearchParams.lat && newSearchParams.lng && newSearchParams.location) {
      setSelectedLocation({
        lat: newSearchParams.lat,
        lng: newSearchParams.lng,
        displayName: newSearchParams.location
      });
    } else {
      setSelectedLocation(null);
    }

    // Fetch venues from API with search parameters
    const fetchVenues = async () => {
      setIsLoading(true);
      try {
        // Convert search params to API params
        const apiParams: VenueSearchParams = {
          location: newSearchParams.location,
          lat: newSearchParams.lat,
          lng: newSearchParams.lng,
          startDate: newSearchParams.startDate,
          endDate: newSearchParams.endDate,
          guests: newSearchParams.guests,
          budget: newSearchParams.budget
        };

        // Only include parameters that have values
        Object.keys(apiParams).forEach(key => {
          if (apiParams[key] === '' || apiParams[key] === 0 || apiParams[key] === null) {
            delete apiParams[key];
          }
        });

        console.log('🔍 FIND VENUES DEBUG: Fetching venues with params:', apiParams);
        console.log('🔍 FIND VENUES DEBUG: Original search params from URL:', newSearchParams);
        console.log('🔍 FIND VENUES DEBUG: About to call getVenues API...');



        // Fetch venues with filters (or all venues if no filters)
        const venueResults = await getVenues(Object.keys(apiParams).length > 0 ? apiParams : undefined);
        console.log('🔍 FIND VENUES DEBUG: Received venues from API:', venueResults.length, 'venues');
        console.log('🔍 FIND VENUES DEBUG: First 3 venue names:', venueResults.slice(0, 3).map(v => v.title));

        // Debug venue metadata
        console.log('🔍 VENUE METADATA DEBUG:', venueResults.slice(0, 5).map(v => ({
          title: v.title,
          location: v.location,
          isExactMatch: v.isExactMatch,
          isSuggestion: v.isSuggestion,
          distanceKm: v.distanceKm
        })));

        setVenues(venueResults);
      } catch (error) {
        console.error('Error fetching venues:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVenues();
  }, [location.search, location.state]);

  return (
    <div className="pt-32 px-4 sm:px-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          {searchParams.location ?
            `Venues in ${searchParams.location}` :
            'All NSW Venues'
          }
        </h1>

        {/* Display search parameters if any */}
        {(searchParams.location || searchParams.startDate || searchParams.guests > 0 || searchParams.budget > 0) && (
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h2 className="text-lg font-medium text-purple-800 mb-2">Search Parameters:</h2>
            <ul className="text-sm text-purple-700">
              {searchParams.location && <li>Location: {searchParams.location}</li>}
              {searchParams.startDate && searchParams.endDate && (
                <li>Dates: {new Date(searchParams.startDate).toLocaleDateString()} - {new Date(searchParams.endDate).toLocaleDateString()}</li>
              )}
              {searchParams.guests > 0 && <li>Guests: {searchParams.guests}</li>}
              {searchParams.budget > 0 && <li>Budget: ${searchParams.budget}</li>}
            </ul>
          </div>
        )}

        {/* Smart Venue Search */}
        <div className="mb-8">
          <SmartVenueSearch
            onVenuesFound={handleSmartVenuesFound}
            initialQuery={initialSmartQuery}
          />
        </div>

        <SearchBar />

        <div className="mt-8 flex justify-end">
          <VenueSort onSortChange={handleSortChange} />
        </div>

        <div className="mt-8 grid grid-cols-12 gap-8">
          <div className="col-span-12 lg:col-span-3">
            <VenueFilters
              onFilterChange={handleFilterChange}
              selectedLocation={selectedLocation}
            />
          </div>

          <div className="col-span-12 lg:col-span-9">
            {/* Smart Search Results */}
            {showSmartResults && (
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">
                    Smart Search Results
                    <span className="text-sm font-normal text-gray-600 ml-2">
                      ({smartSearchResults.length} venue{smartSearchResults.length !== 1 ? 's' : ''} found)
                    </span>
                  </h2>
                  <button
                    onClick={() => {
                      setShowSmartResults(false);
                      setSmartSearchResults([]);
                    }}
                    className="text-sm text-purple-600 hover:text-purple-700"
                  >
                    Clear Smart Search
                  </button>
                </div>
                {smartSearchResults.length > 0 ? (
                  <VenueGrid venues={smartSearchResults.map(venue => ({
                    id: venue.id.toString(),
                    title: venue.name,
                    description: venue.description,
                    location: venue.location,
                    price: venue.pricePerHour,
                    capacity: venue.capacity.max,
                    rating: 4.5 + Math.random() * 0.5, // Mock rating
                    reviews: Math.floor(Math.random() * 100) + 20, // Mock reviews
                    images: [venue.image || `https://images.unsplash.com/photo-1519167758481-83f29c1fe8ea?w=400&h=300&fit=crop`],
                    amenities: venue.amenities.slice(0, 3),
                    eventTypes: venue.eventTypes.slice(0, 2),
                    host: {
                      id: `h${venue.id}`,
                      name: `Host ${venue.id}`,
                      image: `https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200`,
                      rating: 4.5 + Math.random() * 0.5
                    }
                  }))} />
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-medium text-gray-900 mb-2">No venues found</h3>
                    <p className="text-gray-600">
                      Try adjusting your search terms or browse our featured venues below.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Regular Search Results */}
            {!showSmartResults && (
              <>
                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                  </div>
                ) : venues.length > 0 ? (
                  <>
                    <div className="mb-6">
                      <p className="text-gray-600">
                        {searchParams.location || searchParams.startDate || searchParams.guests > 0 || searchParams.budget > 0
                          ? `Found ${venues.length} venue${venues.length !== 1 ? 's' : ''} matching your criteria`
                          : `Showing all ${venues.length} venues available in NSW`
                        }
                      </p>
                    </div>
                    <VenueGrid venues={venues} />
                  </>
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-medium text-gray-900 mb-2">No venues found</h3>
                    <p className="text-gray-600">
                      Try adjusting your search criteria or budget to see more results.
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Venue Assistant Chat */}
      <VenueAssistant />
    </div>
  );
}