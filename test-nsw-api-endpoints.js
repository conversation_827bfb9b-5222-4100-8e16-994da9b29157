/**
 * Test NSW API Endpoints
 * 
 * This script tests the NSW Spatial Services API for LGA detection
 * and the NSW Planning Portal API for zoning detection.
 */

// Test coordinates (Sydney CBD)
const testCoordinates = {
  sydney: { lat: -33.8688, lng: 151.2093 },
  parramatta: { lat: -33.8150, lng: 151.0011 },
  newcastle: { lat: -32.9283, lng: 151.7817 },
  wollongong: { lat: -34.4278, lng: 150.8931 },
  epping: { lat: -33.7728, lng: 151.0824 }
};

// Test the NSW Spatial Services API for LGA detection
async function testLGADetection() {
  console.log('Testing NSW Spatial Services API for LGA detection...');
  
  for (const [location, coords] of Object.entries(testCoordinates)) {
    console.log(`\nTesting ${location} (${coords.lat}, ${coords.lng})...`);
    
    // Test layer 8 (current implementation)
    await testLGAEndpoint(
      'https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query',
      coords.lat,
      coords.lng,
      'Layer 8'
    );
    
    // Test alternative layer 1
    await testLGAEndpoint(
      'https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/1/query',
      coords.lat,
      coords.lng,
      'Layer 1'
    );
  }
}

async function testLGAEndpoint(url, lat, lng, label) {
  try {
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`${label} response:`, data.features && data.features.length > 0 
        ? `Found: ${JSON.stringify(data.features[0].attributes)}` 
        : 'No features found');
    } else {
      console.error(`${label} error:`, response.statusText);
    }
  } catch (error) {
    console.error(`${label} exception:`, error.message);
  }
}

// Test the NSW Planning Portal API for zoning detection
async function testZoningDetection() {
  console.log('\n\nTesting NSW Planning Portal API for zoning detection...');
  
  for (const [location, coords] of Object.entries(testCoordinates)) {
    console.log(`\nTesting ${location} (${coords.lat}, ${coords.lng})...`);
    
    // Test current implementation
    await testZoningEndpoint(
      'https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query',
      coords.lat,
      coords.lng,
      'Current endpoint'
    );
    
    // Test alternative endpoint
    await testZoningEndpoint(
      'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer',
      coords.lat,
      coords.lng,
      'WFS endpoint',
      true
    );
  }
}

async function testZoningEndpoint(url, lat, lng, label, isWFS = false) {
  try {
    if (isWFS) {
      // WFS request is more complex, just log that we would need to test it separately
      console.log(`${label}: WFS endpoints require special handling, skipping direct test`);
      return;
    }
    
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`${label} response:`, data.features && data.features.length > 0 
        ? `Found: ${JSON.stringify(data.features[0].attributes)}` 
        : 'No features found');
    } else {
      console.error(`${label} error:`, response.statusText);
    }
  } catch (error) {
    console.error(`${label} exception:`, error.message);
  }
}

// Run the tests
async function runTests() {
  await testLGADetection();
  await testZoningDetection();
}

runTests().catch(console.error);
