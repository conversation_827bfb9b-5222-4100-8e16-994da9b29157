/**
 * Centralized Authentication Manager
 *
 * This module provides a single source of truth for authentication state
 * and resolves conflicts between Clerk and Supabase authentication systems.
 */

import { supabase } from './supabase-client';

// Authentication state
interface AuthState {
  isAuthenticated: boolean;
  user: any | null;
  clerkToken: string | null;
  isLoading: boolean;
  error: string | null;
}

// Global auth state
let authState: AuthState = {
  isAuthenticated: false,
  user: null,
  clerkToken: null,
  isLoading: true,
  error: null
};

// Event listeners for auth state changes
const authListeners: Array<(state: AuthState) => void> = [];

// Token cache to prevent unnecessary API calls
let cachedToken: string | null = null;
let tokenExpiryTime: number = 0;

/**
 * Subscribe to authentication state changes
 */
export function subscribeToAuth(callback: (state: AuthState) => void): () => void {
  authListeners.push(callback);

  // Immediately call with current state
  callback(authState);

  // Return unsubscribe function
  return () => {
    const index = authListeners.indexOf(callback);
    if (index > -1) {
      authListeners.splice(index, 1);
    }
  };
}

/**
 * Notify all listeners of auth state changes
 */
function notifyAuthListeners() {
  authListeners.forEach(callback => callback(authState));
}

/**
 * Update authentication state
 */
function updateAuthState(updates: Partial<AuthState>) {
  authState = { ...authState, ...updates };
  notifyAuthListeners();
}

/**
 * Check if we're in development mode
 */
function isDevelopmentMode(): boolean {
  return typeof window !== 'undefined' &&
         (window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          window.location.hostname.includes('local') ||
          window.location.hostname.includes('netlify'));
}

/**
 * Get fresh Clerk token with caching (NEW NATIVE INTEGRATION)
 */
async function getClerkToken(): Promise<string | null> {
  const now = Date.now();

  // Return cached token if still valid
  if (cachedToken && now < tokenExpiryTime) {
    return cachedToken;
  }

  try {
    // In development mode, return mock token
    if (isDevelopmentMode()) {
      console.log('🔧 Development mode: Using mock Clerk token');
      return 'mock-clerk-token';
    }

    // Check if Clerk is available
    if (typeof window === 'undefined' || !window.Clerk) {
      console.log('❌ Clerk not available on window object');
      return null;
    }

    // Wait for Clerk to be ready with timeout
    if (!window.Clerk.loaded) {
      console.log('⏳ Waiting for Clerk to load...');
      let attempts = 0;
      while (!window.Clerk.loaded && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (!window.Clerk.loaded) {
        console.log('❌ Clerk failed to load after 5 seconds');
        return null;
      }
    }

    // Check if user is signed in
    if (!window.Clerk.user) {
      console.log('❌ No Clerk user found');
      return null;
    }

    console.log('✅ Clerk user found, getting token with NEW NATIVE INTEGRATION...');

    // NEW: Use native integration - no template needed!
    // This is the new recommended way as of April 2025
    const token = await window.Clerk.session?.getToken();

    if (token) {
      console.log('✅ Clerk token obtained successfully (native integration)');
      // Cache the token for 4 minutes (expires in 5)
      cachedToken = token;
      tokenExpiryTime = now + 4 * 60 * 1000;
    } else {
      console.log('❌ Failed to get Clerk token');
    }

    return token;
  } catch (error) {
    console.error('Error getting Clerk token:', error);
    return null;
  }
}

/**
 * Convert Clerk user ID to deterministic UUID
 */
function clerkIdToUuid(clerkId: string): string {
  // Remove 'user_' prefix if present
  const cleanId = clerkId.replace(/^user_/, '');

  // Create a deterministic UUID from the Clerk ID
  // Using a simple hash-based approach
  let hash = 0;
  for (let i = 0; i < cleanId.length; i++) {
    const char = cleanId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Convert to positive number and pad
  const positiveHash = Math.abs(hash).toString(16).padStart(8, '0');

  // Create UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  const uuid = [
    positiveHash.slice(0, 8),
    positiveHash.slice(0, 4),
    '4' + positiveHash.slice(1, 4), // Version 4 UUID
    '8' + positiveHash.slice(1, 4), // Variant bits
    cleanId.slice(-12).padStart(12, '0')
  ].join('-');

  return uuid;
}

/**
 * Create a custom JWT token with UUID sub claim
 */
async function createCustomSupabaseToken(clerkToken: string): Promise<string | null> {
  try {
    // Decode the Clerk JWT to get user info
    const payload = JSON.parse(atob(clerkToken.split('.')[1]));

    // Create new payload with UUID sub
    const customPayload = {
      ...payload,
      sub: clerkIdToUuid(payload.sub), // Convert Clerk ID to UUID
      iss: 'https://fxqoowlruissctsgbljk.supabase.co/auth/v1',
      aud: 'authenticated',
      role: 'authenticated'
    };

    // For now, we'll use the original token but this shows the concept
    // In production, you'd need to re-sign with Supabase JWT secret
    console.log('Custom payload would be:', customPayload);
    return clerkToken; // Return original for now

  } catch (error) {
    console.error('Error creating custom token:', error);
    return null;
  }
}

/**
 * Set Supabase session with Clerk token
 */
async function setSupabaseSession(token: string): Promise<boolean> {
  try {
    // Try to create a custom token with UUID sub
    const customToken = await createCustomSupabaseToken(token);

    await supabase.auth.setSession({
      access_token: customToken || token,
      refresh_token: '' // Clerk handles refresh
    });

    console.log('✅ Supabase session set with Clerk token');
    return true;
  } catch (error) {
    console.error('❌ Error setting Supabase session:', error);
    return false;
  }
}

/**
 * Clear Supabase session
 */
async function clearSupabaseSession(): Promise<void> {
  try {
    await supabase.auth.signOut();
    console.log('✅ Supabase session cleared');
  } catch (error) {
    console.error('❌ Error clearing Supabase session:', error);
  }
}

/**
 * Initialize authentication system
 */
export async function initializeAuth(): Promise<void> {
  updateAuthState({ isLoading: true, error: null });

  try {
    // In development mode, set a mock authenticated state for testing
    if (isDevelopmentMode()) {
      console.log('🔧 Development mode: Using mock authentication');

      // Create a mock user for development
      const mockUser = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        user_metadata: {
          first_name: 'Dev',
          last_name: 'User',
          email: '<EMAIL>'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      updateAuthState({
        isAuthenticated: true,
        user: mockUser,
        clerkToken: 'mock-dev-token',
        isLoading: false,
        error: null
      });
      return;
    }

    // Production mode: Check Clerk authentication
    console.log('🚀 Production mode: Checking Clerk authentication...');

    // Wait for Clerk to be ready
    if (typeof window !== 'undefined' && window.Clerk) {
      // Wait for Clerk to load
      let attempts = 0;
      while (!window.Clerk.loaded && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (window.Clerk.loaded && window.Clerk.user) {
        console.log('✅ Clerk user found:', window.Clerk.user);

        // Create user object from Clerk data
        const clerkUser = window.Clerk.user;
        const user = {
          id: clerkUser.id,
          email: clerkUser.primaryEmailAddress?.emailAddress || '',
          user_metadata: {
            first_name: clerkUser.firstName || '',
            last_name: clerkUser.lastName || '',
            email: clerkUser.primaryEmailAddress?.emailAddress || '',
            full_name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim()
          },
          created_at: new Date(clerkUser.createdAt).toISOString(),
          updated_at: new Date(clerkUser.updatedAt).toISOString()
        };

        // Try to get Clerk token
        const token = await getClerkToken();

        updateAuthState({
          isAuthenticated: true,
          user,
          clerkToken: token,
          isLoading: false,
          error: null
        });

        console.log('✅ Authentication successful with Clerk user');
        return;
      }
    }

    // No valid authentication found
    console.log('❌ No Clerk authentication found');
    updateAuthState({
      isAuthenticated: false,
      user: null,
      clerkToken: null,
      isLoading: false,
      error: null
    });

  } catch (error) {
    console.error('Error initializing auth:', error);
    updateAuthState({
      isAuthenticated: false,
      user: null,
      clerkToken: null,
      isLoading: false,
      error: error instanceof Error ? error.message : 'Authentication error'
    });
  }
}

/**
 * Sign out from all systems
 */
export async function signOut(): Promise<void> {
  updateAuthState({ isLoading: true });

  try {
    // Clear cached token
    cachedToken = null;
    tokenExpiryTime = 0;

    // In development mode, just clear the mock state
    if (isDevelopmentMode()) {
      console.log('🔧 Development mode: Clearing mock authentication');
      updateAuthState({
        isAuthenticated: false,
        user: null,
        clerkToken: null,
        isLoading: false,
        error: null
      });
      return;
    }

    // Production mode: Sign out from Clerk
    if (window.Clerk?.signOut) {
      await window.Clerk.signOut();
    }

    // Clear Supabase session
    await clearSupabaseSession();

    // Update state
    updateAuthState({
      isAuthenticated: false,
      user: null,
      clerkToken: null,
      isLoading: false,
      error: null
    });

    console.log('✅ Successfully signed out from all systems');
  } catch (error) {
    console.error('❌ Error during sign out:', error);
    updateAuthState({
      isLoading: false,
      error: error instanceof Error ? error.message : 'Sign out error'
    });
  }
}

/**
 * Refresh authentication state
 */
export async function refreshAuth(): Promise<void> {
  // Clear cached token to force refresh
  cachedToken = null;
  tokenExpiryTime = 0;

  // Re-initialize
  await initializeAuth();
}

/**
 * Get current authentication state
 */
export function getAuthState(): AuthState {
  return authState;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  return authState.isAuthenticated;
}

/**
 * Get current user
 */
export function getCurrentUser(): any | null {
  return authState.user;
}

/**
 * Get authenticated Supabase client
 */
export async function getAuthenticatedSupabaseClient() {
  const token = await getClerkToken();

  if (token && token !== authState.clerkToken) {
    // Token has changed, update session
    await setSupabaseSession(token);
    updateAuthState({ clerkToken: token });
  }

  return supabase;
}

// Global Clerk type declaration (UPDATED FOR NATIVE INTEGRATION)
declare global {
  interface Window {
    Clerk?: {
      loaded: boolean;
      user: any;
      session?: {
        // NEW: Native integration uses getToken() without template parameter
        getToken: () => Promise<string | null>;
        // OLD: Keep for backward compatibility if needed
        // getToken: (options?: { template?: string }) => Promise<string | null>;
      };
      signOut: () => Promise<void>;
    };
  }
}

// Auto-initialize when module loads (with error handling)
if (typeof window !== 'undefined') {
  // Initialize after a longer delay to ensure Clerk is loaded
  setTimeout(async () => {
    try {
      await initializeAuth();
    } catch (error) {
      console.error('Failed to auto-initialize auth:', error);
      // Set a safe default state if initialization fails
      updateAuthState({
        isAuthenticated: false,
        user: null,
        clerkToken: null,
        isLoading: false,
        error: null
      });
    }
  }, 1000); // Increased delay to 1 second

  // Also listen for Clerk events to re-initialize when user signs in
  window.addEventListener('clerk:loaded', async () => {
    console.log('🔔 Clerk loaded event detected, re-initializing auth...');
    await initializeAuth();
  });

  // Listen for custom auth events
  window.addEventListener('auth_complete', async (event) => {
    console.log('🔔 Auth complete event detected:', event.detail);
    await initializeAuth();
  });

  // Periodically check auth state (fallback)
  setInterval(async () => {
    if (typeof window !== 'undefined' && window.Clerk?.loaded && window.Clerk?.user && !authState.isAuthenticated) {
      console.log('🔄 Periodic auth check: Found signed-in user, updating state...');
      await initializeAuth();
    }
  }, 3000); // Check every 3 seconds
}
