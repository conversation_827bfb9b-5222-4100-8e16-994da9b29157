/**
 * Official Clerk + Supabase Integration Hook
 * 
 * This hook provides the new native integration between Clerk and Supa<PERSON>
 * as recommended by the official documentation (April 2025).
 * 
 * Usage:
 * ```tsx
 * import { useClerkSupabase } from '@/hooks/useClerkSupabase';
 * 
 * function MyComponent() {
 *   const { supabase, isReady } = useClerkSupabase();
 *   
 *   useEffect(() => {
 *     if (isReady) {
 *       // Use supabase client with Clerk authentication
 *       supabase.from('tasks').select('*').then(console.log);
 *     }
 *   }, [isReady, supabase]);
 * }
 * ```
 */

import { useAuth } from '../providers/AuthProvider';
import { useMemo } from 'react';
import { supabase } from '../lib/supabase-client';

export function useClerkSupabase() {
  const { user, session, isLoading, isAuthenticated } = useAuth();

  // Return the standard Supabase client (already configured with auth)
  const supabaseClient = useMemo(() => {
    return supabase;
  }, []);

  // Determine if the integration is ready to use
  const isReady = !isLoading && isAuthenticated && !!user && !!session;

  return {
    supabase: supabaseClient,
    isReady,
    isAuthenticated,
    user,
    session,
    isLoading
  };
}

/**
 * Hook for server-side usage (API routes, etc.)
 * This is for when you need to create a Supabase client on the server
 */
export function useServerClerkSupabase() {
  const supabaseClient = useMemo(() => {
    return supabase;
  }, []);

  return { supabase: supabaseClient };
}
