/**
 * Official Clerk + Supabase Integration Hook
 * 
 * This hook provides the new native integration between <PERSON> and <PERSON>pa<PERSON>
 * as recommended by the official documentation (April 2025).
 * 
 * Usage:
 * ```tsx
 * import { useClerkSupabase } from '@/hooks/useClerkSupabase';
 * 
 * function MyComponent() {
 *   const { supabase, isReady } = useClerkSupabase();
 *   
 *   useEffect(() => {
 *     if (isReady) {
 *       // Use supabase client with Clerk authentication
 *       supabase.from('tasks').select('*').then(console.log);
 *     }
 *   }, [isReady, supabase]);
 * }
 * ```
 */

import { useSession, useUser } from '@clerk/clerk-react';
import { useMemo } from 'react';
import { createClerkSupabaseClient } from '../lib/clerk-supabase-official';

export function useClerkSupabase() {
  const { session, isLoaded: sessionLoaded } = useSession();
  const { user, isLoaded: userLoaded } = useUser();

  // Create the Supabase client with Clerk authentication
  const supabase = useMemo(() => {
    if (!session) return null;
    return createClerkSupabaseClient(session);
  }, [session]);

  // Determine if the integration is ready to use
  const isReady = sessionLoaded && userLoaded && !!session && !!user && !!supabase;
  const isAuthenticated = !!user && !!session;

  return {
    supabase,
    isReady,
    isAuthenticated,
    user,
    session,
    isLoading: !sessionLoaded || !userLoaded
  };
}

/**
 * Hook for server-side usage (API routes, etc.)
 * This is for when you need to create a Supabase client on the server
 */
export function useServerClerkSupabase(getToken: () => Promise<string | null>) {
  const supabase = useMemo(() => {
    return createClerkSupabaseClient({ getToken });
  }, [getToken]);

  return { supabase };
}
