/**
 * Official Clerk + Supabase Integration Hook
 * 
 * This hook provides the new native integration between <PERSON> and <PERSON>pa<PERSON>
 * as recommended by the official documentation (April 2025).
 * 
 * Usage:
 * ```tsx
 * import { useClerkSupabase } from '@/hooks/useClerkSupabase';
 * 
 * function MyComponent() {
 *   const { supabase, isReady } = useClerkSupabase();
 *   
 *   useEffect(() => {
 *     if (isReady) {
 *       // Use supabase client with Clerk authentication
 *       supabase.from('tasks').select('*').then(console.log);
 *     }
 *   }, [isReady, supabase]);
 * }
 * ```
 */

import { useSession, useUser } from '@clerk/clerk-react';
import { useMemo } from 'react';
import { createClient } from '@supabase/supabase-js';

export function useClerkSupabase() {
  const { session, isLoaded: sessionLoaded } = useSession();
  const { user, isLoaded: userLoaded } = useUser();

  // Create a custom Supabase client that injects the <PERSON> session token (following official docs)
  const supabase = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return session?.getToken() ?? null;
        },
      },
    );
  }, [session]);

  // Determine if the integration is ready to use
  const isReady = sessionLoaded && userLoaded && !!session && !!user;
  const isAuthenticated = !!user && !!session;

  return {
    supabase,
    isReady,
    isAuthenticated,
    user,
    session,
    isLoading: !sessionLoaded || !userLoaded
  };
}

/**
 * Hook for server-side usage (API routes, etc.)
 * This is for when you need to create a Supabase client on the server
 */
export function useServerClerkSupabase(getToken: () => Promise<string | null>) {
  const supabase = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return await getToken();
        },
      },
    );
  }, [getToken]);

  return { supabase };
}
